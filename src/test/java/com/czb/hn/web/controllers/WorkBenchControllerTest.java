package com.czb.hn.web.controllers;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.enums.SubscriptionStatus;
import com.czb.hn.service.business.BillingService;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.user.UserLoginRecordService;
import com.czb.hn.util.UserContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WorkBenchControllerTest {

    @Mock
    private ElasticsearchSearchService elasticsearchSearchService;

    @Mock
    private UserLoginRecordService userLoginRecordService;

    @Mock
    private BillingService billingService;

    @Mock
    private PlanService planService;

    @InjectMocks
    private WorkBenchController workBenchController;

    private static final String TEST_USER_ID = "testUser123";
    private static final String TEST_ENTERPRISE_ID = "testEnterprise456";
    private static final String TEST_ENTERPRISE_CODE = "testEnterpriseCode";

    @BeforeEach
    void setUp() {
        // Reset any static mocks before each test
    }

    @Test
    void testGetWorkbenchStats_Success() {
        // 准备测试数据
        LocalDateTime lastLoginTime = LocalDateTime.of(2024, 1, 15, 14, 30, 0);
        long sevenDayLoginCount = 5L;
        LocalDate expirationDate = LocalDate.of(2024, 12, 31);
        long planCount = 3L;

        // 创建模拟的企业订阅
        EnterpriseSubscription subscription = EnterpriseSubscription.builder()
                .enterpriseId(TEST_ENTERPRISE_ID)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(expirationDate)
                .status(SubscriptionStatus.ACTIVE)
                .build();

        // 创建模拟的方案列表
        List<PlanDTO> mockPlans = Arrays.asList(
                mock(PlanDTO.class),
                mock(PlanDTO.class),
                mock(PlanDTO.class));

        // 模拟静态方法调用
        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getCurrentUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseId).thenReturn(TEST_ENTERPRISE_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseCode).thenReturn(TEST_ENTERPRISE_CODE);

            // 模拟服务调用
            when(userLoginRecordService.getLastLoginTime(TEST_USER_ID)).thenReturn(lastLoginTime);
            when(userLoginRecordService.getLoginCountInDays(TEST_USER_ID, 7)).thenReturn(sevenDayLoginCount);
            when(billingService.getCurrentSubscription(TEST_ENTERPRISE_CODE)).thenReturn(Optional.of(subscription));
            when(planService.getPlansByEnterpriseId(TEST_ENTERPRISE_ID)).thenReturn(mockPlans);

            // 执行测试
            ResponseEntity<?> response = workBenchController.getWorkbenchStats();

            // 验证结果
            assertEquals(HttpStatus.OK, response.getStatusCode());
            assertNotNull(response.getBody());

            // 验证服务方法被正确调用
            verify(userLoginRecordService).getLastLoginTime(TEST_USER_ID);
            verify(userLoginRecordService).getLoginCountInDays(TEST_USER_ID, 7);
            verify(billingService).getCurrentSubscription(TEST_ENTERPRISE_CODE);
            verify(planService).getPlansByEnterpriseId(TEST_ENTERPRISE_ID);
        }
    }

    @Test
    void testGetWorkbenchStats_UserNotLoggedIn() {
        // 模拟用户未登录的情况
        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getCurrentUserId).thenReturn(null);

            // 执行测试
            ResponseEntity<?> response = workBenchController.getWorkbenchStats();

            // 验证结果
            assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());

            // 验证没有调用其他服务
            verifyNoInteractions(userLoginRecordService, billingService, planService);
        }
    }

    @Test
    void testGetWorkbenchStats_EnterpriseNotFound() {
        // 模拟企业信息未找到的情况
        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getCurrentUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseId).thenReturn(null);
            mockedUserContext.when(UserContext::getCurrentEnterpriseCode).thenReturn(null);

            // 执行测试
            ResponseEntity<?> response = workBenchController.getWorkbenchStats();

            // 验证结果
            assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());

            // 验证没有调用其他服务
            verifyNoInteractions(userLoginRecordService, billingService, planService);
        }
    }

    @Test
    void testGetWorkbenchStats_NoSubscription() {
        // 准备测试数据（无订阅信息）
        LocalDateTime lastLoginTime = LocalDateTime.of(2024, 1, 15, 14, 30, 0);
        long sevenDayLoginCount = 5L;
        long planCount = 2L;

        List<PlanDTO> mockPlans = Arrays.asList(mock(PlanDTO.class), mock(PlanDTO.class));

        // 模拟静态方法调用
        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getCurrentUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseId).thenReturn(TEST_ENTERPRISE_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseCode).thenReturn(TEST_ENTERPRISE_CODE);

            // 模拟服务调用（无订阅信息）
            when(userLoginRecordService.getLastLoginTime(TEST_USER_ID)).thenReturn(lastLoginTime);
            when(userLoginRecordService.getLoginCountInDays(TEST_USER_ID, 7)).thenReturn(sevenDayLoginCount);
            when(billingService.getCurrentSubscription(TEST_ENTERPRISE_CODE)).thenReturn(Optional.empty());
            when(planService.getPlansByEnterpriseId(TEST_ENTERPRISE_ID)).thenReturn(mockPlans);

            // 执行测试
            ResponseEntity<?> response = workBenchController.getWorkbenchStats();

            // 验证结果
            assertEquals(HttpStatus.OK, response.getStatusCode());
            assertNotNull(response.getBody());

            // 验证服务方法被正确调用
            verify(userLoginRecordService).getLastLoginTime(TEST_USER_ID);
            verify(userLoginRecordService).getLoginCountInDays(TEST_USER_ID, 7);
            verify(billingService).getCurrentSubscription(TEST_ENTERPRISE_CODE);
            verify(planService).getPlansByEnterpriseId(TEST_ENTERPRISE_ID);
        }
    }

    @Test
    void testGetWorkbenchStats_ServiceException() {
        // 模拟服务异常
        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getCurrentUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseId).thenReturn(TEST_ENTERPRISE_ID);
            mockedUserContext.when(UserContext::getCurrentEnterpriseCode).thenReturn(TEST_ENTERPRISE_CODE);

            // 模拟服务抛出异常
            when(userLoginRecordService.getLastLoginTime(TEST_USER_ID))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // 执行测试
            ResponseEntity<?> response = workBenchController.getWorkbenchStats();

            // 验证结果
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

            // 验证服务方法被调用
            verify(userLoginRecordService).getLastLoginTime(TEST_USER_ID);
        }
    }
}
