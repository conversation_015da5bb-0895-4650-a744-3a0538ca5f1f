package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for AlertResult entity
 * Verifies that the entity works correctly with all new fields
 */
@DisplayName("AlertResult Entity Tests")
class AlertResultEntityTest {

    @Test
    @DisplayName("Should create AlertResult with all new fields using builder")
    void testCreateAlertResultWithAllFields() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        
        // When
        AlertResult alertResult = AlertResult.builder()
                .enterpriseId("enterprise-123")
                .planId(100L)
                .configurationId(1L)
                .title("Test Alert Title")
                .content("Test alert content")
                .involvedKeywords("[{\"keyword\":\"test\",\"count\":1}]")
                .informationSensitivityType(InformationSensitivityType.SENSITIVE)
                .sourceType(SourceType.WEIBO)
                .contentType(ContentType.TEXT)
                .contentMatchType(ContentMatchType.MAIN_TXT)
                .contentCategory(ContentCategory.ORIGINAL)
                .mediaLevel(MediaLevel.NATIONAL)
                .warningLevel(AlertResult.WarningLevel.SEVERE)
                .source("微博")
                .provincial("北京")
                .warningTime(now)
                .similarArticleCount(5)
                .originalContentId("content-123")
                .processingVersion("1.0")
                .ruleSnapshot("{\"test\":\"snapshot\"}")
                .createdBy("SYSTEM")
                .build();

        // Then
        assertNotNull(alertResult);
        assertEquals("enterprise-123", alertResult.getEnterpriseId());
        assertEquals(100L, alertResult.getPlanId());
        assertEquals(1L, alertResult.getConfigurationId());
        assertEquals("Test Alert Title", alertResult.getTitle());
        assertEquals("Test alert content", alertResult.getContent());
        assertEquals("[{\"keyword\":\"test\",\"count\":1}]", alertResult.getInvolvedKeywords());
        assertEquals(InformationSensitivityType.SENSITIVE, alertResult.getInformationSensitivityType());
        assertEquals(SourceType.WEIBO, alertResult.getSourceType());
        assertEquals(ContentType.TEXT, alertResult.getContentType());
        assertEquals(ContentMatchType.MAIN_TXT, alertResult.getContentMatchType());
        assertEquals(ContentCategory.ORIGINAL, alertResult.getContentCategory());
        assertEquals(MediaLevel.NATIONAL, alertResult.getMediaLevel());
        assertEquals(AlertResult.WarningLevel.SEVERE, alertResult.getWarningLevel());
        assertEquals("微博", alertResult.getSource());
        assertEquals("北京", alertResult.getProvincial());
        assertEquals(now, alertResult.getWarningTime());
        assertEquals(5, alertResult.getSimilarArticleCount());
        assertEquals("content-123", alertResult.getOriginalContentId());
        assertEquals("1.0", alertResult.getProcessingVersion());
        assertEquals("{\"test\":\"snapshot\"}", alertResult.getRuleSnapshot());
        assertEquals("SYSTEM", alertResult.getCreatedBy());
    }

    @Test
    @DisplayName("Should use default values for builder defaults")
    void testBuilderDefaults() {
        // When
        AlertResult alertResult = AlertResult.builder()
                .enterpriseId("enterprise-123")
                .configurationId(1L)
                .title("Test Title")
                .content("Test Content")
                .involvedKeywords("[]")
                .source("Test Source")
                .provincial("Test Province")
                .warningTime(LocalDateTime.now())
                .originalContentId("content-123")
                .build();

        // Then
        assertNotNull(alertResult);
        assertEquals(InformationSensitivityType.NEUTRAL, alertResult.getInformationSensitivityType());
        assertEquals(SourceType.WEIBO, alertResult.getSourceType());
        assertEquals(ContentType.TEXT, alertResult.getContentType());
        assertEquals(ContentMatchType.MAIN_TXT, alertResult.getContentMatchType());
        assertEquals(ContentCategory.ORIGINAL, alertResult.getContentCategory());
        assertEquals(MediaLevel.SMALL_MEDIUM, alertResult.getMediaLevel());
        assertEquals(0, alertResult.getSimilarArticleCount());
        assertEquals("1.0", alertResult.getProcessingVersion());
        assertNull(alertResult.getWarningLevel()); // Default is null
    }

    @Test
    @DisplayName("Should handle enum conversions correctly")
    void testEnumConversions() {
        // Given
        AlertResult alertResult = AlertResult.builder()
                .enterpriseId("enterprise-123")
                .configurationId(1L)
                .title("Test Title")
                .content("Test Content")
                .involvedKeywords("[]")
                .sourceType(SourceType.fromString("wx"))
                .contentType(ContentType.fromInteger(2))
                .mediaLevel(MediaLevel.fromChineseValue("省级"))
                .informationSensitivityType(InformationSensitivityType.fromInteger(1))
                .contentCategory(ContentCategory.fromInteger(2))
                .source("Test Source")
                .provincial("Test Province")
                .warningTime(LocalDateTime.now())
                .originalContentId("content-123")
                .build();

        // Then
        assertEquals(SourceType.WECHAT, alertResult.getSourceType());
        assertEquals(ContentType.IMAGE, alertResult.getContentType());
        assertEquals(MediaLevel.PROVINCIAL, alertResult.getMediaLevel());
        assertEquals(InformationSensitivityType.SENSITIVE, alertResult.getInformationSensitivityType());
        assertEquals(ContentCategory.FORWARD, alertResult.getContentCategory());
    }

    @Test
    @DisplayName("Should handle warning level enum correctly")
    void testWarningLevelEnum() {
        // Test all warning levels
        for (AlertResult.WarningLevel level : AlertResult.WarningLevel.values()) {
            AlertResult alertResult = AlertResult.builder()
                    .enterpriseId("enterprise-123")
                    .configurationId(1L)
                    .title("Test Title")
                    .content("Test Content")
                    .involvedKeywords("[]")
                    .warningLevel(level)
                    .source("Test Source")
                    .provincial("Test Province")
                    .warningTime(LocalDateTime.now())
                    .originalContentId("content-123")
                    .build();

            assertEquals(level, alertResult.getWarningLevel());
            assertNotNull(level.getDescription());
        }
    }

    @Test
    @DisplayName("Should validate required fields are not null")
    void testRequiredFields() {
        // Test that builder works with minimal required fields
        AlertResult alertResult = AlertResult.builder()
                .enterpriseId("enterprise-123")
                .configurationId(1L)
                .title("Test Title")
                .content("Test Content")
                .involvedKeywords("[]")
                .source("Test Source")
                .provincial("Test Province")
                .warningTime(LocalDateTime.now())
                .originalContentId("content-123")
                .build();

        assertNotNull(alertResult.getEnterpriseId());
        assertNotNull(alertResult.getConfigurationId());
        assertNotNull(alertResult.getTitle());
        assertNotNull(alertResult.getContent());
        assertNotNull(alertResult.getInvolvedKeywords());
        assertNotNull(alertResult.getSource());
        assertNotNull(alertResult.getProvincial());
        assertNotNull(alertResult.getWarningTime());
        assertNotNull(alertResult.getOriginalContentId());
    }
}
