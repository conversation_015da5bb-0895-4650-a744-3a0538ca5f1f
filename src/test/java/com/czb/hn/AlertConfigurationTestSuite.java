package com.czb.hn;

import com.czb.hn.dto.alert.AlertConfigurationDtoValidationTest;
import com.czb.hn.service.business.impl.AlertConfigurationSentimentMonitoringTest;
import com.czb.hn.service.business.impl.AlertConfigurationServiceImplTest;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * Test suite for all Alert Configuration module tests
 */
@Suite
@SelectClasses({
        AlertConfigurationServiceImplTest.class,
        AlertConfigurationSentimentMonitoringTest.class,
        AlertConfigurationDtoValidationTest.class
})
public class AlertConfigurationTestSuite {
    // Test suite class - no implementation needed
}
