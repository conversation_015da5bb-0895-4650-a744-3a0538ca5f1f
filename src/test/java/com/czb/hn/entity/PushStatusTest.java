package com.czb.hn.entity;

import com.czb.hn.enums.PushStatus;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PushStatus enum
 */
class PushStatusTest {

    @Test
    void testGetDescription() {
        assertEquals("成功", PushStatus.SUCCESS.getDescription());
        assertEquals("失败", PushStatus.FAILURE.getDescription());
    }

    @Test
    void testFromString_ValidValues() {
        assertEquals(PushStatus.SUCCESS, PushStatus.fromString("SUCCESS"));
        assertEquals(PushStatus.FAILURE, PushStatus.fromString("FAILURE"));

        // Test case insensitive
        assertEquals(PushStatus.SUCCESS, PushStatus.fromString("success"));
        assertEquals(PushStatus.FAILURE, PushStatus.fromString("failure"));
    }

    @Test
    void testFromString_InvalidValue() {
        // Smart conversion returns default value instead of throwing exception
        PushStatus result = PushStatus.fromString("INVALID");
        assertEquals(PushStatus.FAILURE, result);
    }

    @Test
    void testFromString_NullValue() {
        // Smart conversion returns default value instead of throwing exception
        PushStatus result = PushStatus.fromString(null);
        assertEquals(PushStatus.FAILURE, result);
    }

    @Test
    void testFromString_BlankValue() {
        // Smart conversion returns default value instead of throwing exception
        PushStatus result = PushStatus.fromString("   ");
        assertEquals(PushStatus.FAILURE, result);
    }

    @Test
    void testIsSuccess() {
        assertTrue(PushStatus.SUCCESS.isSuccess());
        assertFalse(PushStatus.FAILURE.isSuccess());
    }

    @Test
    void testIsFailure() {
        assertFalse(PushStatus.SUCCESS.isFailure());
        assertTrue(PushStatus.FAILURE.isFailure());
    }

    @Test
    void testEnumValues() {
        PushStatus[] values = PushStatus.values();
        assertEquals(3, values.length);

        assertTrue(java.util.Arrays.asList(values).contains(PushStatus.SUCCESS));
        assertTrue(java.util.Arrays.asList(values).contains(PushStatus.FAILURE));
    }

    @Test
    void testValueOf() {
        assertEquals(PushStatus.SUCCESS, PushStatus.valueOf("SUCCESS"));
        assertEquals(PushStatus.FAILURE, PushStatus.valueOf("FAILURE"));
    }
}
