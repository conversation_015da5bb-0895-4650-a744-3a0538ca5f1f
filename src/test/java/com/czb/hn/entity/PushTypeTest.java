package com.czb.hn.entity;

import com.czb.hn.enums.PushType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PushType enum
 */
class PushTypeTest {

    @Test
    void testGetDescription() {
        assertEquals("邮件", PushType.EMAIL.getDescription());
        assertEquals("短信", PushType.SMS.getDescription());
    }

    @Test
    void testFromString_ValidValues() {
        assertEquals(PushType.EMAIL, PushType.fromString("EMAIL"));
        assertEquals(PushType.SMS, PushType.fromString("SMS"));

        // Test case insensitive
        assertEquals(PushType.EMAIL, PushType.fromString("email"));
        assertEquals(PushType.SMS, PushType.fromString("sms"));
    }

    @Test
    void testFromString_InvalidValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("INVALID");
        assertNull(result); // 无效值返回 null
    }

    @Test
    void testFromString_NullValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString(null);
        assertNull(result); // null 值返回 null
    }

    @Test
    void testFromString_BlankValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("   ");
        assertNull(result); // 空白值返回 null
    }

    @Test
    void testFromString_EmptyValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("");
        assertNull(result); // 空字符串返回 null
    }

    @Test
    void testEnumValues() {
        PushType[] values = PushType.values();
        assertEquals(2, values.length); // 现在只有 EMAIL 和 SMS

        assertTrue(java.util.Arrays.asList(values).contains(PushType.EMAIL));
        assertTrue(java.util.Arrays.asList(values).contains(PushType.SMS));
    }

    @Test
    void testValueOf() {
        assertEquals(PushType.EMAIL, PushType.valueOf("EMAIL"));
        assertEquals(PushType.SMS, PushType.valueOf("SMS"));
    }
}
