package com.czb.hn.entity;

import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AlertPushDetail entity
 */
class AlertPushDetailTest {

    private AlertPushDetail pushDetail;

    @BeforeEach
    void setUp() {
        pushDetail = AlertPushDetail.builder()
                .id(1L)
                .planId(1L)
                .alertConfigSnapshotId(1L)
                .enterpriseId("enterprise123")
                .accountInfo("<EMAIL>")
                .pushType(PushType.EMAIL)
                .pushStatus(PushStatus.SUCCESS)
                .pushTime(LocalDateTime.now())
                .retryCount(0)
                .build();
    }

    @Test
    void testIsSuccessful() {
        // Given
        pushDetail.setPushStatus(PushStatus.SUCCESS);

        // When & Then
        assertTrue(pushDetail.isSuccessful());
        assertFalse(pushDetail.isFailed());
    }

    @Test
    void testIsFailed() {
        // Given
        pushDetail.setPushStatus(PushStatus.FAILURE);

        // When & Then
        assertTrue(pushDetail.isFailed());
        assertFalse(pushDetail.isSuccessful());
    }

    @Test
    void testCanRetry_FailedWithLowRetryCount() {
        // Given
        pushDetail.setPushStatus(PushStatus.FAILURE);
        pushDetail.setRetryCount(1);

        // When & Then
        assertTrue(pushDetail.canRetry());
    }

    @Test
    void testCanRetry_FailedWithMaxRetryCount() {
        // Given
        pushDetail.setPushStatus(PushStatus.FAILURE);
        pushDetail.setRetryCount(3);

        // When & Then
        assertFalse(pushDetail.canRetry());
    }

    @Test
    void testCanRetry_Successful() {
        // Given
        pushDetail.setPushStatus(PushStatus.SUCCESS);
        pushDetail.setRetryCount(1);

        // When & Then
        assertFalse(pushDetail.canRetry());
    }

    @Test
    void testIncrementRetryCount() {
        // Given
        int initialRetryCount = pushDetail.getRetryCount();
        LocalDateTime beforeIncrement = LocalDateTime.now();

        // When
        pushDetail.incrementRetryCount();

        // Then
        assertEquals(initialRetryCount + 1, pushDetail.getRetryCount());
        assertNotNull(pushDetail.getLastRetryTime());
        assertTrue(pushDetail.getLastRetryTime().isAfter(beforeIncrement) ||
                pushDetail.getLastRetryTime().equals(beforeIncrement));
    }

    @Test
    void testBuilder() {
        // When
        AlertPushDetail built = AlertPushDetail.builder()
                .planId(2L)
                .alertConfigSnapshotId(2L)
                .enterpriseId("enterprise456")
                .accountInfo("***********")
                .pushType(PushType.SMS)
                .pushStatus(PushStatus.FAILURE)
                .pushTime(LocalDateTime.now())
                .errorMessage("SMS sending failed")
                .retryCount(1)
                .build();

        // Then
        assertEquals(2L, built.getPlanId());
        assertEquals("enterprise456", built.getEnterpriseId());
        assertEquals("***********", built.getAccountInfo());
        assertEquals(PushType.SMS, built.getPushType());
        assertEquals(PushStatus.FAILURE, built.getPushStatus());
        assertEquals("SMS sending failed", built.getErrorMessage());
        assertEquals(1, built.getRetryCount());
    }

    @Test
    void testDefaultValues() {
        // When
        AlertPushDetail defaultPushDetail = AlertPushDetail.builder().build();

        // Then
        assertEquals(0, defaultPushDetail.getRetryCount());
    }
}
