package com.czb.hn.dto.alert;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AlertPushDetailCreateDto
 */
class AlertPushDetailCreateDtoTest {

    @Test
    void testValidDto_Success() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "<EMAIL>", // accountInfo
                    PushType.EMAIL, // pushType
                    PushStatus.SUCCESS, // pushStatus
                    pushTime, // pushTime
                    null, // errorMessage
                    "邮件发送成功", // pushDetails
                    "admin"); // createdBy
        });
    }

    @Test
    void testValidDto_FailureWithErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "<EMAIL>", // accountInfo
                    PushType.EMAIL, // pushType
                    PushStatus.FAILURE, // pushStatus
                    pushTime, // pushTime
                    "邮件发送失败", // errorMessage
                    "邮件发送失败详情", // pushDetails
                    "admin"); // createdBy
        });
    }

    @Test
    void testInvalidDto_FailureWithoutErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "<EMAIL>", // accountInfo
                    PushType.EMAIL, // pushType
                    PushStatus.FAILURE, // pushStatus
                    pushTime, // pushTime
                    null, // errorMessage - No error message for failure
                    "邮件发送失败详情", // pushDetails
                    "admin"); // createdBy
        });

        assertEquals("Error message is required when push status is FAILURE", exception.getMessage());
    }

    @Test
    void testInvalidDto_FailureWithBlankErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "<EMAIL>", // accountInfo
                    PushType.EMAIL, // pushType
                    PushStatus.FAILURE, // pushStatus
                    pushTime, // pushTime
                    "   ", // errorMessage - Blank error message for failure
                    "邮件发送失败详情", // pushDetails
                    "admin"); // createdBy
        });

        assertEquals("Error message is required when push status is FAILURE", exception.getMessage());
    }

    @Test
    void testInvalidDto_InvalidEmailFormat() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "invalid-email", // accountInfo - Invalid email format
                    PushType.EMAIL, // pushType
                    PushStatus.SUCCESS, // pushStatus
                    pushTime, // pushTime
                    null, // errorMessage
                    "邮件发送成功", // pushDetails
                    "admin"); // createdBy
        });

        assertTrue(exception.getMessage().contains("Invalid email address format"));
    }

    @Test
    void testInvalidDto_InvalidPhoneFormat() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "12345", // accountInfo - Invalid phone format
                    PushType.SMS, // pushType
                    PushStatus.SUCCESS, // pushStatus
                    pushTime, // pushTime
                    null, // errorMessage
                    "短信发送成功", // pushDetails
                    "admin"); // createdBy
        });

        assertTrue(exception.getMessage().contains("Invalid phone number format"));
    }

    @Test
    void testInvalidDto_FuturePushTime() {
        // Given
        LocalDateTime futurePushTime = LocalDateTime.now().plusHours(1);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                    1L, // alertId
                    1L, // planId
                    1L, // alertConfigSnapshotId
                    "enterprise123", // enterpriseId
                    "<EMAIL>", // accountInfo
                    PushType.EMAIL, // pushType
                    PushStatus.SUCCESS, // pushStatus
                    futurePushTime, // pushTime
                    null, // errorMessage
                    "邮件发送成功", // pushDetails
                    "admin"); // createdBy
        });

        assertEquals("Push time cannot be in the future", exception.getMessage());
    }

    @Test
    void testValidDto_ValidEmailFormats() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        String[] validEmails = {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        };

        // When & Then
        for (String email : validEmails) {
            assertDoesNotThrow(() -> {
                new AlertPushDetailCreateDto(
                        1L, // alertId
                        1L, // planId
                        1L, // alertConfigSnapshotId
                        "enterprise123", // enterpriseId
                        email, // accountInfo
                        PushType.EMAIL, // pushType
                        PushStatus.SUCCESS, // pushStatus
                        pushTime, // pushTime
                        null, // errorMessage
                        "邮件发送成功", // pushDetails
                        "admin"); // createdBy
            }, "Should accept valid email: " + email);
        }
    }

    @Test
    void testValidDto_ValidPhoneFormats() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        String[] validPhones = {
                "***********",
                "***********",
                "***********",
                "***********"
        };

        // When & Then
        for (String phone : validPhones) {
            assertDoesNotThrow(() -> {
                new AlertPushDetailCreateDto(
                        1L, // alertId
                        1L, // planId
                        1L, // alertConfigSnapshotId
                        "enterprise123", // enterpriseId
                        phone, // accountInfo
                        PushType.SMS, // pushType
                        PushStatus.SUCCESS, // pushStatus
                        pushTime, // pushTime
                        null, // errorMessage
                        "短信发送成功", // pushDetails
                        "admin"); // createdBy
            }, "Should accept valid phone: " + phone);
        }
    }

    @Test
    void testRecordMethods() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        AlertPushDetailCreateDto dto = new AlertPushDetailCreateDto(
                1L, // alertId
                1L, // planId
                1L, // alertConfigSnapshotId
                "enterprise123", // enterpriseId
                "<EMAIL>", // accountInfo
                PushType.EMAIL, // pushType
                PushStatus.SUCCESS, // pushStatus
                pushTime, // pushTime
                null, // errorMessage
                "邮件发送成功", // pushDetails
                "admin"); // createdBy

        // When & Then
        assertEquals(1L, dto.alertId());
        assertEquals("enterprise123", dto.enterpriseId());
        assertEquals("<EMAIL>", dto.accountInfo());
        assertEquals(PushType.EMAIL, dto.pushType());
        assertEquals(PushStatus.SUCCESS, dto.pushStatus());
        assertEquals(pushTime, dto.pushTime());
        assertNull(dto.errorMessage());
        assertEquals("邮件发送成功", dto.pushDetails());
        assertEquals("admin", dto.createdBy());
    }
}
