package com.czb.hn.dto.alert;

import com.czb.hn.dto.alert.config.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for DTO validation in alert configuration DTOs
 */
public class AlertConfigurationDtoValidationTest {

        private AlertKeywordsDto validAlertKeywords;
        private ContentSettingsDto validContentSettings;
        private ThresholdSettingsDto validThresholdSettings;
        private LevelSettingsDto validLevelSettings;
        private ReceptionSettingsDto validReceptionSettings;

        @BeforeEach
        void setUp() {
                // Setup valid DTO objects for testing
                validAlertKeywords = new AlertKeywordsDto(
                                List.of("urgent", "critical", "breaking"),
                                "Test alert keywords");

                validContentSettings = new ContentSettingsDto(
                                "1", // sensitivityType
                                List.of("wb", "wx"), // sourceTypes
                                List.of("1", "2"), // contentTypes
                                "1", // resultDisplay
                                List.of("-1", "1"), // weiboVerification
                                List.of("央级", "省级"), // sourceLevel
                                "1" // contentCategory
                );

                ThresholdSettingsDto.ThresholdConditionDto interactionThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 100L);

                validThresholdSettings = new ThresholdSettingsDto(
                                "OR", interactionThreshold, null, null, null, List.of());

                LevelSettingsDto.ThresholdRangeDto generalRange = new LevelSettingsDto.ThresholdRangeDto(0L, 99L);
                LevelSettingsDto.ThresholdRangeDto mediumRange = new LevelSettingsDto.ThresholdRangeDto(100L, 999L);
                LevelSettingsDto.ThresholdRangeDto severeRange = new LevelSettingsDto.ThresholdRangeDto(1000L, null);

                LevelSettingsDto.LevelThresholdsDto interactionLevels = new LevelSettingsDto.LevelThresholdsDto(
                                generalRange, mediumRange, severeRange);

                validLevelSettings = new LevelSettingsDto(
                                null, interactionLevels, null, null, null);

                ReceptionSettingsDto.TimePeriodDto timePeriod = new ReceptionSettingsDto.TimePeriodDto("09:00",
                                "18:00");

                ReceptionSettingsDto.EmailRecipientDto emailRecipient = new ReceptionSettingsDto.EmailRecipientDto(
                                "Test User", "<EMAIL>");

                ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(true,
                                List.of(emailRecipient));

                ReceptionSettingsDto.SmsRecipientDto smsRecipient = new ReceptionSettingsDto.SmsRecipientDto(
                                "Test User", "13800138000");

                ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(false,
                                List.of(smsRecipient));

                ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                                emailConfig, smsConfig);

                validReceptionSettings = new ReceptionSettingsDto(
                                "DAILY", 30, timePeriod, false, receptionMethods, false);
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Valid input should pass")
        void createDto_ValidInput_Success() {
                // When & Then
                assertDoesNotThrow(() -> new AlertConfigurationCreateDto(
                                "Valid Configuration",
                                "Valid Description",
                                1L,
                                "enterprise123",
                                true,
                                validAlertKeywords,
                                validContentSettings,
                                validThresholdSettings,
                                validLevelSettings,
                                validReceptionSettings,
                                "testUser",
                                "Valid creation"));
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Null name should throw exception")
        void createDto_NullName_ThrowsException() {
                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new AlertConfigurationCreateDto(
                                                null,
                                                "Valid Description",
                                                1L,
                                                "enterprise123",
                                                true,
                                                validAlertKeywords,
                                                validContentSettings,
                                                validThresholdSettings,
                                                validLevelSettings,
                                                validReceptionSettings,
                                                "testUser",
                                                "Valid creation"));
                assertEquals("Configuration name cannot be null or blank", exception.getMessage());
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Blank name should throw exception")
        void createDto_BlankName_ThrowsException() {
                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new AlertConfigurationCreateDto(
                                                "   ",
                                                "Valid Description",
                                                1L,
                                                "enterprise123",
                                                true,
                                                validAlertKeywords,
                                                validContentSettings,
                                                validThresholdSettings,
                                                validLevelSettings,
                                                validReceptionSettings,
                                                "testUser",
                                                "Valid creation"));
                assertEquals("Configuration name cannot be null or blank", exception.getMessage());
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Long name should throw exception")
        void createDto_LongName_ThrowsException() {
                // Given
                String longName = "a".repeat(256);

                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new AlertConfigurationCreateDto(
                                                longName,
                                                "Valid Description",
                                                1L,
                                                "enterprise123",
                                                true,
                                                validAlertKeywords,
                                                validContentSettings,
                                                validThresholdSettings,
                                                validLevelSettings,
                                                validReceptionSettings,
                                                "testUser",
                                                "Valid creation"));
                assertEquals("Configuration name cannot exceed 255 characters", exception.getMessage());
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Null enterprise ID should throw exception")
        void createDto_NullEnterpriseId_ThrowsException() {
                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new AlertConfigurationCreateDto(
                                                "Valid Name",
                                                "Valid Description",
                                                1L,
                                                null,
                                                true,
                                                validAlertKeywords,
                                                validContentSettings,
                                                validThresholdSettings,
                                                validLevelSettings,
                                                validReceptionSettings,
                                                "testUser",
                                                "Valid creation"));
                assertEquals("Enterprise ID cannot be null or blank", exception.getMessage());
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Optional fields can be null")
        void createDto_OptionalFieldsNull_Success() {
                // When & Then - contentSettings, thresholdSettings, and levelSettings are now
                // optional
                assertDoesNotThrow(() -> new AlertConfigurationCreateDto(
                                "Valid Configuration",
                                "Valid Description",
                                1L,
                                "enterprise123",
                                true,
                                validAlertKeywords,
                                null, // contentSettings is optional
                                null, // thresholdSettings is optional
                                null, // levelSettings is optional
                                validReceptionSettings,
                                "testUser",
                                "Valid creation"));
        }

        @Test
        @DisplayName("AlertConfigurationCreateDto - Null reception settings should throw exception")
        void createDto_NullReceptionSettings_ThrowsException() {
                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new AlertConfigurationCreateDto(
                                                "Valid Name",
                                                "Valid Description",
                                                1L,
                                                "enterprise123",
                                                true,
                                                validAlertKeywords,
                                                validContentSettings,
                                                validThresholdSettings,
                                                validLevelSettings,
                                                null, // receptionSettings is required
                                                "testUser",
                                                "Valid creation"));
                assertEquals("Reception settings cannot be null", exception.getMessage());
        }

        @Test
        @DisplayName("EmailConfigDto - Enabled email with no recipients should throw exception")
        void emailConfigDto_EnabledWithNoRecipients_ThrowsException() {
                // When & Then - This should fail at EmailConfigDto level
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new ReceptionSettingsDto.EmailConfigDto(true, List.of()));
                assertEquals("Email recipients cannot be empty when email is enabled", exception.getMessage());
        }

        @Test
        @DisplayName("SmsConfigDto - Enabled SMS with no recipients should throw exception")
        void smsConfigDto_EnabledWithNoRecipients_ThrowsException() {
                // When & Then - This should fail at SmsConfigDto level
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> new ReceptionSettingsDto.SmsConfigDto(true, List.of()));
                assertEquals("SMS recipients cannot be empty when SMS is enabled", exception.getMessage());
        }

        @Test
        @DisplayName("AlertConfigurationUpdateDto - Valid update with null reception settings should pass")
        void updateDto_NullReceptionSettings_Success() {
                // When & Then - receptionSettings is optional in update DTO
                assertDoesNotThrow(() -> new AlertConfigurationUpdateDto(
                                "Updated Name",
                                "Updated Description",
                                2L,
                                false,
                                null,
                                null,
                                null,
                                null,
                                null, // receptionSettings is optional in updates
                                "testUser",
                                "Update reason"));
        }

        @Test
        @DisplayName("AlertConfigurationUpdateDto - Valid update with valid reception settings should pass")
        void updateDto_ValidReceptionSettings_Success() {
                // When & Then
                assertDoesNotThrow(() -> new AlertConfigurationUpdateDto(
                                "Updated Name",
                                "Updated Description",
                                2L,
                                false,
                                null,
                                null,
                                null,
                                null,
                                validReceptionSettings, // Valid reception settings
                                "testUser",
                                "Update reason"));
        }

}
