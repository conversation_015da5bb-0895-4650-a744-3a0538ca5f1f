package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 最终枚举标准化系统测试
 * 
 * 验证：
 * 1. 所有枚举值都写到value中，不使用序号
 * 2. 所有业务逻辑和存储逻辑均使用value值
 * 3. 根据接口文档的实际值类型确定存储类型
 */
@DisplayName("最终枚举标准化系统测试")
class FinalEnumStandardTest {

        @Test
        @DisplayName("Integer类型枚举 - InformationSensitivityType")
        void testIntegerTypeEnum() {
                // 验证getValue()返回API值，不是序号
                assertEquals(Integer.valueOf(1), InformationSensitivityType.SENSITIVE.getValue());
                assertEquals(Integer.valueOf(2), InformationSensitivityType.NON_SENSITIVE.getValue());
                assertEquals(Integer.valueOf(3), InformationSensitivityType.NEUTRAL.getValue());

                // 验证描述
                assertEquals("敏感", InformationSensitivityType.SENSITIVE.getDescription());
                assertEquals("非敏感", InformationSensitivityType.NON_SENSITIVE.getDescription());
                assertEquals("中性", InformationSensitivityType.NEUTRAL.getDescription());

                // 验证智能转换 - 按API值转换
                Optional<InformationSensitivityType> result1 = StandardEnum
                                .smartConvert(InformationSensitivityType.class, 1);
                assertTrue(result1.isPresent());
                assertEquals(InformationSensitivityType.SENSITIVE, result1.get());

                // 验证智能转换 - 按描述转换
                Optional<InformationSensitivityType> result2 = StandardEnum
                                .smartConvert(InformationSensitivityType.class, "敏感");
                assertTrue(result2.isPresent());
                assertEquals(InformationSensitivityType.SENSITIVE, result2.get());

                // 验证智能转换 - 按字符串数值转换
                Optional<InformationSensitivityType> result3 = StandardEnum
                                .smartConvert(InformationSensitivityType.class, "2");
                assertTrue(result3.isPresent());
                assertEquals(InformationSensitivityType.NON_SENSITIVE, result3.get());
        }

        @Test
        @DisplayName("String类型枚举 - SourceType")
        void testStringTypeEnum() {
                // 验证getValue()返回API代码
                assertEquals("hdlt", SourceType.INTERACTIVE_FORUMS.getValue());
                assertEquals("wb", SourceType.WEIBO.getValue());
                assertEquals("wx", SourceType.WECHAT.getValue());
                assertEquals("zmtapp", SourceType.MOBILE_APPS.getValue());
                assertEquals("sp", SourceType.VIDEO.getValue());
                assertEquals("szb", SourceType.DIGITAL_NEWSPAPERS.getValue());
                assertEquals("wz", SourceType.WEBSITES.getValue());

                // 验证描述
                assertEquals("互动论坛", SourceType.INTERACTIVE_FORUMS.getDescription());
                assertEquals("微博", SourceType.WEIBO.getDescription());
                assertEquals("微信", SourceType.WECHAT.getDescription());

                // 验证智能转换 - 按API代码转换
                Optional<SourceType> result1 = StandardEnum.smartConvert(SourceType.class, "wb");
                assertTrue(result1.isPresent());
                assertEquals(SourceType.WEIBO, result1.get());

                // 验证智能转换 - 按描述转换
                Optional<SourceType> result2 = StandardEnum.smartConvert(SourceType.class, "微博");
                assertTrue(result2.isPresent());
                assertEquals(SourceType.WEIBO, result2.get());

                // 验证智能转换 - 按枚举名转换
                Optional<SourceType> result3 = StandardEnum.smartConvert(SourceType.class, "WEIBO");
                assertTrue(result3.isPresent());
                assertEquals(SourceType.WEIBO, result3.get());
        }

        @Test
        @DisplayName("智能转换 - 带默认值")
        void testSmartConvertWithDefault() {
                // Integer类型 - 有效值
                InformationSensitivityType result1 = StandardEnum.smartConvertWithDefault(
                                InformationSensitivityType.class, 1, InformationSensitivityType.NEUTRAL);
                assertEquals(InformationSensitivityType.SENSITIVE, result1);

                // Integer类型 - 无效值返回默认值
                InformationSensitivityType result2 = StandardEnum.smartConvertWithDefault(
                                InformationSensitivityType.class, "invalid", InformationSensitivityType.NEUTRAL);
                assertEquals(InformationSensitivityType.NEUTRAL, result2);

                // String类型 - 有效值
                SourceType result3 = StandardEnum.smartConvertWithDefault(SourceType.class, "wb", SourceType.WEBSITES);
                assertEquals(SourceType.WEIBO, result3);

                // String类型 - 无效值返回默认值
                SourceType result4 = StandardEnum.smartConvertWithDefault(SourceType.class, "invalid",
                                SourceType.WEBSITES);
                assertEquals(SourceType.WEBSITES, result4);
        }

        @Test
        @DisplayName("验证不使用序号")
        void testNotUsingOrdinal() {
                // 验证getValue()不等于ordinal()
                assertNotEquals(InformationSensitivityType.SENSITIVE.ordinal(),
                                InformationSensitivityType.SENSITIVE.getValue());
                assertNotEquals(InformationSensitivityType.NON_SENSITIVE.ordinal(),
                                InformationSensitivityType.NON_SENSITIVE.getValue());
                assertNotEquals(InformationSensitivityType.NEUTRAL.ordinal(),
                                InformationSensitivityType.NEUTRAL.getValue());

                // 验证序号从0开始，但getValue()使用API值
                assertEquals(0, InformationSensitivityType.SENSITIVE.ordinal());
                assertEquals(1, InformationSensitivityType.NON_SENSITIVE.ordinal());
                assertEquals(2, InformationSensitivityType.NEUTRAL.ordinal());

                // 但getValue()返回的是API值
                assertEquals(Integer.valueOf(1), InformationSensitivityType.SENSITIVE.getValue());
                assertEquals(Integer.valueOf(2), InformationSensitivityType.NON_SENSITIVE.getValue());
                assertEquals(Integer.valueOf(3), InformationSensitivityType.NEUTRAL.getValue());
        }

        @Test
        @DisplayName("业务逻辑使用value值")
        void testBusinessLogicUsesValue() {
                // 模拟数据库存储 - 使用getValue()
                Integer storedSensitivity = InformationSensitivityType.SENSITIVE.getValue();
                String storedSource = SourceType.WEIBO.getValue();

                assertEquals(Integer.valueOf(1), storedSensitivity); // API值，不是序号
                assertEquals("wb", storedSource); // API代码

                // 模拟从数据库读取 - 使用getValue()匹配
                InformationSensitivityType retrievedSensitivity = StandardEnum.smartConvertWithDefault(
                                InformationSensitivityType.class, storedSensitivity,
                                InformationSensitivityType.NEUTRAL);
                SourceType retrievedSource = StandardEnum.smartConvertWithDefault(SourceType.class, storedSource,
                                SourceType.WEBSITES);

                assertEquals(InformationSensitivityType.SENSITIVE, retrievedSensitivity);
                assertEquals(SourceType.WEIBO, retrievedSource);
        }

        @Test
        @DisplayName("API兼容性验证")
        void testApiCompatibility() {
                // 验证API值直接对应
                // sensitivityType: 1=敏感, 2=非敏感, 3=中性
                assertEquals(Integer.valueOf(1), InformationSensitivityType.SENSITIVE.getValue());
                assertEquals(Integer.valueOf(2), InformationSensitivityType.NON_SENSITIVE.getValue());
                assertEquals(Integer.valueOf(3), InformationSensitivityType.NEUTRAL.getValue());

                // newOriginType: hdlt, wb, wx, zmtapp, sp, szb, wz
                assertEquals("hdlt", SourceType.INTERACTIVE_FORUMS.getValue());
                assertEquals("wb", SourceType.WEIBO.getValue());
                assertEquals("wx", SourceType.WECHAT.getValue());
                assertEquals("zmtapp", SourceType.MOBILE_APPS.getValue());
                assertEquals("sp", SourceType.VIDEO.getValue());
                assertEquals("szb", SourceType.DIGITAL_NEWSPAPERS.getValue());
                assertEquals("wz", SourceType.WEBSITES.getValue());

                // emotion: 中性, 喜悦, 悲伤, 愤怒, 惊奇, 恐惧
                assertEquals("中性", EmotionType.NEUTRAL.getValue());
                assertEquals("喜悦", EmotionType.JOY.getValue());
                assertEquals("悲伤", EmotionType.SADNESS.getValue());

                // isOriginal: 1=原创, 2=转发
                assertEquals(Integer.valueOf(1), ContentCategory.ORIGINAL.getValue());
                assertEquals(Integer.valueOf(2), ContentCategory.FORWARD.getValue());

                // contentTypes: "1"=文本, "2"=图片, "3"=短链, "4"=视频
                assertEquals("1", ContentType.TEXT.getValue());
                assertEquals("2", ContentType.IMAGE.getValue());
                assertEquals("3", ContentType.SHORT_URL.getValue());
                assertEquals("4", ContentType.VIDEO.getValue());

                // matchInfo.type: "1"=关键词, "2"=用户
                assertEquals("1", MatchType.KEYWORD.getValue());
                assertEquals("2", MatchType.USER.getValue());

                // isNormarlData: 1=正常, 2=噪音
                assertEquals(Integer.valueOf(1), ResultViewType.NORMAL.getValue());
                assertEquals(Integer.valueOf(2), ResultViewType.NOISE.getValue());

                // originLevel: 央级, 省级, 地市, 重点, 中小, 企业商业
                assertEquals("央级", MediaLevel.NATIONAL.getValue());
                assertEquals("省级", MediaLevel.PROVINCIAL.getValue());
                assertEquals("地市", MediaLevel.MUNICIPAL.getValue());

                // verifiedType: "-1"=普通, "0"=橙V, "1"=蓝V, "200"=达人, "600"=金V
                assertEquals("-1", UserVerificationType.REGULAR.getValue());
                assertEquals("0", UserVerificationType.ORANGE_V.getValue());
                assertEquals("1", UserVerificationType.BLUE_V.getValue());
                assertEquals("200", UserVerificationType.EXPERT.getValue());
                assertEquals("600", UserVerificationType.GOLD_V.getValue());
        }

        @Test
        @DisplayName("类型安全验证")
        void testTypeSafety() {
                // 验证Integer类型枚举
                assertTrue(InformationSensitivityType.SENSITIVE.getValue() instanceof Integer);
                assertTrue(InformationSensitivityType.NON_SENSITIVE.getValue() instanceof Integer);
                assertTrue(InformationSensitivityType.NEUTRAL.getValue() instanceof Integer);

                // 验证String类型枚举
                assertTrue(SourceType.WEIBO.getValue() instanceof String);
                assertTrue(SourceType.WECHAT.getValue() instanceof String);
                assertTrue(SourceType.WEBSITES.getValue() instanceof String);

                // 验证描述都是String类型
                assertTrue(InformationSensitivityType.SENSITIVE.getDescription() instanceof String);
                assertTrue(SourceType.WEIBO.getDescription() instanceof String);
        }
}
