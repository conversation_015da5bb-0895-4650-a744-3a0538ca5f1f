package com.czb.hn.service.cleaner;

import com.czb.hn.service.cleaner.impl.SinaNewsCleanerServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 编码安全性测试
 * 专门测试解决数据库编码错误的方案
 */
public class EncodingSafetyTest {

    private SinaNewsCleanerServiceImpl cleanerService;

    @BeforeEach
    void setUp() {
        cleanerService = new SinaNewsCleanerServiceImpl();
    }

    @Test
    void testEncodingSafetyWithProblematicData() throws Exception {
        Method ensureEncodingSafetyMethod = SinaNewsCleanerServiceImpl.class
                .getDeclaredMethod("ensureEncodingSafety", String.class);
        ensureEncodingSafetyMethod.setAccessible(true);

        // 测试用户提供的具体问题数据
        String problematicHtml = "<div><table><thead><tr><th>长信内需成长混合C(015768)  基金公开信息</th></tr></thead>" +
                "<tbody><tr><td><strong>流水号</strong></td><td>4570659</td></tr>" +
                "<tr><td><strong>基金代码</strong></td><td>015768</td></tr>" +
                "<tr><td><strong>客户</strong></td><td>测试客户数据&nbsp;更多信息😊</td></tr></tbody></table></div>";

        String result = (String) ensureEncodingSafetyMethod.invoke(cleanerService, problematicHtml);

        assertNotNull(result);

        // 验证所有内容都被完整保留（不进行清洗）
        assertTrue(result.contains("<div>"));
        assertTrue(result.contains("<table>"));
        assertTrue(result.contains("<thead>"));
        assertTrue(result.contains("<tbody>"));
        assertTrue(result.contains("<tr>"));
        assertTrue(result.contains("<td>"));
        assertTrue(result.contains("<th>"));
        assertTrue(result.contains("<strong>"));
        assertTrue(result.contains("</td>"));
        assertTrue(result.contains("</tr>"));
        assertTrue(result.contains("</tbody>"));
        assertTrue(result.contains("</thead>"));
        assertTrue(result.contains("</table>"));
        assertTrue(result.contains("</div>"));

        // 验证中文内容被保留
        assertTrue(result.contains("长信内需成长混合C"));
        assertTrue(result.contains("基金公开信息"));
        assertTrue(result.contains("流水号"));
        assertTrue(result.contains("客户"));
        assertTrue(result.contains("测试客户数据"));

        // 验证HTML实体和表情符号被保留
        assertTrue(result.contains("&nbsp;"));
        assertTrue(result.contains("更多信息"));
        assertTrue(result.contains("😊"));

        // 验证编码安全性 - 应该能正常转换为UTF-8
        byte[] utf8Bytes = result.getBytes("UTF-8");
        String utf8String = new String(utf8Bytes, "UTF-8");
        assertEquals(result, utf8String);

        System.out.println("编码安全处理后的完整内容: " + result);
        System.out.println("UTF-8字节长度: " + utf8Bytes.length);
        System.out.println("内容完全保留，只做编码安全处理");
    }

    @Test
    void testEncodingSafetyWithSpecialCharacters() throws Exception {
        Method ensureEncodingSafetyMethod = SinaNewsCleanerServiceImpl.class
                .getDeclaredMethod("ensureEncodingSafety", String.class);
        ensureEncodingSafetyMethod.setAccessible(true);

        // 测试包含可能导致编码问题的特殊字符
        String textWithSpecialChars = "客户信息：张三\u0000李四\u0001王五\u007F";
        String result = (String) ensureEncodingSafetyMethod.invoke(cleanerService, textWithSpecialChars);

        assertNotNull(result);
        assertTrue(result.contains("客户信息"));
        assertTrue(result.contains("张三"));
        assertTrue(result.contains("李四"));
        assertTrue(result.contains("王五"));

        // 控制字符应该被替换
        assertFalse(result.contains("\u0000"));
        assertFalse(result.contains("\u0001"));
        assertFalse(result.contains("\u007F"));

        // 验证UTF-8编码安全性
        assertDoesNotThrow(() -> {
            byte[] bytes = result.getBytes("UTF-8");
            new String(bytes, "UTF-8");
        });

        System.out.println("特殊字符处理结果: " + result);
    }

    @Test
    void testEncodingSafetyWithEmptyAndNull() throws Exception {
        Method ensureEncodingSafetyMethod = SinaNewsCleanerServiceImpl.class
                .getDeclaredMethod("ensureEncodingSafety", String.class);
        ensureEncodingSafetyMethod.setAccessible(true);

        // 测试null
        String nullResult = (String) ensureEncodingSafetyMethod.invoke(cleanerService, (String) null);
        assertNull(nullResult);

        // 测试空字符串
        String emptyResult = (String) ensureEncodingSafetyMethod.invoke(cleanerService, "");
        assertEquals("", emptyResult);

        // 测试空白字符串
        String blankResult = (String) ensureEncodingSafetyMethod.invoke(cleanerService, "   ");
        assertEquals("   ", blankResult);
    }
}
