package com.czb.hn.service.collector;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试时间解析功能
 * 验证多种时间格式的解析是否正常工作
 */
@DisplayName("DateTime Parsing Tests")
class DateTimeParsingTest {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter ISO_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    /**
     * 解析时间字符串，支持多种格式
     * 支持格式：
     * 1. yyyy-MM-dd HH:mm:ss
     * 2. yyyy-MM-ddTHH:mm:ss (ISO 8601)
     * 
     * @param timeStr 时间字符串
     * @return 解析后的LocalDateTime，解析失败返回null
     */
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.isBlank()) {
            return null;
        }

        try {
            // 首先尝试标准格式 yyyy-MM-dd HH:mm:ss
            return LocalDateTime.parse(timeStr, DATE_FORMAT);
        } catch (Exception e1) {
            try {
                // 尝试ISO 8601格式 yyyy-MM-ddTHH:mm:ss
                return LocalDateTime.parse(timeStr, ISO_DATE_FORMAT);
            } catch (Exception e2) {
                try {
                    // 尝试使用默认的ISO格式解析器
                    return LocalDateTime.parse(timeStr);
                } catch (Exception e3) {
                    System.err.println("Failed to parse datetime '" + timeStr + "': " + e3.getMessage());
                    return null;
                }
            }
        }
    }

    @Test
    @DisplayName("Should parse standard format yyyy-MM-dd HH:mm:ss")
    void testParseStandardFormat() {
        String timeStr = "2025-07-02 11:25:16";
        LocalDateTime result = parseDateTime(timeStr);
        
        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should parse ISO 8601 format yyyy-MM-ddTHH:mm:ss")
    void testParseISOFormat() {
        String timeStr = "2025-07-02T11:25:16";
        LocalDateTime result = parseDateTime(timeStr);
        
        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should parse ISO 8601 format with milliseconds")
    void testParseISOFormatWithMilliseconds() {
        String timeStr = "2025-07-02T11:25:16.123";
        LocalDateTime result = parseDateTime(timeStr);
        
        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should return null for invalid format")
    void testParseInvalidFormat() {
        String timeStr = "invalid-date-format";
        LocalDateTime result = parseDateTime(timeStr);
        
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for null input")
    void testParseNullInput() {
        LocalDateTime result = parseDateTime(null);
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for blank input")
    void testParseBlankInput() {
        LocalDateTime result = parseDateTime("   ");
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle various ISO formats")
    void testParseVariousISOFormats() {
        // Test different ISO 8601 variations
        String[] testCases = {
            "2025-07-02T11:25:16",
            "2025-07-02T11:25:16.123",
            "2025-07-02T11:25:16.123456",
            "2025-12-31T23:59:59"
        };

        for (String timeStr : testCases) {
            LocalDateTime result = parseDateTime(timeStr);
            assertNotNull(result, "Failed to parse: " + timeStr);
        }
    }
}
