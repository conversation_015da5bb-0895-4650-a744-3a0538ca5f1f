package com.czb.hn.service.user;

import com.czb.hn.dto.user.UserLoginStatsDTO;
import com.czb.hn.jpa.securadar.entity.UserLoginRecord;
import com.czb.hn.jpa.securadar.repository.UserLoginRecordRepository;
import com.czb.hn.service.user.impl.UserLoginRecordServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * User Login Record Service Test
 * Tests for user login tracking and statistics functionality
 */
@ExtendWith(MockitoExtension.class)
class UserLoginRecordServiceTest {

    @Mock
    private UserLoginRecordRepository userLoginRecordRepository;

    @InjectMocks
    private UserLoginRecordServiceImpl userLoginRecordService;

    private UserLoginRecord testRecord;
    private final String testUserId = "test-user-123";
    private final String testUserName = "Test User";
    private final String testEnterpriseId = "enterprise-123";
    private final String testEnterpriseCode = "COMP001";

    @BeforeEach
    void setUp() {
        testRecord = new UserLoginRecord();
        testRecord.setId(1L);
        testRecord.setUserId(testUserId);
        testRecord.setUserName(testUserName);
        testRecord.setEnterpriseId(testEnterpriseId);
        testRecord.setEnterpriseCode(testEnterpriseCode);
        testRecord.setLoginTime(LocalDateTime.now());
        testRecord.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testRecordLogin() {
        // Given
        when(userLoginRecordRepository.save(any(UserLoginRecord.class))).thenReturn(testRecord);

        // When
        userLoginRecordService.recordLogin(testUserId, testUserName, testEnterpriseId, testEnterpriseCode);

        // Then
        verify(userLoginRecordRepository, times(1)).save(any(UserLoginRecord.class));
    }

    @Test
    void testGetLastLoginTime() {
        // Given
        when(userLoginRecordRepository.findTopByUserIdOrderByLoginTimeDesc(testUserId))
                .thenReturn(Optional.of(testRecord));

        // When
        LocalDateTime lastLoginTime = userLoginRecordService.getLastLoginTime(testUserId);

        // Then
        assertNotNull(lastLoginTime);
        assertEquals(testRecord.getLoginTime(), lastLoginTime);
        verify(userLoginRecordRepository, times(1)).findTopByUserIdOrderByLoginTimeDesc(testUserId);
    }

    @Test
    void testGetLastLoginTimeWhenNoRecord() {
        // Given
        when(userLoginRecordRepository.findTopByUserIdOrderByLoginTimeDesc(testUserId))
                .thenReturn(Optional.empty());

        // When
        LocalDateTime lastLoginTime = userLoginRecordService.getLastLoginTime(testUserId);

        // Then
        assertNull(lastLoginTime);
        verify(userLoginRecordRepository, times(1)).findTopByUserIdOrderByLoginTimeDesc(testUserId);
    }

    @Test
    void testGetLoginCountInDays() {
        // Given
        long expectedCount = 5L;
        when(userLoginRecordRepository.countByUserIdAndLoginTimeAfter(eq(testUserId), any(LocalDateTime.class)))
                .thenReturn(expectedCount);

        // When
        long actualCount = userLoginRecordService.getLoginCountInDays(testUserId, 7);

        // Then
        assertEquals(expectedCount, actualCount);
        verify(userLoginRecordRepository, times(1))
                .countByUserIdAndLoginTimeAfter(eq(testUserId), any(LocalDateTime.class));
    }

    @Test
    void testGetUserLoginStats() {
        // Given
        when(userLoginRecordRepository.findTopByUserIdOrderByLoginTimeDesc(testUserId))
                .thenReturn(Optional.of(testRecord));
        when(userLoginRecordRepository.countByUserIdAndLoginTimeAfter(eq(testUserId), any(LocalDateTime.class)))
                .thenReturn(3L);

        // When
        UserLoginStatsDTO stats = userLoginRecordService.getUserLoginStats(testUserId);

        // Then
        assertNotNull(stats);
        assertEquals(testUserId, stats.userId());
        assertEquals(testUserName, stats.userName());
        assertEquals(testEnterpriseId, stats.enterpriseId());
        assertEquals(testEnterpriseCode, stats.enterpriseCode());
        assertEquals(testRecord.getLoginTime(), stats.lastLoginTime());
        assertEquals(3L, stats.loginCountInSevenDays());
    }

    @Test
    void testGetUserLoginStatsWhenNoRecord() {
        // Given
        when(userLoginRecordRepository.findTopByUserIdOrderByLoginTimeDesc(testUserId))
                .thenReturn(Optional.empty());

        // When
        UserLoginStatsDTO stats = userLoginRecordService.getUserLoginStats(testUserId);

        // Then
        assertNull(stats);
        verify(userLoginRecordRepository, times(1)).findTopByUserIdOrderByLoginTimeDesc(testUserId);
    }

    @Test
    void testCleanupOldRecords() {
        // Given
        long oldRecordsCount = 100L;
        int deletedCount = 80;
        when(userLoginRecordRepository.countByLoginTimeBefore(any(LocalDateTime.class)))
                .thenReturn(oldRecordsCount);
        when(userLoginRecordRepository.deleteOldRecordsButKeepLatest(any(LocalDateTime.class)))
                .thenReturn(deletedCount);

        // When
        int result = userLoginRecordService.cleanupOldRecords();

        // Then
        assertEquals(deletedCount, result);
        verify(userLoginRecordRepository, times(1)).countByLoginTimeBefore(any(LocalDateTime.class));
        verify(userLoginRecordRepository, times(1)).deleteOldRecordsButKeepLatest(any(LocalDateTime.class));
    }
}
