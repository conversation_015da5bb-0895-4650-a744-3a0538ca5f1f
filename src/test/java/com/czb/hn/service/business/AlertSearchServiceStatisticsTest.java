package com.czb.hn.service.business;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.alert.AlertStatisticsDto;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.impl.AlertSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Alert Search Service Statistics Test
 * 测试预警统计功能
 */
@ExtendWith(MockitoExtension.class)
class AlertSearchServiceStatisticsTest {

    @Mock
    private AlertResultRepository alertResultRepository;

    @Mock
    private PlanService planService;

    @InjectMocks
    private AlertSearchServiceImpl alertSearchService;

    private Long planId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private PlanDTO mockPlanDto;

    @BeforeEach
    void setUp() {
        planId = 1L;
        startTime = LocalDateTime.of(2024, 1, 15, 10, 30, 0);
        endTime = LocalDateTime.of(2024, 1, 15, 18, 30, 0);
        
        mockPlanDto = new PlanDTO(
                1L,
                "华能资本舆情监控方案",
                "测试方案描述",
                "华能资本+投资|华能集团+金融",
                "广告|招聘",
                null, // enterprise
                LocalDateTime.now(),
                LocalDateTime.now()
        );
    }

    @Test
    void testGetAlertStatistics_Success() {
        // 准备测试数据
        Long totalCount = 156L;
        Long sensitiveCount = 42L;

        // 模拟依赖调用
        when(planService.getPlanById(planId)).thenReturn(mockPlanDto);
        when(alertResultRepository.countByPlanIdAndWarningTimeBetween(eq(planId), eq(startTime), eq(endTime)))
                .thenReturn(totalCount);
        when(alertResultRepository.countSensitiveByPlanIdAndWarningTimeBetween(eq(planId), eq(startTime), eq(endTime)))
                .thenReturn(sensitiveCount);

        // 执行测试
        AlertStatisticsDto result = alertSearchService.getAlertStatistics(planId, startTime, endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(planId, result.planId());
        assertEquals("华能资本舆情监控方案", result.planName());
        assertEquals(startTime, result.startTime());
        assertEquals(endTime, result.endTime());
        assertEquals(totalCount, result.totalAlertCount());
        assertEquals(sensitiveCount, result.sensitiveAlertCount());
    }

    @Test
    void testGetAlertStatistics_WithNullTimes() {
        // 准备测试数据
        Long totalCount = 200L;
        Long sensitiveCount = 50L;

        // 模拟依赖调用
        when(planService.getPlanById(planId)).thenReturn(mockPlanDto);
        when(alertResultRepository.countByPlanIdAndWarningTimeBetween(eq(planId), any(), any()))
                .thenReturn(totalCount);
        when(alertResultRepository.countSensitiveByPlanIdAndWarningTimeBetween(eq(planId), any(), any()))
                .thenReturn(sensitiveCount);

        // 执行测试（时间参数为null）
        AlertStatisticsDto result = alertSearchService.getAlertStatistics(planId, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(planId, result.planId());
        assertEquals("华能资本舆情监控方案", result.planName());
        assertNull(result.startTime());
        assertNull(result.endTime());
        assertEquals(totalCount, result.totalAlertCount());
        assertEquals(sensitiveCount, result.sensitiveAlertCount());
    }

    @Test
    void testGetAlertStatistics_PlanNotFound() {
        // 模拟方案不存在
        when(planService.getPlanById(planId)).thenThrow(new IllegalArgumentException("Plan not found with ID: " + planId));

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            alertSearchService.getAlertStatistics(planId, startTime, endTime);
        });

        assertEquals("Plan not found with ID: " + planId, exception.getMessage());
    }

    @Test
    void testGetAlertStatistics_ZeroCounts() {
        // 准备测试数据 - 零计数
        Long totalCount = 0L;
        Long sensitiveCount = 0L;

        // 模拟依赖调用
        when(planService.getPlanById(planId)).thenReturn(mockPlanDto);
        when(alertResultRepository.countByPlanIdAndWarningTimeBetween(eq(planId), eq(startTime), eq(endTime)))
                .thenReturn(totalCount);
        when(alertResultRepository.countSensitiveByPlanIdAndWarningTimeBetween(eq(planId), eq(startTime), eq(endTime)))
                .thenReturn(sensitiveCount);

        // 执行测试
        AlertStatisticsDto result = alertSearchService.getAlertStatistics(planId, startTime, endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(0L, result.totalAlertCount());
        assertEquals(0L, result.sensitiveAlertCount());
    }
}
