package com.czb.hn.service.business;

import com.czb.hn.dto.workbench.AlertListItemDTO;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.impl.AlertSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 预警搜索服务时间段查询功能测试
 */
@ExtendWith(MockitoExtension.class)
public class AlertSearchServiceTimeRangeTest {

    @Mock
    private AlertResultRepository alertResultRepository;

    @InjectMocks
    private AlertSearchServiceImpl alertSearchService;

    private AlertResult mockAlertResult1;
    private AlertResult mockAlertResult2;
    private LocalDateTime now;

    @BeforeEach
    void setUp() {
        now = LocalDateTime.now();

        mockAlertResult1 = AlertResult.builder()
                .id(1L)
                .title("重要安全预警")
                .content("发现重要安全漏洞，请及时关注")
                .involvedKeywords("安全,漏洞,预警")
                .informationSensitivityType(InformationSensitivityType.SENSITIVE)
                .contentCategory(ContentCategory.ORIGINAL)
                .warningLevel(AlertResult.WarningLevel.SEVERE)
                .source("微博")
                .warningTime(now.minusHours(1))
                .similarArticleCount(5)
                .enterpriseId("enterprise123")
                .planId(1L)
                .build();

        mockAlertResult2 = AlertResult.builder()
                .id(2L)
                .title("一般信息提醒")
                .content("一般性信息提醒内容")
                .involvedKeywords("信息,提醒")
                .informationSensitivityType(InformationSensitivityType.NEUTRAL)
                .contentCategory(ContentCategory.ORIGINAL)
                .warningLevel(AlertResult.WarningLevel.GENERAL)
                .source("微信")
                .warningTime(now.minusHours(2))
                .similarArticleCount(2)
                .enterpriseId("enterprise123")
                .planId(1L)
                .build();
    }

    @Test
    @DisplayName("测试按方案ID和时间段查询预警信息 - 成功")
    void testGetAlertListByPlanIdAndTimeRange_Success() {
        // 准备测试数据
        Long planId = 1L;
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;
        Integer limit = 20;

        when(alertResultRepository.findTopAlertsByPlanIdAndTimeRange(eq(planId), eq(startTime), eq(endTime), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1, mockAlertResult2));

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        AlertListItemDTO firstAlert = result.get(0);
        assertEquals(1L, firstAlert.id());
        assertEquals("重要安全预警", firstAlert.title());
        assertEquals(InformationSensitivityType.SENSITIVE, firstAlert.sensitivityType());
    }

    @Test
    @DisplayName("测试按企业ID和时间段查询预警信息 - 成功")
    void testGetAlertListByEnterpriseIdAndTimeRange_Success() {
        // 准备测试数据
        String enterpriseId = "enterprise123";
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;
        Integer limit = 20;

        when(alertResultRepository.findTopAlertsByEnterpriseIdAndTimeRange(eq(enterpriseId), eq(startTime), eq(endTime), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1, mockAlertResult2));

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByEnterpriseIdAndTimeRange(enterpriseId, startTime, endTime, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        AlertListItemDTO firstAlert = result.get(0);
        assertEquals(1L, firstAlert.id());
        assertEquals("重要安全预警", firstAlert.title());
    }

    @Test
    @DisplayName("测试时间段查询 - 只有开始时间")
    void testTimeRangeQuery_OnlyStartTime() {
        Long planId = 1L;
        LocalDateTime startTime = now.minusHours(3);
        Integer limit = 20;

        when(alertResultRepository.findTopAlertsByPlanIdAndTimeRange(eq(planId), eq(startTime), isNull(), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1));

        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, null, limit);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    @DisplayName("测试时间段查询 - 只有结束时间")
    void testTimeRangeQuery_OnlyEndTime() {
        Long planId = 1L;
        LocalDateTime endTime = now;
        Integer limit = 20;

        when(alertResultRepository.findTopAlertsByPlanIdAndTimeRange(eq(planId), isNull(), eq(endTime), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult2));

        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, null, endTime, limit);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    @DisplayName("测试时间段查询 - 空结果")
    void testTimeRangeQuery_EmptyResult() {
        Long planId = 1L;
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;
        Integer limit = 20;

        when(alertResultRepository.findTopAlertsByPlanIdAndTimeRange(eq(planId), eq(startTime), eq(endTime), any(Pageable.class)))
                .thenReturn(Collections.emptyList());

        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, limit);

        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("测试时间段查询 - 参数验证：planId为null")
    void testTimeRangeQuery_NullPlanId() {
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;
        Integer limit = 20;

        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByPlanIdAndTimeRange(null, startTime, endTime, limit);
        });
    }

    @Test
    @DisplayName("测试时间段查询 - 参数验证：开始时间晚于结束时间")
    void testTimeRangeQuery_InvalidTimeRange() {
        Long planId = 1L;
        LocalDateTime startTime = now;
        LocalDateTime endTime = now.minusHours(1); // 结束时间早于开始时间
        Integer limit = 20;

        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, limit);
        });
    }

    @Test
    @DisplayName("测试时间段查询 - 参数验证：企业ID为空")
    void testTimeRangeQuery_EmptyEnterpriseId() {
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;
        Integer limit = 20;

        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByEnterpriseIdAndTimeRange("", startTime, endTime, limit);
        });
    }

    @Test
    @DisplayName("测试时间段查询 - 限制参数处理")
    void testTimeRangeQuery_LimitHandling() {
        Long planId = 1L;
        LocalDateTime startTime = now.minusHours(3);
        LocalDateTime endTime = now;

        when(alertResultRepository.findTopAlertsByPlanIdAndTimeRange(eq(planId), eq(startTime), eq(endTime), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1));

        // 测试默认限制
        List<AlertListItemDTO> result1 = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, null);
        assertNotNull(result1);

        // 测试超过最大限制
        List<AlertListItemDTO> result2 = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, 150);
        assertNotNull(result2);
    }
}
