package com.czb.hn.service.business;

import com.czb.hn.dto.workbench.AlertListItemDTO;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.impl.AlertSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * AlertSearchService预警信息列表功能测试
 */
@ExtendWith(MockitoExtension.class)
class AlertSearchServiceAlertListTest {

    @Mock
    private AlertResultRepository alertResultRepository;

    @Mock
    private PlanService planService;

    @InjectMocks
    private AlertSearchServiceImpl alertSearchService;

    private AlertResult mockAlertResult1;
    private AlertResult mockAlertResult2;

    @BeforeEach
    void setUp() {
        LocalDateTime now = LocalDateTime.now();
        
        mockAlertResult1 = AlertResult.builder()
                .id(1L)
                .title("重要安全预警")
                .content("发现重要安全漏洞，请及时关注")
                .involvedKeywords("安全,漏洞,预警")
                .informationSensitivityType(InformationSensitivityType.SENSITIVE)
                .contentCategory(ContentCategory.ORIGINAL)
                .warningLevel(AlertResult.WarningLevel.SEVERE)
                .source("微博")
                .warningTime(now.minusHours(1))
                .similarArticleCount(5)
                .enterpriseId("enterprise123")
                .planId(1L)
                .build();

        mockAlertResult2 = AlertResult.builder()
                .id(2L)
                .title("一般信息提醒")
                .content("一般性信息提醒内容")
                .involvedKeywords("信息,提醒")
                .informationSensitivityType(InformationSensitivityType.NEUTRAL)
                .contentCategory(ContentCategory.ORIGINAL)
                .warningLevel(AlertResult.WarningLevel.GENERAL)
                .source("微信")
                .warningTime(now.minusHours(2))
                .similarArticleCount(2)
                .enterpriseId("enterprise123")
                .planId(1L)
                .build();
    }

    @Test
    void testGetAlertListByPlanId_Success() {
        // 准备测试数据
        Long planId = 1L;
        Integer limit = 20;
        
        when(alertResultRepository.findTopAlertsByPlanId(eq(planId), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1, mockAlertResult2));

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanId(planId, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        AlertListItemDTO firstAlert = result.get(0);
        assertEquals(1L, firstAlert.id());
        assertEquals("重要安全预警", firstAlert.title());
        assertEquals("发现重要安全漏洞，请及时关注", firstAlert.content());
        assertEquals(InformationSensitivityType.SENSITIVE, firstAlert.sensitivityType());
        assertEquals("敏感", firstAlert.sensitivityDescription());
        assertEquals("微博", firstAlert.source());
        assertEquals("安全,漏洞,预警", firstAlert.involvedKeywords());
        assertEquals(AlertResult.WarningLevel.SEVERE, firstAlert.warningLevel());
        assertEquals("严重", firstAlert.warningLevelDescription());
        assertEquals(5, firstAlert.similarArticleCount());
    }

    @Test
    void testGetAlertListByPlanId_WithDefaultLimit() {
        // 测试默认limit
        Long planId = 1L;
        
        when(alertResultRepository.findTopAlertsByPlanId(eq(planId), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1));

        // 执行测试 - 传入null limit
        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanId(planId, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testGetAlertListByPlanId_WithLimitExceedsMaximum() {
        // 测试limit超过最大值
        Long planId = 1L;
        Integer limit = 150; // 超过最大值100
        
        when(alertResultRepository.findTopAlertsByPlanId(eq(planId), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1));

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByPlanId(planId, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testGetAlertListByEnterpriseId_Success() {
        // 准备测试数据
        String enterpriseId = "enterprise123";
        Integer limit = 20;
        
        when(alertResultRepository.findTopAlertsByEnterpriseId(eq(enterpriseId), any(Pageable.class)))
                .thenReturn(Arrays.asList(mockAlertResult1, mockAlertResult2));

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByEnterpriseId(enterpriseId, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        AlertListItemDTO firstAlert = result.get(0);
        assertEquals(1L, firstAlert.id());
        assertEquals("重要安全预警", firstAlert.title());
    }

    @Test
    void testGetAlertListByEnterpriseId_EmptyResult() {
        // 测试空结果
        String enterpriseId = "enterprise123";
        Integer limit = 20;
        
        when(alertResultRepository.findTopAlertsByEnterpriseId(eq(enterpriseId), any(Pageable.class)))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<AlertListItemDTO> result = alertSearchService.getAlertListByEnterpriseId(enterpriseId, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void testGetAlertListByPlanId_NullPlanId() {
        // 测试planId为null的情况
        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByPlanId(null, 20);
        });
    }

    @Test
    void testGetAlertListByEnterpriseId_NullEnterpriseId() {
        // 测试enterpriseId为null的情况
        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByEnterpriseId(null, 20);
        });
    }

    @Test
    void testGetAlertListByEnterpriseId_EmptyEnterpriseId() {
        // 测试enterpriseId为空字符串的情况
        assertThrows(RuntimeException.class, () -> {
            alertSearchService.getAlertListByEnterpriseId("", 20);
        });
    }
}
