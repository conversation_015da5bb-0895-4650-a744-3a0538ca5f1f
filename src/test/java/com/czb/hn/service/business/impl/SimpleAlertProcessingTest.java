package com.czb.hn.service.business.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertRuleEngine;
import com.czb.hn.service.business.KeywordTrackingService;
import com.czb.hn.util.AlertResultMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 简单的测试类，用于验证编译修复是否正确
 */
@ExtendWith(MockitoExtension.class)
class SimpleAlertProcessingTest {

    @Mock
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Mock
    private AlertRuleEngine ruleEngine;

    @Mock
    private ElasticsearchClient elasticsearchClient;

    @Mock
    private AlertResultRepository alertResultRepository;

    @Mock
    private AlertResultMapper alertResultMapper;

    @Mock
    private KeywordTrackingService keywordTrackingService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private AlertProcessingServiceImpl alertProcessingService;

    private AlertConfigurationResponseDto testConfig;
    private SinaNewsDocument testDocument;
    private AlertResult testAlertResult;
    private AlertResultResponseDto testAlertResponseDto;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        testConfig = new AlertConfigurationResponseDto(
                1L, "测试配置", "测试描述", 100L, "enterprise-001", true,
                null, null, null, null, null,
                LocalDateTime.now(), LocalDateTime.now(), "testUser", "testUser", 1, true, LocalDateTime.now());

        // 设置测试文档
        testDocument = new SinaNewsDocument();
        testDocument.setContentId("doc-001");
        testDocument.setTitle("测试文档标题");
        testDocument.setContent("测试文档内容");
        testDocument.setSource("微博");
        testDocument.setPublishTime(LocalDateTime.now());
        testDocument.setSimilarityNum(5L);

        // 设置测试告警结果
        testAlertResult = new AlertResult();
        testAlertResult.setId(1L);
        testAlertResult.setEnterpriseId("enterprise-001");
        testAlertResult.setTitle("测试告警");

        // 设置测试响应DTO
        testAlertResponseDto = new AlertResultResponseDto(
                1L, "enterprise-001", 100L, 1L, "测试告警", "测试内容",
                "[{\"keyword\":\"关键词1\",\"count\":3}]", InformationSensitivityType.NEUTRAL.getValue(),
                ContentCategory.ORIGINAL.getValue(),
                SourceType.WEIBO.getValue(),
                ContentType.TEXT.getValue(),
                ContentMatchType.MAIN_TXT.getValue(),
                MediaLevel.SMALL_MEDIUM.getValue(),
                AlertResult.WarningLevel.GENERAL.name(),
                "微博", "北京", LocalDateTime.now(), 1, "doc-001", null, LocalDateTime.now(), LocalDateTime.now());
    }

    @Test
    void testProcessEnterpriseAlerts_ShouldReturnEmpty_WhenNoConfigurations() throws Exception {
        // 准备测试数据
        String enterpriseId = "enterprise-001";

        // 模拟无配置情况
        when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                .thenReturn(List.of());

        // 执行测试
        List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

        // 验证结果
        assertNotNull(results);
        assertTrue(results.isEmpty());

        // 验证方法调用
        verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
        // 当没有配置时，不应该调用 Elasticsearch
        verify(elasticsearchClient, never()).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
    }

    /**
     * 创建模拟的 Elasticsearch 搜索响应
     */
    @SuppressWarnings("unchecked")
    private SearchResponse<SinaNewsDocument> mockElasticsearchResponse(List<SinaNewsDocument> documents) {
        SearchResponse<SinaNewsDocument> mockResponse = mock(SearchResponse.class);
        HitsMetadata<SinaNewsDocument> mockHits = mock(HitsMetadata.class);

        List<Hit<SinaNewsDocument>> hits = documents.stream()
                .map(doc -> {
                    Hit<SinaNewsDocument> hit = mock(Hit.class);
                    when(hit.source()).thenReturn(doc);
                    return hit;
                })
                .toList();

        when(mockHits.hits()).thenReturn(hits);
        when(mockResponse.hits()).thenReturn(mockHits);

        return mockResponse;
    }
}
