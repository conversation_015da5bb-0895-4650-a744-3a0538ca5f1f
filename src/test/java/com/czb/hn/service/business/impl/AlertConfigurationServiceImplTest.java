package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.*;
import com.czb.hn.dto.alert.config.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertConfigurationSnapshot;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationRepository;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationSnapshotRepository;
import com.czb.hn.util.AlertConfigurationMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AlertConfigurationServiceImpl
 */
@ExtendWith(MockitoExtension.class)
public class AlertConfigurationServiceImplTest {

        @Mock
        private AlertConfigurationRepository alertConfigurationRepository;

        @Mock
        private AlertConfigurationSnapshotRepository snapshotRepository;

        @Mock
        private AlertConfigurationMapper configurationMapper;

        @Mock
        private ObjectMapper objectMapper;

        @InjectMocks
        private AlertConfigurationServiceImpl alertConfigurationService;

        private AlertConfiguration testConfiguration;
        private AlertConfigurationCreateDto testCreateDto;
        private AlertConfigurationResponseDto testResponseDto;

        private AlertKeywordsDto validAlertKeywords;
        private ContentSettingsDto validContentSettings;
        private ThresholdSettingsDto validThresholdSettings;
        private LevelSettingsDto validLevelSettings;
        private ReceptionSettingsDto validReceptionSettings;

        @BeforeEach
        void setUp() {
                // Setup valid DTO objects for testing
                validAlertKeywords = new AlertKeywordsDto(
                                List.of("urgent", "critical", "breaking"),
                                "Test alert keywords");

                validContentSettings = new ContentSettingsDto(
                                "1", // sensitivityType
                                List.of("wb", "wx"), // sourceTypes
                                List.of("1", "2"), // contentTypes
                                "1", // resultDisplay
                                List.of("-1", "1"), // weiboVerification
                                List.of("央级", "省级"), // sourceLevel
                                "1" // contentCategory
                );

                ThresholdSettingsDto.ThresholdConditionDto interactionThreshold = new ThresholdSettingsDto.ThresholdConditionDto(
                                true, 100L);

                validThresholdSettings = new ThresholdSettingsDto(
                                "OR", interactionThreshold, null, null, null, List.of());

                LevelSettingsDto.ThresholdRangeDto generalRange = new LevelSettingsDto.ThresholdRangeDto(0L, 99L);
                LevelSettingsDto.ThresholdRangeDto mediumRange = new LevelSettingsDto.ThresholdRangeDto(100L, 999L);
                LevelSettingsDto.ThresholdRangeDto severeRange = new LevelSettingsDto.ThresholdRangeDto(1000L, null);

                LevelSettingsDto.LevelThresholdsDto interactionLevels = new LevelSettingsDto.LevelThresholdsDto(
                                generalRange,
                                mediumRange, severeRange);

                validLevelSettings = new LevelSettingsDto(
                                null, interactionLevels, null, null, null);

                ReceptionSettingsDto.TimePeriodDto timePeriod = new ReceptionSettingsDto.TimePeriodDto("09:00",
                                "18:00");

                ReceptionSettingsDto.EmailRecipientDto emailRecipient = new ReceptionSettingsDto.EmailRecipientDto(
                                "Test User",
                                "<EMAIL>");

                ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(true,
                                List.of(emailRecipient));

                ReceptionSettingsDto.SmsRecipientDto smsRecipient = new ReceptionSettingsDto.SmsRecipientDto(
                                "Test User",
                                "13800138000");

                ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(false,
                                List.of(smsRecipient));

                ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                                emailConfig, smsConfig);

                validReceptionSettings = new ReceptionSettingsDto(
                                "DAILY", 30, timePeriod, false, receptionMethods, false);

                // Setup test data
                testConfiguration = new AlertConfiguration();
                testConfiguration.setId(1L);
                testConfiguration.setName("Test Configuration");
                testConfiguration.setDescription("Test Description");
                testConfiguration.setPlanId(1L); // Fixed: Use Long instead of String
                testConfiguration.setEnterpriseId("enterprise123");
                testConfiguration.setEnabled(true);
                testConfiguration.setCreatedAt(LocalDateTime.now());
                testConfiguration.setUpdatedAt(LocalDateTime.now());

                testCreateDto = new AlertConfigurationCreateDto(
                                "Test Configuration",
                                "Test Description",
                                1L, // Fixed: Use Long instead of String
                                "enterprise123",
                                true,
                                validAlertKeywords,
                                validContentSettings,
                                validThresholdSettings,
                                validLevelSettings,
                                validReceptionSettings,
                                "testUser",
                                "Test creation");

                testResponseDto = new AlertConfigurationResponseDto(
                                1L,
                                "Test Configuration",
                                "Test Description",
                                1L, // Fixed: Use Long instead of String
                                "enterprise123",
                                true,
                                validAlertKeywords,
                                validContentSettings,
                                validThresholdSettings,
                                validLevelSettings,
                                validReceptionSettings,
                                LocalDateTime.now(),
                                LocalDateTime.now(),
                                "testUser",
                                "testUser",
                                1,
                                true, // isActive
                                LocalDateTime.now() // lastSnapshotAt
                );
        }

        @Test
        void createConfiguration_Success() {
                // Given
                when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                .thenReturn(false);
                when(configurationMapper.toEntity(any(AlertConfigurationCreateDto.class)))
                                .thenReturn(testConfiguration);
                when(alertConfigurationRepository.save(any(AlertConfiguration.class))).thenReturn(testConfiguration);
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);
                when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                .thenReturn("{\"test\":\"snapshot\"}");

                AlertConfigurationSnapshot mockSnapshot = new AlertConfigurationSnapshot();
                mockSnapshot.setId(1L);
                when(snapshotRepository.save(any(AlertConfigurationSnapshot.class))).thenReturn(mockSnapshot);

                // When
                AlertConfigurationResponseDto result = alertConfigurationService.createConfiguration(testCreateDto);

                // Then
                assertNotNull(result);
                assertEquals("Test Configuration", result.name());
                assertEquals("enterprise123", result.enterpriseId());
                verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
        }

        @Test
        void createConfiguration_DuplicateName_ThrowsException() {
                // Given
                when(alertConfigurationRepository.existsByNameAndEnterpriseId(anyString(), anyString()))
                                .thenReturn(true);

                // When & Then
                RuntimeException exception = assertThrows(RuntimeException.class,
                                () -> alertConfigurationService.createConfiguration(testCreateDto));
                assertTrue(exception.getMessage().contains("Failed to create alert configuration") ||
                                exception.getMessage()
                                                .contains("Configuration name already exists for this enterprise"));
        }

        @Test
        void getConfigurationById_Success() {
                // Given
                when(alertConfigurationRepository.findActiveById(1L)).thenReturn(Optional.of(testConfiguration));
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);

                // When
                AlertConfigurationResponseDto result = alertConfigurationService.getConfigurationById(1L);

                // Then
                assertNotNull(result);
                assertEquals(1L, result.id());
                assertEquals("Test Configuration", result.name());
        }

        @Test
        void getConfigurationById_NotFound_ThrowsException() {
                // Given
                when(alertConfigurationRepository.findActiveById(1L)).thenReturn(Optional.empty());

                // When & Then
                IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                                () -> alertConfigurationService.getConfigurationById(1L));
                assertEquals("Alert configuration not found with ID: 1", exception.getMessage());
        }

        @Test
        void updateConfiguration_Success() {
                // Given
                AlertConfigurationUpdateDto updateDto = new AlertConfigurationUpdateDto(
                                "Updated Configuration",
                                "Updated Description",
                                2L, // planId
                                true, // enabled
                                validAlertKeywords,
                                validContentSettings,
                                validThresholdSettings,
                                validLevelSettings,
                                validReceptionSettings,
                                "updateUser",
                                "Update test");

                when(alertConfigurationRepository.findActiveById(1L)).thenReturn(Optional.of(testConfiguration));
                when(alertConfigurationRepository.existsByNameAndEnterpriseIdExcludingId(anyString(), anyString(),
                                anyLong()))
                                .thenReturn(false);
                when(alertConfigurationRepository.save(any(AlertConfiguration.class))).thenReturn(testConfiguration);
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);
                when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                .thenReturn("{\"test\":\"snapshot\"}");

                AlertConfigurationSnapshot mockSnapshot = new AlertConfigurationSnapshot();
                mockSnapshot.setId(1L);
                when(snapshotRepository.save(any(AlertConfigurationSnapshot.class))).thenReturn(mockSnapshot);

                // When
                AlertConfigurationResponseDto result = alertConfigurationService.updateConfiguration(1L, updateDto);

                // Then
                assertNotNull(result);
                verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
        }

        @Test
        void deleteConfiguration_Success() {
                // Given
                when(alertConfigurationRepository.findActiveById(1L)).thenReturn(Optional.of(testConfiguration));
                when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                .thenReturn("{\"test\":\"snapshot\"}");

                AlertConfigurationSnapshot mockSnapshot = new AlertConfigurationSnapshot();
                mockSnapshot.setId(1L);
                when(snapshotRepository.save(any(AlertConfigurationSnapshot.class))).thenReturn(mockSnapshot);

                // When
                alertConfigurationService.deleteConfiguration(1L, "testUser", "Test deletion");

                // Then
                verify(alertConfigurationRepository).findActiveById(1L);
                verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
                verify(alertConfigurationRepository).softDeleteById(1L, "testUser");
        }

        @Test
        void toggleConfiguration_Enable_Success() {
                // Given
                testConfiguration.setEnabled(false);
                when(alertConfigurationRepository.findActiveById(1L)).thenReturn(Optional.of(testConfiguration));
                when(alertConfigurationRepository.save(any(AlertConfiguration.class))).thenReturn(testConfiguration);
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);
                when(configurationMapper.toSnapshotJson(any(AlertConfiguration.class)))
                                .thenReturn("{\"test\":\"snapshot\"}");

                AlertConfigurationSnapshot mockSnapshot = new AlertConfigurationSnapshot();
                mockSnapshot.setId(1L);
                when(snapshotRepository.save(any(AlertConfigurationSnapshot.class))).thenReturn(mockSnapshot);

                // When
                AlertConfigurationResponseDto result = alertConfigurationService.toggleConfiguration(1L, true,
                                "testUser",
                                "Enable test");

                // Then
                assertNotNull(result);
                verify(alertConfigurationRepository).save(any(AlertConfiguration.class));
                verify(snapshotRepository).save(any(AlertConfigurationSnapshot.class));
        }

        @Test
        void getConfigurationsByEnterpriseId_Success() {
                // Given
                List<AlertConfiguration> configurations = Arrays.asList(testConfiguration);
                when(alertConfigurationRepository.findByEnterpriseId("enterprise123")).thenReturn(configurations);
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);

                // When
                List<AlertConfigurationResponseDto> result = alertConfigurationService
                                .getConfigurationsByEnterpriseId("enterprise123");

                // Then
                assertNotNull(result);
                assertEquals(1, result.size());
                assertEquals("Test Configuration", result.get(0).name());
        }

        @Test
        void searchConfigurations_Success() {
                // Given
                Pageable pageable = PageRequest.of(0, 10);
                Page<AlertConfiguration> configPage = new PageImpl<>(Arrays.asList(testConfiguration));
                when(alertConfigurationRepository.findByNameContaining(anyString(), any(Pageable.class)))
                                .thenReturn(configPage);
                when(configurationMapper.toResponseDto(any(AlertConfiguration.class))).thenReturn(testResponseDto);

                // When
                Page<AlertConfigurationResponseDto> result = alertConfigurationService.searchConfigurationsByName(
                                "test", pageable);

                // Then
                assertNotNull(result);
                assertEquals(1, result.getTotalElements());
                assertEquals("Test Configuration", result.getContent().get(0).name());
        }

        @Test
        void isConfigurationNameAvailable_Available_ReturnsTrue() {
                // Given
                when(alertConfigurationRepository.existsByNameAndEnterpriseId("New Name", "enterprise123"))
                                .thenReturn(false);

                // When
                Boolean result = alertConfigurationService.isConfigurationNameAvailable("New Name", "enterprise123",
                                null);

                // Then
                assertTrue(result);
        }

        @Test
        void isConfigurationNameAvailable_NotAvailable_ReturnsFalse() {
                // Given
                when(alertConfigurationRepository.existsByNameAndEnterpriseId("Existing Name", "enterprise123"))
                                .thenReturn(true);

                // When
                Boolean result = alertConfigurationService.isConfigurationNameAvailable("Existing Name",
                                "enterprise123", null);

                // Then
                assertFalse(result);
        }

        @Test
        void getConfigurationStatistics_Success() {
                // Given
                when(alertConfigurationRepository.countByEnterpriseId("enterprise123")).thenReturn(10L);
                when(alertConfigurationRepository.countEnabledByEnterpriseId("enterprise123")).thenReturn(8L);
                when(alertConfigurationRepository.findByEnterpriseId("enterprise123"))
                                .thenReturn(Arrays.asList(testConfiguration));
                when(snapshotRepository.countByConfigurationId(1L)).thenReturn(5L);

                // When
                var result = alertConfigurationService.getConfigurationStatistics("enterprise123");

                // Then
                assertNotNull(result);
                assertEquals(10L, result.totalConfigurations());
                assertEquals(8L, result.enabledConfigurations());
                assertEquals(2L, result.disabledConfigurations());
                assertEquals(5L, result.totalSnapshots());
                assertEquals(0L, result.averageSnapshotsPerConfiguration()); // 5/10 = 0 (integer division)
        }
}
