package com.czb.hn.service.business;

import com.czb.hn.dto.PlanCreateDTO;
import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.PlanUpdateDTO;
import com.czb.hn.jpa.securadar.entity.Plan;
import com.czb.hn.jpa.securadar.repository.PlanRepository;
import com.czb.hn.service.business.impl.PlanServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("方案服务关键词处理测试")
class PlanServiceKeywordTest {

    @Mock
    private PlanRepository planRepository;

    @InjectMocks
    private PlanServiceImpl planService;

    private Plan testPlan;

    @BeforeEach
    void setUp() {
        testPlan = new Plan();
        testPlan.setId(1L);
        testPlan.setName("测试方案");
        testPlan.setDescription("测试描述");
        testPlan.setMonitorKeywords("关键词1|关键词2|关键词3");
        testPlan.setExcludeKeywords("排除词1|排除词2");
        testPlan.setEnterpriseId("enterprise123");
        testPlan.setCreatedAt(LocalDateTime.now());
        testPlan.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    @DisplayName("创建方案时清理关键词")
    void testCreatePlan_CleansKeywords() {
        // 准备测试数据 - 包含需要清理的关键词
        PlanCreateDTO createDTO = new PlanCreateDTO(
                "测试方案",
                "测试描述",
                "关键词1|关键词2|关键词3", // 使用新的逻辑运算符格式
                "排除词1|排除词2",
                "enterprise123");

        Plan savedPlan = new Plan();
        savedPlan.setId(1L);
        savedPlan.setName(createDTO.name());
        savedPlan.setDescription(createDTO.description());
        savedPlan.setMonitorKeywords("关键词1|关键词2|关键词3"); // 清理后的结果
        savedPlan.setExcludeKeywords("排除词1|排除词2");
        savedPlan.setEnterpriseId(createDTO.enterpriseId());
        savedPlan.setCreatedAt(LocalDateTime.now());
        savedPlan.setUpdatedAt(LocalDateTime.now());

        when(planRepository.save(any(Plan.class))).thenReturn(savedPlan);

        // 执行测试
        PlanDTO result = planService.createPlan(createDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals("关键词1|关键词2|关键词3", result.monitorKeywords());
        assertEquals("排除词1|排除词2", result.excludeKeywords());

        // 验证保存时关键词已被清理
        verify(planRepository).save(argThat(plan -> "关键词1|关键词2|关键词3".equals(plan.getMonitorKeywords()) &&
                "排除词1|排除词2".equals(plan.getExcludeKeywords())));
    }

    @Test
    @DisplayName("更新方案时清理关键词")
    void testUpdatePlan_CleansKeywords() {
        // 准备测试数据
        PlanUpdateDTO updateDTO = new PlanUpdateDTO(
                null,
                null,
                "新关键词1|新关键词2", // 使用新的逻辑运算符格式
                "新排除词1|新排除词2",
                null);

        when(planRepository.findById(1L)).thenReturn(Optional.of(testPlan));
        when(planRepository.save(any(Plan.class))).thenReturn(testPlan);

        // 执行测试
        PlanDTO result = planService.updatePlan(1L, updateDTO);

        // 验证保存时关键词已被清理
        verify(planRepository).save(argThat(plan -> "新关键词1|新关键词2".equals(plan.getMonitorKeywords()) &&
                "新排除词1|新排除词2".equals(plan.getExcludeKeywords())));
    }



    @Test
    @DisplayName("创建方案时处理空排除关键词")
    void testCreatePlan_EmptyExcludeKeywords() {
        PlanCreateDTO createDTO = new PlanCreateDTO(
                "测试方案",
                "测试描述",
                "关键词1|关键词2",
                "", // 空排除关键词
                "enterprise123");

        Plan savedPlan = new Plan();
        savedPlan.setId(1L);
        savedPlan.setName(createDTO.name());
        savedPlan.setDescription(createDTO.description());
        savedPlan.setMonitorKeywords("关键词1|关键词2");
        savedPlan.setExcludeKeywords(null); // 应该设置为null
        savedPlan.setEnterpriseId(createDTO.enterpriseId());
        savedPlan.setCreatedAt(LocalDateTime.now());
        savedPlan.setUpdatedAt(LocalDateTime.now());

        when(planRepository.save(any(Plan.class))).thenReturn(savedPlan);

        PlanDTO result = planService.createPlan(createDTO);

        // 验证保存时空排除关键词被设置为null
        verify(planRepository).save(argThat(plan -> plan.getExcludeKeywords() == null));
    }

    @Test
    @DisplayName("更新方案时处理空排除关键词")
    void testUpdatePlan_EmptyExcludeKeywords() {
        PlanUpdateDTO updateDTO = new PlanUpdateDTO(
                null,
                null,
                null,
                "", // 空排除关键词
                null);

        when(planRepository.findById(1L)).thenReturn(Optional.of(testPlan));
        when(planRepository.save(any(Plan.class))).thenReturn(testPlan);

        planService.updatePlan(1L, updateDTO);

        // 验证保存时空排除关键词被设置为null
        verify(planRepository).save(argThat(plan -> plan.getExcludeKeywords() == null));
    }
}
