package com.czb.hn.service.business;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.workbench.InformationStatsDTO;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.impl.InformationStatsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 信息统计服务测试类
 */
@ExtendWith(MockitoExtension.class)
class InformationStatsServiceTest {

    @Mock
    private ElasticsearchSearchService elasticsearchSearchService;

    @Mock
    private AlertResultRepository alertResultRepository;

    @Mock
    private PlanService planService;

    @InjectMocks
    private InformationStatsServiceImpl informationStatsService;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Long planId;
    private String enterpriseId;
    private PlanDTO mockPlanDto;

    @BeforeEach
    void setUp() {
        startTime = LocalDateTime.of(2024, 1, 15, 10, 0, 0);
        endTime = LocalDateTime.of(2024, 1, 15, 18, 0, 0);
        planId = 1L;
        enterpriseId = "enterprise123";

        mockPlanDto = new PlanDTO(
                planId,
                "测试方案",
                "测试方案描述",
                "测试关键词",
                "排除关键词",
                null,
                LocalDateTime.now(),
                LocalDateTime.now());
    }

    @Test
    void testGetInformationStats_Success() {
        // 准备测试数据
        Long totalCount = 1250L;
        Long sensitiveCount = 85L;
        Long alertCount = 12L;

        // 模拟依赖调用
        when(planService.getPlanById(planId)).thenReturn(mockPlanDto);
        when(elasticsearchSearchService.countInformationTotal(eq(planId), anyString(), anyString()))
                .thenReturn(totalCount);
        when(elasticsearchSearchService.countSensitiveInformationTotal(eq(planId), anyString(), anyString()))
                .thenReturn(sensitiveCount);
        when(alertResultRepository.countByPlanIdAndWarningTimeBetween(eq(planId), eq(startTime), eq(endTime)))
                .thenReturn(alertCount);

        // 执行测试
        InformationStatsDTO result = informationStatsService.getInformationStats(planId, startTime, endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(startTime, result.startTime());
        assertEquals(endTime, result.endTime());
        assertEquals(planId, result.planId());
        assertEquals("测试方案", result.planName());
        assertEquals(totalCount, result.totalInformationCount());
        assertEquals(sensitiveCount, result.sensitiveInformationCount());
        assertEquals(alertCount, result.alertCount());
    }

    @Test
    void testGetEnterpriseInformationStats_Success() {
        // 准备测试数据
        PlanDTO plan1 = new PlanDTO(1L, "方案1", "描述1", "关键词1", null, null, LocalDateTime.now(), LocalDateTime.now());
        PlanDTO plan2 = new PlanDTO(2L, "方案2", "描述2", "关键词2", null, null, LocalDateTime.now(), LocalDateTime.now());

        // 模拟依赖调用
        when(planService.getPlansByEnterpriseId(enterpriseId)).thenReturn(Arrays.asList(plan1, plan2));

        // 模拟批量查询响应
        Map<Long, Long> totalCounts = new HashMap<>();
        totalCounts.put(1L, 500L);
        totalCounts.put(2L, 750L);

        Map<Long, Long> sensitiveCounts = new HashMap<>();
        sensitiveCounts.put(1L, 30L);
        sensitiveCounts.put(2L, 55L);

        List<Object[]> alertCounts = new ArrayList<>();
        alertCounts.add(new Object[] { 1L, 5L });
        alertCounts.add(new Object[] { 2L, 7L });

        when(elasticsearchSearchService.batchCountInformationTotal(any(), anyString(), anyString()))
                .thenReturn(totalCounts);
        when(elasticsearchSearchService.batchCountSensitiveInformationTotal(any(), anyString(), anyString()))
                .thenReturn(sensitiveCounts);
        when(alertResultRepository.batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any()))
                .thenReturn(alertCounts);

        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime,
                endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(startTime, result.startTime());
        assertEquals(endTime, result.endTime());
        assertNull(result.planId());
        assertEquals("企业下所有方案", result.planName());
        assertEquals(1250L, result.totalInformationCount()); // 500 + 750
        assertEquals(85L, result.sensitiveInformationCount()); // 30 + 55
        assertEquals(12L, result.alertCount()); // 5 + 7
    }

    @Test
    void testGetEnterpriseInformationStats_NoPlans() {
        // 模拟没有方案的情况
        when(planService.getPlansByEnterpriseId(enterpriseId)).thenReturn(Collections.emptyList());

        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime,
                endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(startTime, result.startTime());
        assertEquals(endTime, result.endTime());
        assertNull(result.planId());
        assertEquals("企业下所有方案", result.planName());
        assertEquals(0L, result.totalInformationCount());
        assertEquals(0L, result.sensitiveInformationCount());
        assertEquals(0L, result.alertCount());
    }

    @Test
    void testGetInformationStats_PlanNotFound() {
        // 模拟方案不存在的情况
        when(planService.getPlanById(planId)).thenThrow(new IllegalArgumentException("Plan not found"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            informationStatsService.getInformationStats(planId, startTime, endTime);
        });
    }
}
