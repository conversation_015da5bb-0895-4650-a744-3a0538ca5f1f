package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.util.AlertResultMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Alert Search Service Implementation Test
 * 告警搜索服务实现单元测试
 */
@ExtendWith(MockitoExtension.class)
class AlertSearchServiceImplTest {

        @Mock
        private AlertResultRepository alertResultRepository;

        @Mock
        private AlertResultMapper alertResultMapper;

        @InjectMocks
        private AlertSearchServiceImpl alertSearchService;

        private AlertResult testAlertResult;
        private AlertResultResponseDto testAlertResponseDto;
        private AlertSearchCriteriaDto testCriteria;

        @BeforeEach
        void setUp() {
                // 设置测试告警结果
                testAlertResult = new AlertResult();
                testAlertResult.setId(1L);
                testAlertResult.setConfigurationId(1L);
                testAlertResult.setPlanId(100L);
                testAlertResult.setEnterpriseId("enterprise-001");
                testAlertResult.setTitle("测试告警");
                testAlertResult.setContent("测试内容");
                testAlertResult.setWarningLevel(AlertResult.WarningLevel.GENERAL);
                testAlertResult.setSource("微博");
                testAlertResult.setWarningTime(LocalDateTime.now());

                // 设置测试响应DTO
                testAlertResponseDto = new AlertResultResponseDto(
                                1L, "enterprise-001", 100L, 1L, "测试告警", "测试内容",
                                "[{\"keyword\":\"关键词1\",\"count\":3}]",
                                InformationSensitivityType.NON_SENSITIVE.getValue(),
                                ContentCategory.ORIGINAL.getValue(),
                                SourceType.WEIBO.getValue(),
                                ContentType.TEXT.getValue(),
                                ContentMatchType.MAIN_TXT.getValue(),
                                MediaLevel.SMALL_MEDIUM.getValue(),
                                AlertResult.WarningLevel.GENERAL.name(),
                                "微博", "北京", LocalDateTime.now(), 1, "content_123456",
                                Map.of(), LocalDateTime.now(), LocalDateTime.now());

                // 设置测试搜索条件
                testCriteria = new AlertSearchCriteriaDto(
                                100L, null, null, null, null,
                                LocalDateTime.now().minusDays(7), LocalDateTime.now(),
                                null, 1, 20);
        }

        @Test
        void testSearchAlerts_ShouldReturnResults_WhenCriteriaMatches() {
                // 准备测试数据
                Page<AlertResult> mockPage = new PageImpl<>(List.of(testAlertResult),
                                PageRequest.of(0, 20), 1);

                // 模拟依赖服务
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenReturn(mockPage);
                when(alertResultMapper.toResponseDto(testAlertResult))
                                .thenReturn(testAlertResponseDto);

                // 执行测试
                AlertSearchResultDto result = alertSearchService.searchAlerts(testCriteria);

                // 验证结果
                assertNotNull(result);
                assertEquals(1, result.content().size());
                assertEquals(testAlertResponseDto, result.content().get(0));
                assertEquals(1L, result.pageInfo().totalElements());
                assertEquals(1, result.pageInfo().totalPages());
                assertEquals(0, result.pageInfo().currentPage());
                assertEquals(20, result.pageInfo().pageSize());

                // 验证方法调用
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any());
                verify(alertResultMapper).toResponseDto(testAlertResult);
        }

        @Test
        void testSearchAlerts_ShouldReturnEmpty_WhenNoResults() {
                // 准备测试数据
                Page<AlertResult> mockPage = new PageImpl<>(List.of(), PageRequest.of(0, 20), 0);

                // 模拟依赖服务
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenReturn(mockPage);

                // 执行测试
                AlertSearchResultDto result = alertSearchService.searchAlerts(testCriteria);

                // 验证结果
                assertNotNull(result);
                assertTrue(result.content().isEmpty());
                assertEquals(0L, result.pageInfo().totalElements());
                assertEquals(0, result.pageInfo().totalPages());

                // 验证方法调用
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any());
                verify(alertResultMapper, never()).toResponseDto(any());
        }

        @Test
        void testSearchAlerts_ShouldHandleException_WhenRepositoryFails() {
                // 模拟依赖服务抛出异常
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenThrow(new RuntimeException("Database error"));

                // 执行测试并验证异常
                assertThrows(RuntimeException.class, () -> {
                        alertSearchService.searchAlerts(testCriteria);
                });

                // 验证方法调用
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any());
                verify(alertResultMapper, never()).toResponseDto(any());
        }

        @Test
        void testSearchAlerts_ShouldHandleNullCriteria() {
                // 执行测试并验证异常
                assertThrows(Exception.class, () -> {
                        alertSearchService.searchAlerts(null);
                });
        }

        @Test
        void testSearchAlerts_ShouldHandleMultipleResults() {
                // 准备测试数据
                AlertResult secondAlert = new AlertResult();
                secondAlert.setId(2L);
                secondAlert.setConfigurationId(2L);
                secondAlert.setPlanId(100L);
                secondAlert.setEnterpriseId("enterprise-001");
                secondAlert.setTitle("第二个告警");
                secondAlert.setContent("第二个内容");
                secondAlert.setWarningLevel(AlertResult.WarningLevel.SEVERE);
                secondAlert.setSource("微信");
                secondAlert.setWarningTime(LocalDateTime.now());

                AlertResultResponseDto secondResponseDto = new AlertResultResponseDto(
                                2L, "enterprise-001", 100L, 2L, "第二个告警", "第二个内容",
                                "[{\"keyword\":\"关键词2\",\"count\":2}]",
                                InformationSensitivityType.SENSITIVE.getValue(),
                                ContentCategory.FORWARD.getValue(),
                                SourceType.WECHAT.getValue(),
                                ContentType.TEXT.getValue(),
                                ContentMatchType.MAIN_TXT.getValue(),
                                MediaLevel.PROVINCIAL.getValue(),
                                AlertResult.WarningLevel.SEVERE.name(),
                                "微信", "上海", LocalDateTime.now(), 2, "content_789012",
                                Map.of(), LocalDateTime.now(), LocalDateTime.now());

                Page<AlertResult> mockPage = new PageImpl<>(List.of(testAlertResult, secondAlert),
                                PageRequest.of(0, 20), 2);

                // 模拟依赖服务
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenReturn(mockPage);
                when(alertResultMapper.toResponseDto(testAlertResult))
                                .thenReturn(testAlertResponseDto);
                when(alertResultMapper.toResponseDto(secondAlert))
                                .thenReturn(secondResponseDto);

                // 执行测试
                AlertSearchResultDto result = alertSearchService.searchAlerts(testCriteria);

                // 验证结果
                assertNotNull(result);
                assertEquals(2, result.content().size());
                assertEquals(testAlertResponseDto, result.content().get(0));
                assertEquals(secondResponseDto, result.content().get(1));
                assertEquals(2L, result.pageInfo().totalElements());
                assertEquals(1, result.pageInfo().totalPages());

                // 验证方法调用
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any());
                verify(alertResultMapper).toResponseDto(testAlertResult);
                verify(alertResultMapper).toResponseDto(secondAlert);
        }

        @Test
        void testSearchAlerts_WithSourceFilter_ShouldReturnFilteredResults() {
                // 准备测试数据 - 只有微博来源的告警
                AlertSearchCriteriaDto criteriaWithSource = new AlertSearchCriteriaDto(
                                100L, null, null, "WEIBO", null,
                                LocalDateTime.now().minusDays(7), LocalDateTime.now(),
                                null, 1, 20);

                Page<AlertResult> mockPage = new PageImpl<>(List.of(testAlertResult), PageRequest.of(0, 20), 1);

                // 模拟依赖服务 - 使用更宽松的匹配
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(String.class),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenReturn(mockPage);
                when(alertResultMapper.toResponseDto(testAlertResult)).thenReturn(testAlertResponseDto);

                // 执行测试
                AlertSearchResultDto result = alertSearchService.searchAlerts(criteriaWithSource);

                // 验证结果
                assertNotNull(result);
                assertEquals(1, result.content().size());
                assertEquals(testAlertResponseDto, result.content().get(0));

                // 验证方法调用 - 确保source参数正确传递
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                eq(100L), eq(null), eq(null), eq("WEIBO"),
                                any(LocalDateTime.class), any(LocalDateTime.class), eq(null), any());
                verify(alertResultMapper).toResponseDto(testAlertResult);
        }

        @Test
        void testSearchAlerts_WithNullSource_ShouldReturnAllResults() {
                // 准备测试数据 - 使用null来源（表示不过滤）
                AlertSearchCriteriaDto criteriaWithNullSource = new AlertSearchCriteriaDto(
                                100L, null, null, null, null,
                                LocalDateTime.now().minusDays(7), LocalDateTime.now(),
                                null, 1, 20);

                Page<AlertResult> mockPage = new PageImpl<>(List.of(testAlertResult), PageRequest.of(0, 20), 1);

                // 模拟依赖服务 - 使用更宽松的匹配
                when(alertResultRepository.findWithFiltersAndFullTextSearch(
                                any(Long.class), any(), any(), any(),
                                any(LocalDateTime.class), any(LocalDateTime.class), any(), any()))
                                .thenReturn(mockPage);
                when(alertResultMapper.toResponseDto(testAlertResult)).thenReturn(testAlertResponseDto);

                // 执行测试
                AlertSearchResultDto result = alertSearchService.searchAlerts(criteriaWithNullSource);

                // 验证结果
                assertNotNull(result);
                assertEquals(1, result.content().size());

                // 验证方法调用 - 确保null参数正确传递
                verify(alertResultRepository).findWithFiltersAndFullTextSearch(
                                eq(100L), eq(null), eq(null), eq(null),
                                any(LocalDateTime.class), any(LocalDateTime.class), eq(null), any());
        }
}
