package com.czb.hn.service.business.impl;

import com.czb.hn.service.business.ReceptionRulesEngine;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for RecipientInfo serialization/deserialization
 * This test verifies that the Jackson configuration properly handles
 * the "enabledMethodsCount" field issue during JSON processing
 */
@DisplayName("RecipientInfo Jackson Serialization Tests")
class AlertNotificationSchedulerImplSerializationTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        // Create ObjectMapper with the same configuration as Spring Boot
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);
    }

    @Test
    @DisplayName("Test RecipientInfo serialization and deserialization")
    void testRecipientInfoSerialization() throws Exception {
        // Create test recipients
        List<ReceptionRulesEngine.RecipientInfo> originalRecipients = List.of(
                new ReceptionRulesEngine.RecipientInfo(
                        "John Doe",
                        "<EMAIL>",
                        null,
                        "John Doe",
                        true, // emailEnabled
                        false // smsEnabled
                ),
                new ReceptionRulesEngine.RecipientInfo(
                        "Jane Smith",
                        null,
                        "13800138000",
                        "Jane Smith",
                        false, // emailEnabled
                        true // smsEnabled
                ));

        // Test serialization directly with ObjectMapper
        String json = objectMapper.writeValueAsString(originalRecipients);
        assertNotNull(json);
        assertFalse(json.isEmpty());

        // Verify that the JSON contains the enabledMethodsCount field
        // (this is expected during serialization due to the getter method)
        assertTrue(json.contains("enabledMethodsCount"));

        // Test deserialization directly with ObjectMapper
        List<ReceptionRulesEngine.RecipientInfo> deserializedRecipients = objectMapper.readValue(json,
                objectMapper.getTypeFactory().constructCollectionType(List.class,
                        ReceptionRulesEngine.RecipientInfo.class));

        // Verify deserialization worked correctly
        assertNotNull(deserializedRecipients);
        assertEquals(2, deserializedRecipients.size());

        // Verify first recipient (email enabled)
        ReceptionRulesEngine.RecipientInfo emailRecipient = deserializedRecipients.get(0);
        assertEquals("John Doe", emailRecipient.name());
        assertEquals("<EMAIL>", emailRecipient.email());
        assertNull(emailRecipient.phone());
        assertTrue(emailRecipient.emailEnabled());
        assertFalse(emailRecipient.smsEnabled());
        assertEquals(1, emailRecipient.getEnabledMethodsCount());

        // Verify second recipient (SMS enabled)
        ReceptionRulesEngine.RecipientInfo smsRecipient = deserializedRecipients.get(1);
        assertEquals("Jane Smith", smsRecipient.name());
        assertNull(smsRecipient.email());
        assertEquals("13800138000", smsRecipient.phone());
        assertFalse(smsRecipient.emailEnabled());
        assertTrue(smsRecipient.smsEnabled());
        assertEquals(1, smsRecipient.getEnabledMethodsCount());
    }

    @Test
    @DisplayName("Test deserialization with enabledMethodsCount field in JSON")
    void testDeserializationWithEnabledMethodsCount() throws Exception {
        // Create JSON string that includes the enabledMethodsCount field
        // This simulates the problematic JSON that was causing the error
        String jsonWithEnabledMethodsCount = """
                [
                    {
                        "name": "John Doe",
                        "email": "<EMAIL>",
                        "phone": null,
                        "username": "John Doe",
                        "smsEnabled": false,
                        "emailEnabled": true,
                        "enabledMethodsCount": 1
                    },
                    {
                        "name": "Jane Smith",
                        "email": null,
                        "phone": "13800138000",
                        "username": "Jane Smith",
                        "smsEnabled": true,
                        "emailEnabled": false,
                        "enabledMethodsCount": 1
                    }
                ]
                """;

        // This should not throw an exception anymore due to the Jackson configuration
        List<ReceptionRulesEngine.RecipientInfo> recipients = objectMapper.readValue(jsonWithEnabledMethodsCount,
                objectMapper.getTypeFactory().constructCollectionType(List.class,
                        ReceptionRulesEngine.RecipientInfo.class));

        assertNotNull(recipients);
        assertEquals(2, recipients.size());

        // Verify the recipients were deserialized correctly
        ReceptionRulesEngine.RecipientInfo emailRecipient = recipients.get(0);
        assertEquals("John Doe", emailRecipient.name());
        assertTrue(emailRecipient.emailEnabled());
        assertFalse(emailRecipient.smsEnabled());

        ReceptionRulesEngine.RecipientInfo smsRecipient = recipients.get(1);
        assertEquals("Jane Smith", smsRecipient.name());
        assertFalse(smsRecipient.emailEnabled());
        assertTrue(smsRecipient.smsEnabled());
    }

    @Test
    @DisplayName("Test empty recipients list serialization")
    void testEmptyRecipientsSerialization() throws Exception {
        List<ReceptionRulesEngine.RecipientInfo> emptyRecipients = List.of();

        String json = objectMapper.writeValueAsString(emptyRecipients);
        assertEquals("[]", json);

        List<ReceptionRulesEngine.RecipientInfo> deserializedRecipients = objectMapper.readValue(json,
                objectMapper.getTypeFactory().constructCollectionType(List.class,
                        ReceptionRulesEngine.RecipientInfo.class));
        assertNotNull(deserializedRecipients);
        assertTrue(deserializedRecipients.isEmpty());
    }
}
