package com.czb.hn.service.business.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.alert.config.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertRuleEngine;
import com.czb.hn.service.business.KeywordTrackingService;
import com.czb.hn.util.AlertResultMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.test.util.ReflectionTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Alert Processing Service Implementation Test
 * 告警处理服务实现单元测试
 */
@ExtendWith(MockitoExtension.class)
class AlertProcessingServiceImplTest {

        @Mock
        private AlertConfigurationConsumerService alertConfigConsumerService;

        @Mock
        private AlertRuleEngine ruleEngine;

        @Mock
        private ElasticsearchClient elasticsearchClient;

        @Mock
        private AlertResultRepository alertResultRepository;

        @Mock
        private AlertResultMapper alertResultMapper;

        @Mock
        private KeywordTrackingService keywordTrackingService;

        @Mock
        private ObjectMapper objectMapper;

        @InjectMocks
        private AlertProcessingServiceImpl alertProcessingService;

        private AlertConfigurationResponseDto testConfig;
        private SinaNewsDocument testDocument;
        private AlertResult testAlertResult;
        private AlertResultResponseDto testAlertResponseDto;

        @BeforeEach
        void setUp() {
                // 设置 @Value 注解的字段值
                ReflectionTestUtils.setField(alertProcessingService, "batchSize", 100);
                ReflectionTestUtils.setField(alertProcessingService, "elasticsearchIndex", "sina_news");
                ReflectionTestUtils.setField(alertProcessingService, "timeWindowHours", 24);

                // 创建简单的测试配置，使用 null 值但确保 AlertRuleEngine 能处理
                AlertKeywordsDto alertKeywords = new AlertKeywordsDto(
                                List.of("测试关键词", "重要新闻"), "测试关键词配置");

                // 设置测试配置 - 其他字段使用 null，让 AlertRuleEngine 的默认处理逻辑生效
                testConfig = new AlertConfigurationResponseDto(
                                1L, "测试配置", "测试描述", 100L, "enterprise-001", true,
                                alertKeywords, null, null, null, null,
                                LocalDateTime.now(), LocalDateTime.now(), "testUser", "testUser", 1, true,
                                LocalDateTime.now());

                // 设置测试文档
                testDocument = new SinaNewsDocument();
                testDocument.setContentId("doc-001");
                testDocument.setTitle("测试标题");
                testDocument.setContent("测试内容");
                testDocument.setSource("微博");
                testDocument.setPublishTime(LocalDateTime.now());
                testDocument.setSensitivityType(3); // 设置敏感度类型
                testDocument.setIsOriginal(1); // 设置是否原创
                testDocument.setSimilarityNum(5L); // 设置相似文章数量

                // 设置测试告警结果
                testAlertResult = new AlertResult();
                testAlertResult.setId(1L);
                testAlertResult.setConfigurationId(1L);
                testAlertResult.setEnterpriseId("enterprise-001");
                testAlertResult.setTitle("测试告警");

                // 设置测试响应DTO
                testAlertResponseDto = new AlertResultResponseDto(
                                1L, "enterprise-001", 100L, 1L, "测试告警", "测试内容",
                                "[{\"keyword\":\"关键词1\",\"count\":3}]",
                                InformationSensitivityType.NON_SENSITIVE.getValue(),
                                ContentCategory.ORIGINAL.getValue(),
                                SourceType.WEIBO.getValue(),
                                ContentType.TEXT.getValue(),
                                ContentMatchType.MAIN_TXT.getValue(),
                                MediaLevel.SMALL_MEDIUM.getValue(),
                                AlertResult.WarningLevel.GENERAL.name(),
                                "微博", "北京", LocalDateTime.now(), 1, "doc-001", null, LocalDateTime.now(),
                                LocalDateTime.now());
        }

        @Test
        void testProcessEnterpriseAlerts_ShouldReturnResults_WhenConfigurationsExist() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(List.of(testDocument));

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(configs);

                // 让规则评估返回 true，测试完整的告警创建流程
                lenient().when(ruleEngine.evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(true);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果 - 应该生成一个告警
                assertNotNull(results);
                assertEquals(1, results.size());
                assertEquals(testAlertResponseDto, results.get(0));

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine).evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class));
                // 应该保存告警
                verify(alertResultRepository).save(any(AlertResult.class));
        }

        @Test
        void testProcessEnterpriseAlerts_ShouldReturnEmpty_WhenNoConfigurations() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";

                // 模拟无配置情况
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(List.of());

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果
                assertNotNull(results);
                assertTrue(results.isEmpty());

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
                // 当没有配置时，不应该调用 Elasticsearch
                verify(elasticsearchClient, never()).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
        }

        @Test
        void testProcessPlanAlerts_ShouldReturnResults_WhenPlanExists() throws Exception {
                // 准备测试数据
                Long planId = 100L;
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(List.of(testDocument));

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByPlan(planId))
                                .thenReturn(configs);
                lenient().when(ruleEngine.evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(true);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processPlanAlerts(planId);

                // 验证结果
                assertNotNull(results);
                assertEquals(1, results.size());
                assertEquals(testAlertResponseDto, results.get(0));

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByPlan(planId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine).evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class));
                verify(alertResultRepository).save(any(AlertResult.class));
        }

        @Test
        void testProcessConfigurationAlerts_ShouldReturnResults_WhenConfigurationExists() throws Exception {
                // 准备测试数据
                Long configurationId = 1L;

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(List.of(testDocument));

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationById(configurationId))
                                .thenReturn(java.util.Optional.of(testConfig));
                lenient().when(ruleEngine.evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(true);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService
                                .processConfigurationAlerts(configurationId);

                // 验证结果
                assertNotNull(results);
                assertEquals(1, results.size());
                assertEquals(testAlertResponseDto, results.get(0));

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationById(configurationId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine).evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class));
                verify(alertResultRepository).save(any(AlertResult.class));
        }

        @Test
        void testProcessConfigurationAlerts_ShouldReturnEmpty_WhenConfigurationNotFound() throws Exception {
                // 准备测试数据
                Long configurationId = 1L;

                // 模拟配置不存在
                when(alertConfigConsumerService.getActiveConfigurationById(configurationId))
                                .thenReturn(java.util.Optional.empty());

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService
                                .processConfigurationAlerts(configurationId);

                // 验证结果
                assertNotNull(results);
                assertTrue(results.isEmpty());

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationById(configurationId);
                // 当配置不存在时，不应该调用 Elasticsearch
                verify(elasticsearchClient, never()).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
        }

        @Test
        void testProcessAlerts_ShouldNotCreateAlert_WhenRuleDoesNotMatch() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(List.of(testDocument));

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(configs);
                when(ruleEngine.evaluateRule(any(), eq(testDocument)))
                                .thenReturn(false); // 规则不匹配

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果
                assertNotNull(results);
                assertTrue(results.isEmpty());

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine).evaluateRule(any(), eq(testDocument));
                verify(alertResultRepository, never()).save(any(AlertResult.class));
        }

        @Test
        void testProcessAlerts_ShouldHandleException_WhenProcessingFails() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                // 设置 Elasticsearch 模拟
                setupElasticsearchMocks(List.of(testDocument));

                // 设置通用模拟，但让 toEntity 抛出异常
                lenient().when(alertResultMapper.getObjectMapper()).thenReturn(objectMapper);
                lenient().when(objectMapper.writeValueAsString(any())).thenReturn("[]");
                lenient().when(alertResultRepository.existsByEnterpriseIdAndOriginalContentIdAndConfigurationId(
                                any(String.class), any(String.class), any(Long.class)))
                                .thenReturn(false);
                lenient().when(alertResultMapper.toEntity(any(AlertConfigurationResponseDto.class)))
                                .thenThrow(new RuntimeException("处理异常"));

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(configs);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果 - 应该继续处理其他文档，不会因为异常而中断
                assertNotNull(results);
                assertTrue(results.isEmpty());

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(alertResultMapper).toEntity(any(AlertConfigurationResponseDto.class));
        }

        @Test
        void testScheduledProcessAlerts_ShouldProcessAllEnterprises() throws Exception {
                // 准备测试数据
                List<AlertConfigurationResponseDto> allConfigs = List.of(testConfig);

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(List.of(testDocument));

                // 模拟依赖服务 - 需要模拟两次调用：一次用于 getActiveEnterprises，一次用于 processEnterpriseAlerts
                when(alertConfigConsumerService.getAllActiveConfigurations())
                                .thenReturn(allConfigs);
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise("enterprise-001"))
                                .thenReturn(allConfigs);
                lenient().when(ruleEngine.evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(true);

                // 执行测试
                assertDoesNotThrow(() -> alertProcessingService.processAlerts());

                // 验证方法调用
                verify(alertConfigConsumerService).getAllActiveConfigurations();
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise("enterprise-001");
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine).evaluateRule(any(AlertConfiguration.class),
                                any(SinaNewsDocument.class));
                verify(alertResultRepository).save(any(AlertResult.class));
        }

        @Test
        void testProcessAlerts_ShouldHandleEmptyDocuments() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                // 设置 Elasticsearch 模拟 - 空结果
                setupElasticsearchMocks(List.of());

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(configs);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果
                assertNotNull(results);
                assertTrue(results.isEmpty());

                // 验证方法调用
                verify(alertConfigConsumerService).getActiveConfigurationsByEnterprise(enterpriseId);
                verify(elasticsearchClient).search(any(SearchRequest.class), eq(SinaNewsDocument.class));
                verify(ruleEngine, never()).evaluateRule(any(), any());
                verify(alertResultRepository, never()).save(any(AlertResult.class));
        }

        @Test
        void testProcessAlerts_ShouldHandleMultipleDocuments() throws Exception {
                // 准备测试数据
                String enterpriseId = "enterprise-001";
                List<AlertConfigurationResponseDto> configs = List.of(testConfig);

                SinaNewsDocument doc1 = new SinaNewsDocument();
                doc1.setContentId("doc-001");
                doc1.setTitle("文档1");

                SinaNewsDocument doc2 = new SinaNewsDocument();
                doc2.setContentId("doc-002");
                doc2.setTitle("文档2");

                List<SinaNewsDocument> documents = List.of(doc1, doc2);

                // 设置通用模拟
                setupCommonMocks();
                setupElasticsearchMocks(documents);

                // 模拟依赖服务
                when(alertConfigConsumerService.getActiveConfigurationsByEnterprise(enterpriseId))
                                .thenReturn(configs);
                when(ruleEngine.evaluateRule(any(), eq(doc1)))
                                .thenReturn(true);
                when(ruleEngine.evaluateRule(any(), eq(doc2)))
                                .thenReturn(false);

                // 执行测试
                List<AlertResultResponseDto> results = alertProcessingService.processEnterpriseAlerts(enterpriseId);

                // 验证结果 - 只有一个文档匹配规则
                assertNotNull(results);
                assertEquals(1, results.size());

                // 验证方法调用
                verify(ruleEngine).evaluateRule(any(), eq(doc1));
                verify(ruleEngine).evaluateRule(any(), eq(doc2));
                verify(alertResultRepository, times(1)).save(any(AlertResult.class));
        }

        /**
         * 创建模拟的 Elasticsearch 搜索响应
         */
        @SuppressWarnings("unchecked")
        private SearchResponse<SinaNewsDocument> mockElasticsearchResponse(List<SinaNewsDocument> documents) {
                SearchResponse<SinaNewsDocument> mockResponse = mock(SearchResponse.class);
                HitsMetadata<SinaNewsDocument> mockHits = mock(HitsMetadata.class);

                List<Hit<SinaNewsDocument>> hits = documents.stream()
                                .map(doc -> {
                                        Hit<SinaNewsDocument> hit = mock(Hit.class);
                                        when(hit.source()).thenReturn(doc);
                                        return hit;
                                })
                                .toList();

                when(mockHits.hits()).thenReturn(hits);
                when(mockResponse.hits()).thenReturn(mockHits);

                return mockResponse;
        }

        /**
         * 设置通用的模拟服务
         */
        private void setupCommonMocks() throws Exception {
                // 模拟 ObjectMapper 和 JSON 序列化
                lenient().when(alertResultMapper.getObjectMapper()).thenReturn(objectMapper);
                lenient().when(objectMapper.writeValueAsString(any()))
                                .thenReturn("[{\"keyword\":\"关键词1\",\"count\":3}]");

                // 模拟实体转换 - 创建一个完整的 AlertConfiguration 对象，包含有效的 JSON 字符串
                AlertConfiguration mockConfig = new AlertConfiguration();
                mockConfig.setId(1L);
                mockConfig.setName("测试配置");
                mockConfig.setEnterpriseId("enterprise-001");
                mockConfig.setEnabled(true);
                // 设置有效的 JSON 字符串，避免解析问题
                mockConfig.setAlertKeywords("{\"keywords\":[\"测试关键词\"],\"description\":\"测试关键词配置\"}");
                mockConfig.setContentSettings("{\"sensitivityType\":\"ALL\"}");
                mockConfig.setThresholdSettings("{\"conditionRelation\":\"OR\"}");
                mockConfig.setLevelSettings("{\"sourceLevel\":{}}");
                mockConfig.setReceptionSettings("{\"receptionTime\":\"DAILY\"}");
                lenient().when(alertResultMapper.toEntity(any(AlertConfigurationResponseDto.class)))
                                .thenReturn(mockConfig);

                // 模拟关键词跟踪服务 - 确保所有重载方法都被模拟
                lenient().when(keywordTrackingService.extractInvolvedKeywords(any(), any()))
                                .thenReturn(List.of("关键词1"));
                lenient().when(keywordTrackingService.extractInvolvedKeywords(any(AlertConfigurationResponseDto.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(List.of("关键词1"));

                // 模拟规则引擎 - 确保所有重载方法都被模拟
                lenient().when(ruleEngine.determineWarningLevel(any(), any(SinaNewsDocument.class)))
                                .thenReturn(AlertResult.WarningLevel.GENERAL);
                lenient().when(ruleEngine.determineWarningLevel(any(LevelSettingsDto.class),
                                any(SinaNewsDocument.class)))
                                .thenReturn(AlertResult.WarningLevel.GENERAL);

                // 模拟数据库操作 - 使用 any() 匹配器确保所有调用都被拦截
                lenient().when(alertResultRepository.existsByEnterpriseIdAndOriginalContentIdAndConfigurationId(
                                any(String.class), any(String.class), any(Long.class)))
                                .thenReturn(false);
                lenient().when(alertResultRepository.save(any(AlertResult.class))).thenReturn(testAlertResult);

                // 模拟响应 DTO 转换
                lenient().when(alertResultMapper.toResponseDto(any(AlertResult.class)))
                                .thenReturn(testAlertResponseDto);
        }

        /**
         * 设置 Elasticsearch 模拟
         */
        private void setupElasticsearchMocks(List<SinaNewsDocument> documents) throws Exception {
                SearchResponse<SinaNewsDocument> mockSearchResponse = mockElasticsearchResponse(documents);
                // 使用 lenient 模式避免严格验证
                lenient().when(elasticsearchClient.search(any(SearchRequest.class), eq(SinaNewsDocument.class)))
                                .thenReturn(mockSearchResponse);
        }
}
