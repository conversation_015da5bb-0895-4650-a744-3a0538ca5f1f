package com.czb.hn.service.business;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.workbench.InformationStatsDTO;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.impl.InformationStatsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 信息统计服务性能测试
 * 验证批量查询优化的效果
 */
@ExtendWith(MockitoExtension.class)
class InformationStatsServicePerformanceTest {

    @Mock
    private ElasticsearchSearchService elasticsearchSearchService;

    @Mock
    private AlertResultRepository alertResultRepository;

    @Mock
    private PlanService planService;

    @InjectMocks
    private InformationStatsServiceImpl informationStatsService;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String enterpriseId;

    @BeforeEach
    void setUp() {
        startTime = LocalDateTime.of(2024, 1, 15, 10, 0, 0);
        endTime = LocalDateTime.of(2024, 1, 15, 18, 0, 0);
        enterpriseId = "enterprise123";
    }

    @Test
    void testPerformanceWith10Plans() {
        // 准备10个方案的测试数据
        List<PlanDTO> plans = createMockPlans(10);
        setupMockResponses(plans);

        long startTimeMs = System.currentTimeMillis();
        
        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime, endTime);
        
        long endTimeMs = System.currentTimeMillis();
        long executionTime = endTimeMs - startTimeMs;

        // 验证结果
        assertNotNull(result);
        assertEquals(10000L, result.totalInformationCount()); // 10 plans * 1000 each
        assertEquals(500L, result.sensitiveInformationCount()); // 10 plans * 50 each
        assertEquals(100L, result.alertCount()); // 10 plans * 10 each

        // 验证批量查询被调用
        verify(elasticsearchSearchService, times(1)).batchCountInformationTotal(any(), anyString(), anyString());
        verify(elasticsearchSearchService, times(1)).batchCountSensitiveInformationTotal(any(), anyString(), anyString());
        verify(alertResultRepository, times(1)).batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any());

        System.out.println("10 plans execution time: " + executionTime + "ms");
        assertTrue(executionTime < 1000, "Should complete within 1 second for 10 plans");
    }

    @Test
    void testPerformanceWith100Plans() {
        // 准备100个方案的测试数据
        List<PlanDTO> plans = createMockPlans(100);
        setupMockResponses(plans);

        long startTimeMs = System.currentTimeMillis();
        
        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime, endTime);
        
        long endTimeMs = System.currentTimeMillis();
        long executionTime = endTimeMs - startTimeMs;

        // 验证结果
        assertNotNull(result);
        assertEquals(100000L, result.totalInformationCount()); // 100 plans * 1000 each
        assertEquals(5000L, result.sensitiveInformationCount()); // 100 plans * 50 each
        assertEquals(1000L, result.alertCount()); // 100 plans * 10 each

        // 验证批量查询被调用
        verify(elasticsearchSearchService, times(1)).batchCountInformationTotal(any(), anyString(), anyString());
        verify(elasticsearchSearchService, times(1)).batchCountSensitiveInformationTotal(any(), anyString(), anyString());
        verify(alertResultRepository, times(1)).batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any());

        System.out.println("100 plans execution time: " + executionTime + "ms");
        assertTrue(executionTime < 5000, "Should complete within 5 seconds for 100 plans");
    }

    @Test
    void testPerformanceWith1000Plans() {
        // 准备1000个方案的测试数据
        List<PlanDTO> plans = createMockPlans(1000);
        setupMockResponses(plans);

        long startTimeMs = System.currentTimeMillis();
        
        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime, endTime);
        
        long endTimeMs = System.currentTimeMillis();
        long executionTime = endTimeMs - startTimeMs;

        // 验证结果
        assertNotNull(result);
        assertEquals(1000000L, result.totalInformationCount()); // 1000 plans * 1000 each
        assertEquals(50000L, result.sensitiveInformationCount()); // 1000 plans * 50 each
        assertEquals(10000L, result.alertCount()); // 1000 plans * 10 each

        // 验证批量查询被调用
        verify(elasticsearchSearchService, times(1)).batchCountInformationTotal(any(), anyString(), anyString());
        verify(elasticsearchSearchService, times(1)).batchCountSensitiveInformationTotal(any(), anyString(), anyString());
        verify(alertResultRepository, times(1)).batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any());

        System.out.println("1000 plans execution time: " + executionTime + "ms");
        assertTrue(executionTime < 10000, "Should complete within 10 seconds for 1000 plans");
    }

    @Test
    void testBatchQueryFallbackToSingleQuery() {
        // 测试批量查询失败时的降级处理
        List<PlanDTO> plans = createMockPlans(5);
        
        when(planService.getPlansByEnterpriseId(enterpriseId)).thenReturn(plans);
        
        // 模拟批量查询失败
        when(elasticsearchSearchService.batchCountInformationTotal(any(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Batch query failed"));
        when(elasticsearchSearchService.batchCountSensitiveInformationTotal(any(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Batch query failed"));
        when(alertResultRepository.batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any()))
                .thenThrow(new RuntimeException("Batch query failed"));
        
        // 模拟单个查询成功
        when(elasticsearchSearchService.countInformationTotal(anyLong(), anyString(), anyString())).thenReturn(1000L);
        when(elasticsearchSearchService.countSensitiveInformationTotal(anyLong(), anyString(), anyString())).thenReturn(50L);
        when(alertResultRepository.countByPlanIdAndWarningTimeBetween(anyLong(), any(), any())).thenReturn(10L);

        // 执行测试
        InformationStatsDTO result = informationStatsService.getEnterpriseInformationStats(enterpriseId, startTime, endTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(5000L, result.totalInformationCount()); // 5 plans * 1000 each
        assertEquals(250L, result.sensitiveInformationCount()); // 5 plans * 50 each
        assertEquals(50L, result.alertCount()); // 5 plans * 10 each

        // 验证降级到单个查询
        verify(elasticsearchSearchService, times(5)).countInformationTotal(anyLong(), anyString(), anyString());
        verify(elasticsearchSearchService, times(5)).countSensitiveInformationTotal(anyLong(), anyString(), anyString());
        verify(alertResultRepository, times(5)).countByPlanIdAndWarningTimeBetween(anyLong(), any(), any());
    }

    private List<PlanDTO> createMockPlans(int count) {
        return IntStream.range(1, count + 1)
                .mapToObj(i -> new PlanDTO(
                        (long) i,
                        "方案" + i,
                        "描述" + i,
                        "关键词" + i,
                        null,
                        null,
                        LocalDateTime.now(),
                        LocalDateTime.now()
                ))
                .toList();
    }

    private void setupMockResponses(List<PlanDTO> plans) {
        when(planService.getPlansByEnterpriseId(enterpriseId)).thenReturn(plans);

        // 模拟批量查询响应
        Map<Long, Long> totalCounts = new HashMap<>();
        Map<Long, Long> sensitiveCounts = new HashMap<>();
        List<Object[]> alertCounts = new ArrayList<>();

        for (PlanDTO plan : plans) {
            totalCounts.put(plan.id(), 1000L);
            sensitiveCounts.put(plan.id(), 50L);
            alertCounts.add(new Object[]{plan.id(), 10L});
        }

        when(elasticsearchSearchService.batchCountInformationTotal(any(), anyString(), anyString()))
                .thenReturn(totalCounts);
        when(elasticsearchSearchService.batchCountSensitiveInformationTotal(any(), anyString(), anyString()))
                .thenReturn(sensitiveCounts);
        when(alertResultRepository.batchCountByPlanIdsAndWarningTimeBetween(any(), any(), any()))
                .thenReturn(alertCounts);
    }
}
