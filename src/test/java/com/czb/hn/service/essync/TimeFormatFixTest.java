package com.czb.hn.service.essync;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.util.DateTimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;

/**
 * 测试时间格式修复功能
 * 验证ES同步时的时间格式处理
 */
@DisplayName("Time Format Fix Tests")
class TimeFormatFixTest {

    @Test
    @DisplayName("Should handle valid LocalDateTime objects")
    void testValidLocalDateTime() {
        LocalDateTime validTime = LocalDateTime.of(2025, 7, 2, 11, 25, 16);
        
        // 验证DateTimeUtil能正确格式化有效的LocalDateTime
        String formatted = DateTimeUtil.formatToStandardString(validTime);
        assertNotNull(formatted);
        assertEquals("2025-07-02 11:25:16", formatted);
    }

    @Test
    @DisplayName("Should handle null LocalDateTime")
    void testNullLocalDateTime() {
        String formatted = DateTimeUtil.formatToStandardString(null);
        assertNull(formatted);
    }

    @Test
    @DisplayName("Should create SinaNewsDocument with valid time fields")
    void testSinaNewsDocumentTimeFields() {
        SinaNewsDocument document = new SinaNewsDocument();
        LocalDateTime testTime = LocalDateTime.of(2025, 7, 2, 11, 25, 16);
        
        document.setPublishTime(testTime);
        document.setCaptureTime(testTime);
        document.setProcessTime(testTime);
        
        // 验证时间字段设置正确
        assertNotNull(document.getPublishTime());
        assertNotNull(document.getCaptureTime());
        assertNotNull(document.getProcessTime());
        
        assertEquals(testTime, document.getPublishTime());
        assertEquals(testTime, document.getCaptureTime());
        assertEquals(testTime, document.getProcessTime());
    }

    @Test
    @DisplayName("Should parse various time formats correctly")
    void testTimeFormatParsing() {
        // 测试标准格式
        LocalDateTime result1 = DateTimeUtil.parseDateTime("2025-07-02 11:25:16");
        assertNotNull(result1);
        assertEquals(2025, result1.getYear());
        assertEquals(7, result1.getMonthValue());
        assertEquals(2, result1.getDayOfMonth());
        assertEquals(11, result1.getHour());
        assertEquals(25, result1.getMinute());
        assertEquals(16, result1.getSecond());

        // 测试ISO格式
        LocalDateTime result2 = DateTimeUtil.parseDateTime("2025-07-02T11:25:16");
        assertNotNull(result2);
        assertEquals(result1, result2); // 应该解析为相同的时间

        // 测试带毫秒的ISO格式
        LocalDateTime result3 = DateTimeUtil.parseDateTime("2025-07-02T11:25:16.123");
        assertNotNull(result3);
        assertEquals(result1, result3); // 毫秒部分应该被忽略，其他部分相同
    }

    @Test
    @DisplayName("Should handle invalid time formats gracefully")
    void testInvalidTimeFormats() {
        assertNull(DateTimeUtil.parseDateTime("invalid-format"));
        assertNull(DateTimeUtil.parseDateTime(""));
        assertNull(DateTimeUtil.parseDateTime(null));
        assertNull(DateTimeUtil.parseDateTime("   "));
    }

    @Test
    @DisplayName("Should convert time formats correctly")
    void testTimeFormatConversion() {
        // 测试ISO到标准格式的转换
        String converted = DateTimeUtil.convertToStandardFormat("2025-07-02T11:25:16");
        assertEquals("2025-07-02 11:25:16", converted);

        // 测试标准格式保持不变
        String unchanged = DateTimeUtil.convertToStandardFormat("2025-07-02 11:25:16");
        assertEquals("2025-07-02 11:25:16", unchanged);

        // 测试无效格式返回null
        String invalid = DateTimeUtil.convertToStandardFormat("invalid-format");
        assertNull(invalid);
    }
}
