package com.czb.hn.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 AlertResultMapper 中关键词处理的新功能
 */
class AlertResultMapperKeywordTest {

    private AlertResultMapper alertResultMapper;

    @BeforeEach
    void setUp() {
        alertResultMapper = new AlertResultMapper();
        // 使用反射注入ObjectMapper
        ReflectionTestUtils.setField(alertResultMapper, "objectMapper", new ObjectMapper());
    }

    @Test
    @DisplayName("测试序列化关键词和计数到JSON")
    void testSerializeInvolvedKeywordsWithCount() {
        // 准备测试数据
        Map<String, Integer> keywordCounts = new HashMap<>();
        keywordCounts.put("重要新闻", 5);
        keywordCounts.put("突发事件", 3);
        keywordCounts.put("安全预警", 2);

        // 执行序列化
        String json = alertResultMapper.serializeInvolvedKeywordsWithCount(keywordCounts);

        // 验证结果
        assertNotNull(json);
        assertFalse(json.isEmpty());
        assertTrue(json.contains("重要新闻"));
        assertTrue(json.contains("突发事件"));
        assertTrue(json.contains("安全预警"));
        assertTrue(json.contains("\"count\":5"));
        assertTrue(json.contains("\"count\":3"));
        assertTrue(json.contains("\"count\":2"));

        System.out.println("序列化结果: " + json);
    }

    @Test
    @DisplayName("测试从JSON解析关键词和计数")
    void testParseInvolvedKeywordsWithCount() {
        // 准备测试JSON
        String json = "[{\"keyword\":\"重要新闻\",\"count\":5},{\"keyword\":\"突发事件\",\"count\":3},{\"keyword\":\"安全预警\",\"count\":2}]";

        // 执行解析
        Map<String, Integer> result = alertResultMapper.parseInvolvedKeywordsWithCount(json);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(5, result.get("重要新闻"));
        assertEquals(3, result.get("突发事件"));
        assertEquals(2, result.get("安全预警"));

        System.out.println("解析结果: " + result);
    }

    @Test
    @DisplayName("测试空数据处理")
    void testEmptyData() {
        // 测试空Map
        String emptyJson = alertResultMapper.serializeInvolvedKeywordsWithCount(new HashMap<>());
        assertEquals("[]", emptyJson);

        // 测试null Map
        String nullJson = alertResultMapper.serializeInvolvedKeywordsWithCount(null);
        assertEquals("[]", nullJson);

        // 测试空JSON字符串
        Map<String, Integer> emptyResult = alertResultMapper.parseInvolvedKeywordsWithCount("");
        assertTrue(emptyResult.isEmpty());

        // 测试null JSON字符串
        Map<String, Integer> nullResult = alertResultMapper.parseInvolvedKeywordsWithCount(null);
        assertTrue(nullResult.isEmpty());
    }

    @Test
    @DisplayName("测试序列化和反序列化的一致性")
    void testSerializationConsistency() {
        // 准备原始数据
        Map<String, Integer> original = new HashMap<>();
        original.put("关键词1", 10);
        original.put("关键词2", 7);
        original.put("关键词3", 1);

        // 序列化
        String json = alertResultMapper.serializeInvolvedKeywordsWithCount(original);

        // 反序列化
        Map<String, Integer> parsed = alertResultMapper.parseInvolvedKeywordsWithCount(json);

        // 验证一致性
        assertEquals(original.size(), parsed.size());
        for (Map.Entry<String, Integer> entry : original.entrySet()) {
            assertEquals(entry.getValue(), parsed.get(entry.getKey()));
        }

        System.out.println("原始数据: " + original);
        System.out.println("JSON: " + json);
        System.out.println("解析数据: " + parsed);
    }

    @Test
    @DisplayName("测试单个关键词")
    void testSingleKeyword() {
        // 准备单个关键词数据
        Map<String, Integer> singleKeyword = Map.of("单个关键词", 1);

        // 序列化
        String json = alertResultMapper.serializeInvolvedKeywordsWithCount(singleKeyword);

        // 验证JSON格式
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        assertTrue(json.contains("单个关键词"));
        assertTrue(json.contains("\"count\":1"));

        // 反序列化验证
        Map<String, Integer> parsed = alertResultMapper.parseInvolvedKeywordsWithCount(json);
        assertEquals(1, parsed.size());
        assertEquals(1, parsed.get("单个关键词"));
    }

    @Test
    @DisplayName("测试中文关键词处理")
    void testChineseKeywords() {
        // 准备中文关键词数据
        Map<String, Integer> chineseKeywords = new HashMap<>();
        chineseKeywords.put("中文关键词", 5);
        chineseKeywords.put("测试数据", 3);
        chineseKeywords.put("JSON格式", 2);

        // 序列化和反序列化
        String json = alertResultMapper.serializeInvolvedKeywordsWithCount(chineseKeywords);
        Map<String, Integer> parsed = alertResultMapper.parseInvolvedKeywordsWithCount(json);

        // 验证中文处理正确
        assertEquals(chineseKeywords.size(), parsed.size());
        assertEquals(5, parsed.get("中文关键词"));
        assertEquals(3, parsed.get("测试数据"));
        assertEquals(2, parsed.get("JSON格式"));
    }
}
