package com.czb.hn.util;

import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContext;
import com.czb.hn.dto.user.LoginUserContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Unit tests for UserContext utility class
 */
@ExtendWith(MockitoExtension.class)
class UserContextTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpSession session;

    private LoginUser testUser;

    @BeforeEach
    void setUp() {
        // 清理ThreadLocal
        LoginUserContextHolder.clearContext();
        
        // 创建测试用户
        testUser = new LoginUser();
        testUser.setUserId("test-user-001");
        testUser.setPrimaryGroupId("enterprise-001");
        testUser.setName("Test User");
    }

    @Test
    void testGetCurrentUser_WithUserInThreadLocal_ShouldReturnUser() {
        // Given
        setupUserInThreadLocal();

        // When
        LoginUser result = UserContext.getCurrentUser();

        // Then
        assertNotNull(result);
        assertEquals("test-user-001", result.getUserId());
        assertEquals("enterprise-001", result.getPrimaryGroupId());
    }

    @Test
    void testGetCurrentUser_WithNoUserInThreadLocal_ShouldReturnNull() {
        // Given - no user in ThreadLocal

        // When
        LoginUser result = UserContext.getCurrentUser();

        // Then
        assertNull(result);
    }

    @Test
    void testGetCurrentUserId_WithUserInThreadLocal_ShouldReturnUserId() {
        // Given
        setupUserInThreadLocal();

        // When
        String result = UserContext.getCurrentUserId();

        // Then
        assertEquals("test-user-001", result);
    }

    @Test
    void testGetCurrentEnterpriseId_WithUserInThreadLocal_ShouldReturnEnterpriseId() {
        // Given
        setupUserInThreadLocal();

        // When
        String result = UserContext.getCurrentEnterpriseId();

        // Then
        assertEquals("enterprise-001", result);
    }

    @Test
    void testGetCurrentUserFromSession_WithUserInSession_ShouldReturnUser() {
        // Given
        when(request.getSession(false)).thenReturn(session);
        when(session.getAttribute("CURRENT_USER")).thenReturn(testUser);

        // When
        LoginUser result = UserContext.getCurrentUserFromSession(request);

        // Then
        assertNotNull(result);
        assertEquals("test-user-001", result.getUserId());
        assertEquals("enterprise-001", result.getPrimaryGroupId());
    }

    @Test
    void testGetCurrentUserFromSession_WithNoSession_ShouldReturnNull() {
        // Given
        when(request.getSession(false)).thenReturn(null);

        // When
        LoginUser result = UserContext.getCurrentUserFromSession(request);

        // Then
        assertNull(result);
    }

    @Test
    void testGetCurrentUserWithFallback_WithUserInThreadLocal_ShouldReturnFromThreadLocal() {
        // Given
        setupUserInThreadLocal();

        // When
        LoginUser result = UserContext.getCurrentUserWithFallback(request);

        // Then
        assertNotNull(result);
        assertEquals("test-user-001", result.getUserId());
        // Should not access session since ThreadLocal has user
    }

    @Test
    void testGetCurrentUserWithFallback_WithNoThreadLocalButSession_ShouldReturnFromSession() {
        // Given - no user in ThreadLocal, but user in session
        when(request.getSession(false)).thenReturn(session);
        when(session.getAttribute("CURRENT_USER")).thenReturn(testUser);

        // When
        LoginUser result = UserContext.getCurrentUserWithFallback(request);

        // Then
        assertNotNull(result);
        assertEquals("test-user-001", result.getUserId());
    }

    @Test
    void testGetCurrentEnterpriseIdWithFallback_WithUserInThreadLocal_ShouldReturnEnterpriseId() {
        // Given
        setupUserInThreadLocal();

        // When
        String result = UserContext.getCurrentEnterpriseIdWithFallback(request);

        // Then
        assertEquals("enterprise-001", result);
    }

    @Test
    void testGetCurrentEnterpriseIdWithFallback_WithNoUser_ShouldReturnNull() {
        // Given - no user anywhere
        when(request.getSession(false)).thenReturn(null);

        // When
        String result = UserContext.getCurrentEnterpriseIdWithFallback(request);

        // Then
        assertNull(result);
    }

    private void setupUserInThreadLocal() {
        LoginUserContext userContext = new LoginUserContext();
        userContext.setUser(testUser);
        LoginUserContextHolder.setContext(userContext);
    }
}
