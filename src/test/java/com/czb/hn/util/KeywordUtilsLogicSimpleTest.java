package com.czb.hn.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

@DisplayName("Keyword Logic Operator Simple Tests")
class KeywordUtilsLogicSimpleTest {

    @Test
    @DisplayName("Parse monitor keywords - OR logic")
    void testParseMonitorKeywords_OrLogic() {
        String keywords = "keyword1|keyword2|keyword3";
        KeywordUtils.MonitorKeywordGroup result = KeywordUtils.parseMonitorKeywords(keywords);

        assertEquals(3, result.getOrKeywords().size());
        assertTrue(result.getOrKeywords().contains("keyword1"));
        assertTrue(result.getOrKeywords().contains("keyword2"));
        assertTrue(result.getOrKeywords().contains("keyword3"));
        assertTrue(result.getAndGroups().isEmpty());
    }

    @Test
    @DisplayName("Parse monitor keywords - AND logic")
    void testParseMonitorKeywords_AndLogic() {
        String keywords = "keyword1+keyword2+keyword3";
        KeywordUtils.MonitorKeywordGroup result = KeywordUtils.parseMonitorKeywords(keywords);

        assertEquals(1, result.getAndGroups().size());
        List<String> andGroup = result.getAndGroups().get(0);
        assertEquals(3, andGroup.size());
        assertTrue(andGroup.contains("keyword1"));
        assertTrue(andGroup.contains("keyword2"));
        assertTrue(andGroup.contains("keyword3"));
        assertTrue(result.getOrKeywords().isEmpty());
    }

    @Test
    @DisplayName("Parse exclude keywords - OR logic")
    void testParseExcludeKeywords() {
        String keywords = "exclude1|exclude2|exclude3";
        KeywordUtils.ExcludeKeywordGroup result = KeywordUtils.parseExcludeKeywords(keywords);

        assertEquals(3, result.getKeywords().size());
        assertTrue(result.getKeywords().contains("exclude1"));
        assertTrue(result.getKeywords().contains("exclude2"));
        assertTrue(result.getKeywords().contains("exclude3"));
    }

    @Test
    @DisplayName("Escape character handling")
    void testUnescapeKeyword() {
        assertEquals("C+language", KeywordUtils.unescapeKeyword("C@@language"));
        assertEquals("Java+Script", KeywordUtils.unescapeKeyword("Java@@Script"));
        assertEquals("normal keyword", KeywordUtils.unescapeKeyword("normal keyword"));
        assertEquals("", KeywordUtils.unescapeKeyword(""));
        assertNull(KeywordUtils.unescapeKeyword(null));
    }

    @Test
    @DisplayName("Complete keyword parsing")
    void testParseKeywordsWithLogic() {
        String monitorKeywords = "Java+Spring|Python";
        String excludeKeywords = "test|Demo";

        KeywordUtils.KeywordParseResult result = KeywordUtils.parseKeywordsWithLogic(monitorKeywords, excludeKeywords);

        // Check monitor keywords
        KeywordUtils.MonitorKeywordGroup monitorGroup = result.getMonitorKeywords();
        assertEquals(1, monitorGroup.getAndGroups().size());
        assertEquals(1, monitorGroup.getOrKeywords().size());
        assertTrue(monitorGroup.getOrKeywords().contains("Python"));

        List<String> andGroup = monitorGroup.getAndGroups().get(0);
        assertTrue(andGroup.contains("Java"));
        assertTrue(andGroup.contains("Spring"));

        // Check exclude keywords
        KeywordUtils.ExcludeKeywordGroup excludeGroup = result.getExcludeKeywords();
        assertEquals(2, excludeGroup.getKeywords().size());
        assertTrue(excludeGroup.getKeywords().contains("test"));
        assertTrue(excludeGroup.getKeywords().contains("Demo"));
    }

    @Test
    @DisplayName("Validate monitor keywords format")
    void testValidateMonitorKeywords() {
        assertTrue(KeywordUtils.validateMonitorKeywords("keyword1|keyword2"));
        assertTrue(KeywordUtils.validateMonitorKeywords("keyword1+keyword2"));
        assertTrue(KeywordUtils.validateMonitorKeywords("keyword1+keyword2|keyword3"));

        assertFalse(KeywordUtils.validateMonitorKeywords(""));
        assertFalse(KeywordUtils.validateMonitorKeywords(null));
    }

    @Test
    @DisplayName("Validate exclude keywords format")
    void testValidateExcludeKeywords() {
        assertTrue(KeywordUtils.validateExcludeKeywords("exclude1|exclude2"));
        assertTrue(KeywordUtils.validateExcludeKeywords("")); // Exclude keywords can be empty
        assertTrue(KeywordUtils.validateExcludeKeywords(null)); // Exclude keywords can be empty
    }

    @Test
    @DisplayName("Get all keywords")
    void testGetAllKeywords() {
        String keywords = "Java+Spring|Python";
        KeywordUtils.MonitorKeywordGroup result = KeywordUtils.parseMonitorKeywords(keywords);

        List<String> allKeywords = result.getAllKeywords();
        assertEquals(3, allKeywords.size());
        assertTrue(allKeywords.contains("Java"));
        assertTrue(allKeywords.contains("Spring"));
        assertTrue(allKeywords.contains("Python"));
    }

    @Test
    @DisplayName("Null values and edge cases")
    void testEdgeCases() {
        // Empty string
        KeywordUtils.MonitorKeywordGroup emptyMonitor = KeywordUtils.parseMonitorKeywords("");
        assertTrue(emptyMonitor.isEmpty());

        KeywordUtils.ExcludeKeywordGroup emptyExclude = KeywordUtils.parseExcludeKeywords("");
        assertTrue(emptyExclude.isEmpty());

        // Only separators
        KeywordUtils.MonitorKeywordGroup onlySeparators = KeywordUtils.parseMonitorKeywords("|||+++");
        assertTrue(onlySeparators.isEmpty());

        // Keywords with whitespace
        KeywordUtils.MonitorKeywordGroup withSpaces = KeywordUtils
                .parseMonitorKeywords(" keyword1 | keyword2 + keyword3 ");
        assertEquals(1, withSpaces.getOrKeywords().size());
        assertEquals(1, withSpaces.getAndGroups().size());
        assertTrue(withSpaces.getOrKeywords().contains("keyword1"));
    }
}
