package com.czb.hn.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;

/**
 * DateTimeUtil工具类测试
 * 验证时间解析和格式转换功能
 */
@DisplayName("DateTimeUtil Tests")
class DateTimeUtilTest {

    @Test
    @DisplayName("Should parse standard format yyyy-MM-dd HH:mm:ss")
    void testParseStandardFormat() {
        String timeStr = "2025-07-02 11:25:16";
        LocalDateTime result = DateTimeUtil.parseDateTime(timeStr);

        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should parse ISO 8601 format yyyy-MM-ddTHH:mm:ss")
    void testParseISOFormat() {
        String timeStr = "2025-07-02T11:25:16";
        LocalDateTime result = DateTimeUtil.parseDateTime(timeStr);

        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should parse ISO 8601 format with milliseconds")
    void testParseISOFormatWithMilliseconds() {
        String timeStr = "2025-07-02T11:25:16.123";
        LocalDateTime result = DateTimeUtil.parseDateTime(timeStr);

        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should return null for invalid format")
    void testParseInvalidFormat() {
        String timeStr = "invalid-date-format";
        LocalDateTime result = DateTimeUtil.parseDateTime(timeStr);

        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for null input")
    void testParseNullInput() {
        LocalDateTime result = DateTimeUtil.parseDateTime(null);
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for blank input")
    void testParseBlankInput() {
        LocalDateTime result = DateTimeUtil.parseDateTime("   ");
        assertNull(result);
    }

    @Test
    @DisplayName("Should format LocalDateTime to standard string")
    void testFormatToStandardString() {
        LocalDateTime dateTime = LocalDateTime.of(2025, 7, 2, 11, 25, 16);
        String result = DateTimeUtil.formatToStandardString(dateTime);

        assertEquals("2025-07-02 11:25:16", result);
    }

    @Test
    @DisplayName("Should return null when formatting null LocalDateTime")
    void testFormatNullDateTime() {
        String result = DateTimeUtil.formatToStandardString(null);
        assertNull(result);
    }

    @Test
    @DisplayName("Should validate standard format correctly")
    void testIsStandardFormat() {
        assertTrue(DateTimeUtil.isStandardFormat("2025-07-02 11:25:16"));
        assertFalse(DateTimeUtil.isStandardFormat("2025-07-02T11:25:16"));
        assertFalse(DateTimeUtil.isStandardFormat("invalid-format"));
        assertFalse(DateTimeUtil.isStandardFormat(null));
        assertFalse(DateTimeUtil.isStandardFormat("   "));
    }

    @Test
    @DisplayName("Should convert various formats to standard format")
    void testConvertToStandardFormat() {
        // Test standard format (should remain unchanged)
        assertEquals("2025-07-02 11:25:16",
                DateTimeUtil.convertToStandardFormat("2025-07-02 11:25:16"));

        // Test ISO format conversion
        assertEquals("2025-07-02 11:25:16",
                DateTimeUtil.convertToStandardFormat("2025-07-02T11:25:16"));

        // Test ISO format with milliseconds conversion
        assertEquals("2025-07-02 11:25:16",
                DateTimeUtil.convertToStandardFormat("2025-07-02T11:25:16.123"));

        // Test invalid format
        assertNull(DateTimeUtil.convertToStandardFormat("invalid-format"));
    }

    @Test
    @DisplayName("Should check if two time strings represent same time")
    void testIsSameTime() {
        // Same time in different formats
        assertTrue(DateTimeUtil.isSameTime("2025-07-02 11:25:16", "2025-07-02T11:25:16"));

        // Different times
        assertFalse(DateTimeUtil.isSameTime("2025-07-02 11:25:16", "2025-07-02 11:25:17"));

        // One invalid format
        assertFalse(DateTimeUtil.isSameTime("2025-07-02 11:25:16", "invalid-format"));

        // Both invalid formats
        assertFalse(DateTimeUtil.isSameTime("invalid1", "invalid2"));
    }

    @Test
    @DisplayName("Should get current time as standard string")
    void testGetCurrentTimeAsStandardString() {
        String currentTime = DateTimeUtil.getCurrentTimeAsStandardString();

        assertNotNull(currentTime);
        assertTrue(DateTimeUtil.isStandardFormat(currentTime));
    }

    @Test
    @DisplayName("Should parse with context info for logging")
    void testParseWithContextInfo() {
        String timeStr = "2025-07-02T11:25:16";
        String contextInfo = "content_123456";

        LocalDateTime result = DateTimeUtil.parseDateTime(timeStr, contextInfo);

        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(2, result.getDayOfMonth());
        assertEquals(11, result.getHour());
        assertEquals(25, result.getMinute());
        assertEquals(16, result.getSecond());
    }

    @Test
    @DisplayName("Should handle edge cases gracefully")
    void testEdgeCases() {
        // Empty string
        assertNull(DateTimeUtil.parseDateTime(""));

        // Only whitespace
        assertNull(DateTimeUtil.parseDateTime("   \t\n   "));

        // Partial date
        assertNull(DateTimeUtil.parseDateTime("2025-07-02"));

        // Invalid month
        assertNull(DateTimeUtil.parseDateTime("2025-13-02 11:25:16"));

        // Invalid day
        assertNull(DateTimeUtil.parseDateTime("2025-07-32 11:25:16"));

        // Invalid hour
        assertNull(DateTimeUtil.parseDateTime("2025-07-02 25:25:16"));
    }

    @Test
    @DisplayName("Should handle microseconds format and convert to Elasticsearch compatible format")
    void testMicrosecondsFormatForElasticsearch() {
        // Test the exact format that was causing the Elasticsearch error
        String timeWithMicroseconds = "2025-07-03T09:18:00.008518";

        // Parse the time with microseconds
        LocalDateTime parsed = DateTimeUtil.parseDateTime(timeWithMicroseconds);
        assertNotNull(parsed, "Should successfully parse time with microseconds");

        // Convert to standard format for Elasticsearch
        String standardFormat = DateTimeUtil.formatToStandardString(parsed);
        assertEquals("2025-07-03 09:18:00", standardFormat,
                "Should convert to standard format without microseconds");

        // Verify the format is compatible with Elasticsearch date format
        assertTrue(standardFormat.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"),
                "Should match yyyy-MM-dd HH:mm:ss pattern");

        // Test that LocalDateTime.now().toString() would cause the same issue
        LocalDateTime now = LocalDateTime.now();
        String nowToString = now.toString(); // This produces ISO format with microseconds
        String nowStandardFormat = DateTimeUtil.formatToStandardString(now);

        // Verify that our utility method produces the correct format
        assertTrue(nowStandardFormat.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"),
                "Current time should be formatted correctly for Elasticsearch");

        // Verify that toString() would be problematic (contains 'T' and possibly
        // microseconds)
        assertTrue(nowToString.contains("T"),
                "LocalDateTime.toString() contains 'T' which is not compatible with ES format");
    }
}
