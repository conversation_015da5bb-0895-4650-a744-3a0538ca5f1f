package com.czb.hn.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 令牌加密工具类测试
 */
class TokenEncryptionUtilTest {

    private TokenEncryptionUtil encryptionUtil;

    @BeforeEach
    void setUp() {
        encryptionUtil = new TokenEncryptionUtil();
        // 设置测试用的加密密钥
        ReflectionTestUtils.setField(encryptionUtil, "encryptionKey", "TestEncryptionKey123456789012");
    }

    @Test
    void testEncryptAndDecrypt() {
        // 测试数据
        String originalToken = "test_access_token_12345";

        // 加密
        String encryptedToken = encryptionUtil.encrypt(originalToken);
        assertNotNull(encryptedToken);
        assertNotEquals(originalToken, encryptedToken);

        // 解密
        String decryptedToken = encryptionUtil.decrypt(encryptedToken);
        assertEquals(originalToken, decryptedToken);
    }

    @Test
    void testEncryptNullOrEmpty() {
        // 测试空值
        assertNull(encryptionUtil.encrypt(null));
        assertNull(encryptionUtil.encrypt(""));

        // 测试解密空值
        assertNull(encryptionUtil.decrypt(null));
        assertNull(encryptionUtil.decrypt(""));
    }

    @Test
    void testEncryptionConsistency() {
        String originalToken = "consistent_test_token";

        // 多次加密应该产生不同的结果（因为使用随机IV）
        String encrypted1 = encryptionUtil.encrypt(originalToken);
        String encrypted2 = encryptionUtil.encrypt(originalToken);

        assertNotEquals(encrypted1, encrypted2);

        // 但解密结果应该相同
        assertEquals(originalToken, encryptionUtil.decrypt(encrypted1));
        assertEquals(originalToken, encryptionUtil.decrypt(encrypted2));
    }

    @Test
    void testValidateEncryption() {
        String testText = "validation_test_token";
        assertTrue(encryptionUtil.validateEncryption(testText));
    }

    @Test
    void testLongToken() {
        // 测试长令牌
        String longToken = "very_long_access_token_".repeat(10);
        String encrypted = encryptionUtil.encrypt(longToken);
        String decrypted = encryptionUtil.decrypt(encrypted);
        assertEquals(longToken, decrypted);
    }

    @Test
    void testSpecialCharacters() {
        // 测试包含特殊字符的令牌
        String specialToken = "token_with_special_chars_!@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        String encrypted = encryptionUtil.encrypt(specialToken);
        String decrypted = encryptionUtil.decrypt(encrypted);
        assertEquals(specialToken, decrypted);
    }

    @Test
    void testGenerateRandomKey() {
        String randomKey = TokenEncryptionUtil.generateRandomKey();
        assertNotNull(randomKey);
        assertFalse(randomKey.isEmpty());
        
        // 生成的密钥应该是Base64编码的
        assertDoesNotThrow(() -> java.util.Base64.getDecoder().decode(randomKey));
    }
}
