# Test Configuration
# 测试环境配置文件

spring:
  # 数据源配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # 缓存配置 - 使用自定义缓存管理器
  # cache:
  #   type: simple

  # 日志配置
  logging:
    level:
      com.czb.hn: DEBUG
      org.springframework.security: DEBUG
      org.hibernate.SQL: DEBUG
      org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 告警处理配置
alert:
  processing:
    # 禁用定时任务
    cron: "-"
    enabled: false

  # 搜索配置
  search:
    default-page-size: 20
    max-page-size: 100

  # 缓存配置
  cache:
    search:
      default-ttl: 300
      statistics-ttl: 600
      configuration-ttl: 1800

# Elasticsearch配置 - 测试环境禁用
elasticsearch:
  enabled: false

# 安全配置 - 测试环境简化
security:
  enabled: false

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
