# 开发环境配置
schedule:
  collector:
    enabled: false # 开发环境启用数据收集
    cron: "0 */30 * * * *" # 开发环境30分钟执行一次
    environment: "development" # 环境标识
    batch-size: 100 # 开发环境较小的批处理大小
    max-retries: 2 # 较少的重试次数
    retry-interval: 3000 # 较短的重试间隔
  cleaner:
    enabled: false # 开发环境启用数据清洗
    cron: "0 */45 * * * *" # 开发环境45分钟执行一次
    environment: "development" # 环境标识
    batch-size: 50 # 较小的批处理大小
  aggregator:
    enabled: false # 开发环境禁用数据聚合
    cron: "-" # 禁用定时任务
    environment: "development" # 环境标识
  elasticsearch-sync:
    enabled: false # 开发环境禁用ES同步
    cron: "-" # 禁用定时任务
    environment: "development" # 环境标识
  billing:
    enabled: true # 开发环境启用计费系统（用于测试）
    expiration-check-cron: "0 */30 * * * ?" # 开发环境30分钟检查一次
    expiration-warning-cron: "0 */15 * * * ?" # 开发环境15分钟警告一次
    environment: "development" # 环境标识
    warning-days: 3 # 开发环境3天警告
    batch-size: 50 # 较小的批处理大小

alert:
  processing:
    time-window-hours: 240

# 开发环境日志配置
logging:
  level:
    root: INFO
    com.czb.hn: DEBUG # 开发环境启用DEBUG日志
    org.hibernate.SQL: DEBUG # 显示SQL语句
    org.springframework.scheduling: DEBUG # 显示定时任务调试信息

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: false
    displayRequestDuration: true
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: true
