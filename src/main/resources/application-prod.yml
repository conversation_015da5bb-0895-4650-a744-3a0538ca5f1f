# 生产环境配置
schedule:
  collector:
    enabled: true                    # 生产环境启用数据收集
    cron: "0 */5 * * * *"           # 生产环境5分钟执行一次（更频繁）
    environment: "production"        # 环境标识
    batch-size: 1000                # 生产环境较大的批处理大小
    max-retries: 5                  # 更多的重试次数
    retry-interval: 10000           # 较长的重试间隔
  cleaner:
    enabled: true                    # 生产环境启用数据清洗
    cron: "0 */10 * * * *"          # 生产环境10分钟执行一次
    environment: "production"        # 环境标识
    batch-size: 200                 # 较大的批处理大小
  aggregator:
    enabled: true                    # 生产环境启用数据聚合
    cron: "0 0 1 * * *"             # 每天凌晨1点执行聚合
    environment: "production"        # 环境标识
  elasticsearch-sync:
    enabled: true                    # 生产环境启用ES同步
    cron: "0 */3 * * * *"           # 生产环境3分钟同步一次（更频繁）
  billing:
    enabled: true                    # 生产环境启用计费系统
    expiration-check-cron: "0 0 2 * * ?" # 生产环境每天凌晨2点检查
    expiration-warning-cron: "0 0 9 * * ?" # 生产环境每天上午9点警告
    environment: "production"        # 环境标识
    warning-days: 7                  # 生产环境7天警告
    batch-size: 200                 # 较大的批处理大小

# 生产环境日志配置
logging:
  level:
    root: WARN                      # 生产环境减少日志输出
    com.czb.hn: INFO
    org.springframework.scheduling: INFO  # 保留定时任务关键日志
  file:
    name: /var/log/securadar/securadar.log  # 生产环境日志文件路径
    max-size: 100MB                 # 日志文件最大大小
    max-history: 30                 # 保留30天的日志

# 生产环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true


springdoc:
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: false
    displayRequestDuration: true
    enabled: false
  api-docs:
    path: /v3/api-docs
    enabled: false