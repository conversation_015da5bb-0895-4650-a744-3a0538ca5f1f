-- Convert InformationSensitivityType from String to Integer Storage
-- Version: 1.0
-- Date: 2025-07-01

-- ==================== 数据类型转换迁移 ====================

-- 1. 备份现有数据（可选，用于回滚）
-- CREATE TABLE alert_results_backup AS SELECT * FROM alert_results;
-- CREATE TABLE sina_news_ods_backup AS SELECT * FROM sina_news_ods;
-- CREATE TABLE sina_news_dwd_backup AS SELECT * FROM sina_news_dwd;

-- 2. 添加临时列存储转换后的数值
ALTER TABLE alert_results ADD COLUMN information_sensitivity_type_temp INT;
ALTER TABLE sina_news_ods ADD COLUMN sensitivity_type_temp INT;
ALTER TABLE sina_news_dwd ADD COLUMN sensitivity_type_temp INT;

-- 3. 转换 alert_results 表数据
UPDATE alert_results SET information_sensitivity_type_temp = 
    CASE information_sensitivity_type
        WHEN 'ALL' THEN 0
        WHEN 'SENSITIVE' THEN 1
        WHEN 'NON_SENSITIVE' THEN 2
        WHEN 'NEUTRAL' THEN 3
        ELSE 3  -- 默认为 NEUTRAL
    END;

-- 4. 转换 sina_news_ods 表数据
UPDATE sina_news_ods SET sensitivity_type_temp = 
    CASE sensitivity_type
        WHEN 'ALL' THEN 0
        WHEN 'SENSITIVE' THEN 1
        WHEN 'NON_SENSITIVE' THEN 2
        WHEN 'NEUTRAL' THEN 3
        ELSE 3  -- 默认为 NEUTRAL
    END;

-- 5. 转换 sina_news_dwd 表数据
UPDATE sina_news_dwd SET sensitivity_type_temp = 
    CASE sensitivity_type
        WHEN 'ALL' THEN 0
        WHEN 'SENSITIVE' THEN 1
        WHEN 'NON_SENSITIVE' THEN 2
        WHEN 'NEUTRAL' THEN 3
        ELSE 3  -- 默认为 NEUTRAL
    END;

-- 6. 删除原列并重命名临时列
-- alert_results 表
ALTER TABLE alert_results DROP COLUMN information_sensitivity_type;
ALTER TABLE alert_results CHANGE COLUMN information_sensitivity_type_temp information_sensitivity_type INT NOT NULL DEFAULT 3;

-- sina_news_ods 表
ALTER TABLE sina_news_ods DROP COLUMN sensitivity_type;
ALTER TABLE sina_news_ods CHANGE COLUMN sensitivity_type_temp sensitivity_type INT;

-- sina_news_dwd 表
ALTER TABLE sina_news_dwd DROP COLUMN sensitivity_type;
ALTER TABLE sina_news_dwd CHANGE COLUMN sensitivity_type_temp sensitivity_type INT;

-- 7. 添加索引优化查询性能
CREATE INDEX idx_alert_results_sensitivity_type ON alert_results(information_sensitivity_type);
CREATE INDEX idx_sina_news_ods_sensitivity_type ON sina_news_ods(sensitivity_type);
CREATE INDEX idx_sina_news_dwd_sensitivity_type ON sina_news_dwd(sensitivity_type);

-- 8. 添加约束确保数据完整性
ALTER TABLE alert_results ADD CONSTRAINT chk_information_sensitivity_type 
    CHECK (information_sensitivity_type IN (0, 1, 2, 3));
    
ALTER TABLE sina_news_ods ADD CONSTRAINT chk_sensitivity_type 
    CHECK (sensitivity_type IS NULL OR sensitivity_type IN (0, 1, 2, 3));
    
ALTER TABLE sina_news_dwd ADD CONSTRAINT chk_sensitivity_type 
    CHECK (sensitivity_type IS NULL OR sensitivity_type IN (0, 1, 2, 3));

-- ==================== 验证数据转换 ====================

-- 验证 alert_results 表转换结果
SELECT 
    'alert_results validation' as table_name,
    information_sensitivity_type,
    COUNT(*) as count,
    CASE information_sensitivity_type
        WHEN 0 THEN 'ALL'
        WHEN 1 THEN 'SENSITIVE'
        WHEN 2 THEN 'NON_SENSITIVE'
        WHEN 3 THEN 'NEUTRAL'
        ELSE 'UNKNOWN'
    END as meaning
FROM alert_results 
GROUP BY information_sensitivity_type
ORDER BY information_sensitivity_type;

-- 验证 sina_news_ods 表转换结果
SELECT 
    'sina_news_ods validation' as table_name,
    sensitivity_type,
    COUNT(*) as count,
    CASE sensitivity_type
        WHEN 0 THEN 'ALL'
        WHEN 1 THEN 'SENSITIVE'
        WHEN 2 THEN 'NON_SENSITIVE'
        WHEN 3 THEN 'NEUTRAL'
        ELSE 'UNKNOWN'
    END as meaning
FROM sina_news_ods 
WHERE sensitivity_type IS NOT NULL
GROUP BY sensitivity_type
ORDER BY sensitivity_type;

-- 验证 sina_news_dwd 表转换结果
SELECT 
    'sina_news_dwd validation' as table_name,
    sensitivity_type,
    COUNT(*) as count,
    CASE sensitivity_type
        WHEN 0 THEN 'ALL'
        WHEN 1 THEN 'SENSITIVE'
        WHEN 2 THEN 'NON_SENSITIVE'
        WHEN 3 THEN 'NEUTRAL'
        ELSE 'UNKNOWN'
    END as meaning
FROM sina_news_dwd 
WHERE sensitivity_type IS NOT NULL
GROUP BY sensitivity_type
ORDER BY sensitivity_type;

-- ==================== 记录迁移完成 ====================

-- 迁移完成提示
SELECT 'InformationSensitivityType integer conversion migration completed successfully' as migration_status;

-- 数值映射说明：
-- 0: ALL (全部) - 用于查询时的"全部"选项
-- 1: SENSITIVE (敏感) - 对应Sina API值1
-- 2: NON_SENSITIVE (非敏感) - 对应Sina API值2  
-- 3: NEUTRAL (中性) - 对应Sina API值3
