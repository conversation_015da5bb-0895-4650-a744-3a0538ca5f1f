-- V004__Create_Alert_Notification_Queue_Table.sql
-- Create alert notification queue table for decoupled notification scheduling

-- Create alert_notification_queue table
CREATE TABLE alert_notification_queue (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知队列ID',
    alert_id BIGINT NULL COMMENT '预警ID，关联alert_results表，无预警通知时为NULL',
    configuration_id BIGINT NOT NULL COMMENT '配置ID，关联alert_configurations表',
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID，用于多租户隔离',
    scheduled_time TIMESTAMP NOT NULL COMMENT '计划发送时间',
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '通知状态',
    recipients JSON NOT NULL COMMENT '接收人信息（邮箱、手机号、用户名）',
    notification_type ENUM('ALERT', 'INFO_PUSH', 'NO_ALERT') NOT NULL COMMENT '通知类型',
    reception_settings JSON COMMENT '接收设置快照',
    attempt_count INT NOT NULL DEFAULT 0 COMMENT '处理尝试次数',
    last_error TEXT COMMENT '最后一次错误信息',
    processed_at TIMESTAMP NULL COMMENT '实际处理时间',
    next_retry_time TIMESTAMP NULL COMMENT '下次重试时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(255) COMMENT '创建人'
) COMMENT = '预警通知队列表 - 解耦预警生成和通知发送，支持复杂的调度规则';

-- Create indexes for performance optimization
CREATE INDEX idx_scheduled_status ON alert_notification_queue(scheduled_time, status);
CREATE INDEX idx_enterprise_scheduled ON alert_notification_queue(enterprise_id, scheduled_time);
CREATE INDEX idx_alert_id ON alert_notification_queue(alert_id);
CREATE INDEX idx_configuration_id ON alert_notification_queue(configuration_id);
CREATE INDEX idx_status_type ON alert_notification_queue(status, notification_type);
CREATE INDEX idx_enterprise_status ON alert_notification_queue(enterprise_id, status);
CREATE INDEX idx_retry_time ON alert_notification_queue(next_retry_time);
CREATE INDEX idx_notification_type ON alert_notification_queue(notification_type);
CREATE INDEX idx_enterprise_config ON alert_notification_queue(enterprise_id, configuration_id);

-- Add foreign key constraints
-- Note: alert_id can be NULL for no-alert notifications
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT fk_alert_notification_queue_alert_id 
FOREIGN KEY (alert_id) REFERENCES alert_results(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE alert_notification_queue 
ADD CONSTRAINT fk_alert_notification_queue_config_id 
FOREIGN KEY (configuration_id) REFERENCES alert_configurations(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraints for data validation
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_attempt_count_non_negative 
CHECK (attempt_count >= 0);

ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_attempt_count_max_limit 
CHECK (attempt_count <= 10);

-- Add constraint for no-alert notifications (alert_id must be NULL when type is NO_ALERT)
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_no_alert_consistency 
CHECK (
    (notification_type = 'NO_ALERT' AND alert_id IS NULL) OR 
    (notification_type != 'NO_ALERT')
);

-- Add constraint for alert notifications (alert_id must not be NULL when type is ALERT)
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_alert_consistency 
CHECK (
    (notification_type = 'ALERT' AND alert_id IS NOT NULL) OR 
    (notification_type != 'ALERT')
);

-- Create a view for notification statistics
CREATE VIEW v_alert_notification_statistics AS
SELECT 
    enterprise_id,
    DATE(scheduled_time) as notification_date,
    notification_type,
    status,
    COUNT(*) as notification_count,
    AVG(attempt_count) as avg_attempt_count,
    COUNT(CASE WHEN alert_id IS NULL THEN 1 END) as no_alert_count,
    COUNT(CASE WHEN alert_id IS NOT NULL THEN 1 END) as alert_based_count
FROM alert_notification_queue
GROUP BY enterprise_id, DATE(scheduled_time), notification_type, status;

-- Create a view for overdue notifications monitoring
CREATE VIEW v_overdue_notifications AS
SELECT 
    id,
    enterprise_id,
    configuration_id,
    notification_type,
    scheduled_time,
    status,
    attempt_count,
    TIMESTAMPDIFF(MINUTE, scheduled_time, NOW()) as minutes_overdue
FROM alert_notification_queue
WHERE status = 'PENDING' 
  AND scheduled_time < NOW() 
  AND TIMESTAMPDIFF(MINUTE, scheduled_time, NOW()) > 30;

-- Add comments for better documentation
ALTER TABLE alert_notification_queue COMMENT = '预警通知队列表 - 实现预警生成与通知发送的解耦，支持复杂的时间调度和接收规则';

-- Insert sample data for testing (optional - remove in production)
-- INSERT INTO alert_notification_queue (
--     alert_id, configuration_id, enterprise_id, scheduled_time, 
--     recipients, notification_type, created_by
-- ) VALUES 
-- (1, 1, 'enterprise123', NOW(), '{"emails":["<EMAIL>"],"phones":["13800138000"],"usernames":["admin"]}', 'ALERT', 'system'),
-- (NULL, 1, 'enterprise123', DATE_ADD(NOW(), INTERVAL 1 HOUR), '{"emails":["<EMAIL>"]}', 'NO_ALERT', 'system');
