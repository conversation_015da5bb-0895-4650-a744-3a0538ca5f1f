-- Alert Configuration Module Database Migration
-- Creates tables for sentiment monitoring alert configurations with snapshot management
-- Version: 1.0
-- Date: 2024-06-24

-- Create alert_configurations table
CREATE TABLE alert_configurations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL COMMENT 'Configuration name',
    description TEXT COMMENT 'Configuration description',
    plan_id BIGINT COMMENT 'Associated plan ID',
    enterprise_id VARCHAR(255) NOT NULL COMMENT 'Enterprise ID',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the alert is enabled',
    
    -- JSON configuration fields
    alert_keywords JSON COMMENT 'Alert keywords configuration as JSON',
    content_settings JSON COMMENT 'Content filtering settings as JSON',
    threshold_settings JSON COMMENT 'Alert threshold conditions as JSON',
    level_settings JSON COMMENT 'Alert level classification settings as JSON',
    reception_settings JSON COMMENT 'Alert reception and notification settings as JSON',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
    created_by VARCHAR(255) COMMENT 'User who created the configuration',
    updated_by VARCHAR(255) COMMENT 'User who last updated the configuration',
    
    -- Version control fields
    current_version INT NOT NULL DEFAULT 1 COMMENT 'Current version number',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the configuration is active',
    last_snapshot_at TIMESTAMP COMMENT 'Last snapshot creation timestamp',
    
    -- Indexes
    INDEX idx_plan_id (plan_id),
    INDEX idx_enterprise_id (enterprise_id),
    INDEX idx_enabled (enabled),
    INDEX idx_created_at (created_at),
    INDEX idx_name (name),
    INDEX idx_active (is_active),
    INDEX idx_enterprise_name (enterprise_id, name),
    INDEX idx_plan_enabled (plan_id, enabled),
    INDEX idx_enterprise_enabled (enterprise_id, enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Alert configuration main table';

-- Create alert_configuration_snapshots table
CREATE TABLE alert_configuration_snapshots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    configuration_id BIGINT NOT NULL COMMENT 'Reference to alert configuration',
    version_number INT NOT NULL COMMENT 'Version number of this snapshot',
    
    -- Snapshot data
    snapshot_data JSON NOT NULL COMMENT 'Complete configuration snapshot as JSON',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Snapshot creation timestamp',
    created_by VARCHAR(255) COMMENT 'User who created the snapshot',
    change_reason VARCHAR(500) COMMENT 'Reason for creating this snapshot',
    
    -- Snapshot metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this is the active snapshot',
    operation_type VARCHAR(50) COMMENT 'Operation type: CREATE, UPDATE, ROLLBACK, DELETE, MANUAL',
    data_size BIGINT COMMENT 'Size of snapshot data in bytes',
    checksum VARCHAR(64) COMMENT 'SHA-256 checksum for data integrity',
    
    -- Indexes
    INDEX idx_config_id (configuration_id),
    INDEX idx_version (configuration_id, version_number),
    INDEX idx_active (configuration_id, is_active),
    INDEX idx_created_at (created_at),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_by (created_by),
    INDEX idx_data_size (data_size),
    
    -- Unique constraint for version per configuration
    UNIQUE KEY uk_config_version (configuration_id, version_number),
    
    -- Foreign key constraint
    CONSTRAINT fk_snapshot_configuration 
        FOREIGN KEY (configuration_id) 
        REFERENCES alert_configurations(id) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Alert configuration snapshots for version control';

-- Create additional indexes for performance optimization
CREATE INDEX idx_alert_configs_composite ON alert_configurations (enterprise_id, enabled, is_active);
CREATE INDEX idx_snapshots_active_version ON alert_configuration_snapshots (configuration_id, is_active, version_number DESC);
CREATE INDEX idx_snapshots_cleanup ON alert_configuration_snapshots (configuration_id, version_number);

-- Add JSON functional indexes for frequently queried JSON fields (MySQL 8.0+)
-- These indexes improve performance when querying JSON fields
-- Note: These will be ignored in MySQL versions < 8.0

-- Index for alert keywords search
ALTER TABLE alert_configurations 
ADD INDEX idx_alert_keywords_json ((CAST(alert_keywords->'$.keywords' AS CHAR(255) ARRAY)));

-- Index for content settings source types
ALTER TABLE alert_configurations 
ADD INDEX idx_content_source_types ((CAST(content_settings->'$.sourceTypes' AS CHAR(100) ARRAY)));

-- Index for content settings sensitivity type
ALTER TABLE alert_configurations 
ADD INDEX idx_content_sensitivity ((CAST(content_settings->'$.sensitivityType' AS CHAR(50))));

-- Create views for common queries
CREATE VIEW v_active_alert_configurations AS
SELECT 
    id,
    name,
    description,
    plan_id,
    enterprise_id,
    enabled,
    alert_keywords,
    content_settings,
    threshold_settings,
    level_settings,
    reception_settings,
    created_at,
    updated_at,
    created_by,
    updated_by,
    current_version,
    last_snapshot_at
FROM alert_configurations 
WHERE is_active = TRUE;

CREATE VIEW v_enabled_alert_configurations AS
SELECT 
    id,
    name,
    description,
    plan_id,
    enterprise_id,
    alert_keywords,
    content_settings,
    threshold_settings,
    level_settings,
    reception_settings,
    created_at,
    updated_at,
    current_version
FROM alert_configurations 
WHERE is_active = TRUE AND enabled = TRUE;

-- Create view for latest snapshots
CREATE VIEW v_latest_snapshots AS
SELECT 
    s.id,
    s.configuration_id,
    s.version_number,
    s.created_at,
    s.created_by,
    s.change_reason,
    s.operation_type,
    s.data_size,
    c.name as configuration_name,
    c.enterprise_id
FROM alert_configuration_snapshots s
JOIN alert_configurations c ON s.configuration_id = c.id
WHERE s.is_active = TRUE;

-- Insert initial data or configuration if needed
-- This section can be used to insert default configurations

-- Add comments for better documentation
ALTER TABLE alert_configurations COMMENT = 'Main table for storing sentiment monitoring alert configurations with JSON-based settings and version control';
ALTER TABLE alert_configuration_snapshots COMMENT = 'Snapshot table for alert configuration version history and audit trail';



-- Performance optimization: Analyze tables after creation
ANALYZE TABLE alert_configurations;
ANALYZE TABLE alert_configuration_snapshots;
