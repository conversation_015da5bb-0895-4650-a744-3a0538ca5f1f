-- Alert Results Module Database Migration
-- Creates tables for alert processing results with multi-tenant support
-- Version: 2.0
-- Date: 2024-06-25

-- Create alert_results table
CREATE TABLE alert_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- Tenant isolation fields
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    plan_id BIGINT COMMENT '方案ID',
    configuration_id BIGINT NOT NULL COMMENT '预警配置ID',
    
    -- Alert core data
    title TEXT NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '正文',
    involved_keywords JSON NOT NULL COMMENT '涉及关键词 [{"keyword":"关键词","count":3,"positions":[1,5,10]}]',
    information_sensitivity_type ENUM('SENSITIVE', 'NEUTRAL', 'NON_SENSITIVE') NOT NULL DEFAULT 'NEUTRAL' COMMENT '信息敏感性类型 (敏感, 中性, 非敏感)',
    content_category ENUM('ORIGINAL', 'FORWARD') NOT NULL DEFAULT 'ORIGINAL' COMMENT '内容类别 (原发, 转发)',
    warning_level ENUM('GENERAL', 'MODERATE', 'SEVERE') NOT NULL COMMENT '预警分级 (一般, 中等, 严重)',
    source VARCHAR(255) NOT NULL COMMENT '来源 (实际内容来源，如：微博、微信、新闻网站等)',
    warning_time TIMESTAMP NOT NULL COMMENT '预警时间',
    similar_article_count INT DEFAULT 0 COMMENT '相似文章数',
    
    -- Source data reference (ALL from Elasticsearch)
    original_content_id VARCHAR(64) NOT NULL COMMENT '原始内容ID (来自Elasticsearch)',
    
    -- Flexible extension
    extended_attributes JSON COMMENT '扩展属性 {"custom_field1":"value1","metrics":{"score":0.85}}',
    
    -- Processing metadata
    processing_version VARCHAR(20) DEFAULT '1.0' COMMENT '处理版本',
    rule_snapshot JSON COMMENT '触发规则快照',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(255) COMMENT '创建者',
    
    -- Performance indexes
    INDEX idx_enterprise_warning_time (enterprise_id, warning_time DESC),
    INDEX idx_plan_warning_time (plan_id, warning_time DESC),
    INDEX idx_configuration_time (configuration_id, warning_time DESC),
    INDEX idx_warning_level (warning_level),
    INDEX idx_information_sensitivity_type (information_sensitivity_type),
    INDEX idx_content_category (content_category),
    INDEX idx_source (source),
    INDEX idx_original_content (original_content_id),
    
    -- Composite indexes for common queries
    INDEX idx_enterprise_sensitive_level (enterprise_id, is_sensitive, warning_level),
    INDEX idx_plan_original_time (plan_id, is_original, warning_time DESC),
    INDEX idx_enterprise_source_time (enterprise_id, source, warning_time DESC),
    
    -- Foreign key constraint to alert_configurations
    CONSTRAINT fk_alert_result_configuration 
        FOREIGN KEY (configuration_id) REFERENCES alert_configurations(id) 
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='预警结果表 - 数据来源：Elasticsearch';

-- Create full-text search index for Chinese text (MySQL 8.0+)
-- This index improves performance for title and content searches
ALTER TABLE alert_results 
ADD FULLTEXT INDEX ft_title_content (title, content) WITH PARSER ngram;

-- Create alert_processing_logs table for monitoring
CREATE TABLE alert_processing_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    plan_id BIGINT COMMENT '方案ID',
    configuration_id BIGINT COMMENT '配置ID',
    processing_start_time TIMESTAMP NOT NULL COMMENT '处理开始时间',
    processing_end_time TIMESTAMP COMMENT '处理结束时间',
    processed_records_count INT DEFAULT 0 COMMENT '处理记录数',
    generated_alerts_count INT DEFAULT 0 COMMENT '生成预警数',
    error_count INT DEFAULT 0 COMMENT '错误数量',
    status ENUM('RUNNING', 'COMPLETED', 'FAILED') DEFAULT 'RUNNING' COMMENT '处理状态',
    error_details TEXT COMMENT '错误详情',
    processing_duration_ms BIGINT COMMENT '处理耗时（毫秒）',
    batch_id VARCHAR(64) COMMENT '批次ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- Performance indexes
    INDEX idx_enterprise_start_time (enterprise_id, processing_start_time DESC),
    INDEX idx_plan_start_time (plan_id, processing_start_time DESC),
    INDEX idx_configuration_start_time (configuration_id, processing_start_time DESC),
    INDEX idx_status (status),
    INDEX idx_batch_id (batch_id),
    INDEX idx_created_at (created_at),
    
    -- Composite indexes for monitoring queries
    INDEX idx_enterprise_status_time (enterprise_id, status, processing_start_time DESC),
    INDEX idx_plan_status_time (plan_id, status, processing_start_time DESC)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='预警处理日志表 - 用于监控和审计';

-- Create additional performance optimization indexes
CREATE INDEX idx_alert_results_duplicate_check ON alert_results (enterprise_id, original_content_id, configuration_id);
CREATE INDEX idx_alert_results_time_range ON alert_results (enterprise_id, warning_time, warning_level);
CREATE INDEX idx_alert_results_source_filter ON alert_results (enterprise_id, source, warning_time DESC);

-- Create views for common queries
CREATE VIEW v_active_alert_results AS
SELECT 
    ar.id,
    ar.enterprise_id,
    ar.plan_id,
    ar.configuration_id,
    ar.title,
    ar.content,
    ar.involved_keywords,
    ar.is_sensitive,
    ar.is_original,
    ar.warning_level,
    ar.source,
    ar.warning_time,
    ar.similar_article_count,
    ar.original_content_id,
    ar.extended_attributes,
    ar.created_at,
    ac.name as configuration_name,
    ac.enabled as configuration_enabled
FROM alert_results ar
JOIN alert_configurations ac ON ar.configuration_id = ac.id
WHERE ac.is_active = TRUE AND ac.enabled = TRUE;

CREATE VIEW v_alert_statistics AS
SELECT 
    enterprise_id,
    plan_id,
    DATE(warning_time) as alert_date,
    warning_level,
    source,
    COUNT(*) as alert_count,
    SUM(CASE WHEN is_sensitive = TRUE THEN 1 ELSE 0 END) as sensitive_count,
    SUM(CASE WHEN is_original = TRUE THEN 1 ELSE 0 END) as original_count,
    AVG(similar_article_count) as avg_similar_count
FROM alert_results
GROUP BY enterprise_id, plan_id, DATE(warning_time), warning_level, source;

-- Add comments for better documentation
ALTER TABLE alert_results COMMENT = '预警结果主表 - 存储基于Elasticsearch数据源的预警处理结果，支持多租户隔离';
ALTER TABLE alert_processing_logs COMMENT = '预警处理日志表 - 记录预警处理过程的监控和审计信息';

-- Performance optimization: Analyze tables after creation
ANALYZE TABLE alert_results;
ANALYZE TABLE alert_processing_logs;
