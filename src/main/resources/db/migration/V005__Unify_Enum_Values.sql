-- Enum Values Unification Migration
-- Unifies similar enum values to maintain consistency
-- Version: 1.0
-- Date: 2025-06-30

-- ==================== 统一枚举值 ====================

-- 1. 统一 sourceLevel 中的 CENTRAL 为 NATIONAL
-- 更新 alert_configurations 表中的 content_settings JSON 字段
UPDATE alert_configurations
SET content_settings = REPLACE(content_settings, '"CENTRAL"', '"NATIONAL"')
WHERE content_settings LIKE '%"CENTRAL"%';

-- 2. 统一 level_settings 中的 sourceLevel 映射
-- 更新 alert_configurations 表中的 level_settings JSON 字段
UPDATE alert_configurations
SET level_settings = REPLACE(level_settings, '"CENTRAL":', '"NATIONAL":')
WHERE level_settings LIKE '%"CENTRAL":%';

-- 3. 统一 briefing_configurations 表中的相同问题（如果存在）
UPDATE briefing_configurations
SET content_settings = REPLACE(content_settings, '"CENTRAL"', '"NATIONAL"')
WHERE content_settings LIKE '%"CENTRAL"%'
AND content_settings IS NOT NULL;

-- 4. 统一 briefing_configurations 表中的 level_settings
UPDATE briefing_configurations
SET level_settings = REPLACE(level_settings, '"CENTRAL":', '"NATIONAL":')
WHERE level_settings LIKE '%"CENTRAL":%'
AND level_settings IS NOT NULL;

-- ==================== 验证数据一致性 ====================

-- 验证 alert_configurations 表中不再包含 CENTRAL 值
SELECT
    'alert_configurations validation' as check_type,
    COUNT(*) as remaining_central_count
FROM alert_configurations
WHERE content_settings LIKE '%"CENTRAL"%' OR level_settings LIKE '%"CENTRAL":%';

-- 验证 briefing_configurations 表中不再包含 CENTRAL 值
SELECT
    'briefing_configurations validation' as check_type,
    COUNT(*) as remaining_central_count
FROM briefing_configurations
WHERE (content_settings LIKE '%"CENTRAL"%' OR level_settings LIKE '%"CENTRAL":%')
AND (content_settings IS NOT NULL OR level_settings IS NOT NULL);

-- ==================== 添加注释说明 ====================

-- 说明：CENTRAL 值已统一为 NATIONAL
-- 未来应使用 NATIONAL 而不是 CENTRAL
-- 这确保了枚举值的一致性和可维护性

-- ==================== 记录迁移完成 ====================

-- 迁移完成提示
SELECT 'Enum values unification migration completed successfully' as migration_status;
