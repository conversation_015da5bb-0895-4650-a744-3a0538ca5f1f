-- V005: Add notification and push associations to alert_results
-- This migration adds foreign key fields to AlertResult to associate with notifications and push details

-- Add notification queue association to alert_results
ALTER TABLE alert_results 
ADD COLUMN alert_notification_queue_id BIGINT NULL COMMENT '关联的通知队列ID';

-- Add push detail association to alert_results  
ALTER TABLE alert_results 
ADD COLUMN alert_push_detail_id BIGINT NULL COMMENT '关联的推送详情ID';

-- Add indexes for the new association fields
CREATE INDEX idx_alert_notification_queue ON alert_results(alert_notification_queue_id);
CREATE INDEX idx_alert_push_detail ON alert_results(alert_push_detail_id);

-- Remove alert_ids column from alert_notification_queue (if it exists)
-- This column is no longer needed with the new association approach
ALTER TABLE alert_notification_queue 
DROP COLUMN IF EXISTS alert_ids;

-- Remove alert_id column from alert_push_details (if it exists)  
-- This column is no longer needed with the new association approach
ALTER TABLE alert_push_details 
DROP COLUMN IF EXISTS alert_id;

-- Add foreign key constraints (optional, for data integrity)
-- Uncomment these if you want strict referential integrity

-- ALTER TABLE alert_results 
-- ADD CONSTRAINT fk_alert_notification_queue 
-- FOREIGN KEY (alert_notification_queue_id) REFERENCES alert_notification_queue(id) 
-- ON DELETE SET NULL;

-- ALTER TABLE alert_results 
-- ADD CONSTRAINT fk_alert_push_detail 
-- FOREIGN KEY (alert_push_detail_id) REFERENCES alert_push_details(id) 
-- ON DELETE SET NULL;
