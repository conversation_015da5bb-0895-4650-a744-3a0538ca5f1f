-- User Login Records Table Migration
-- Creates table for tracking user login activities
-- Version: 1.0
-- Date: 2025-07-01

-- Create user_login_records table
CREATE TABLE user_login_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL COMMENT 'User ID from OnePass authentication',
    user_name VARCHAR(255) COMMENT 'User display name (redundant storage for query convenience)',
    enterprise_id VARCHAR(64) COMMENT 'Enterprise ID for enterprise-level statistics',
    enterprise_code VARCHAR(64) COMMENT 'Enterprise code for enterprise identification',
    login_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Login timestamp',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',

    -- Indexes for performance optimization
    INDEX idx_user_id (user_id),
    INDEX idx_enterprise_id (enterprise_id),
    INDEX idx_enterprise_code (enterprise_code),
    INDEX idx_login_time (login_time),
    INDEX idx_user_login_time (user_id, login_time DESC),
    INDEX idx_enterprise_login_time (enterprise_id, login_time DESC),
    INDEX idx_enterprise_code_time (enterprise_code, login_time DESC),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='User login records table - tracks user login activities for enterprise-level statistics and audit';

-- Add table comment for documentation
ALTER TABLE user_login_records COMMENT = 'User login records table - stores enterprise-level login activities for last login time tracking, login frequency statistics, and maintains 30-day data retention with cleanup policies';
