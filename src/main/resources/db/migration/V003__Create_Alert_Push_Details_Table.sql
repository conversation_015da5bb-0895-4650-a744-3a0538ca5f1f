-- V003__Create_Alert_Push_Details_Table.sql
-- Create alert push details table for tracking notification push operations

-- Create alert_push_details table
CREATE TABLE alert_push_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推送详情ID',
    alert_id BIGINT NULL COMMENT '预警ID，关联alert_results表',
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID，用于多租户隔离',
    account_info VARCHAR(255) NOT NULL COMMENT '账户信息（用户名、手机号或邮箱）',
    push_type ENUM('EMAIL', 'SMS', 'SYSTEM') NOT NULL COMMENT '推送类型',
    push_status ENUM('SUCCESS', 'FAILURE') NOT NULL COMMENT '推送状态',
    push_time TIMESTAMP NOT NULL COMMENT '推送时间',
    error_message TEXT COMMENT '错误信息（推送失败时）',
    push_details TEXT COMMENT '推送详情或元数据',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    last_retry_time TIMESTAMP NULL COMMENT '最后重试时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(255) COMMENT '创建人'
) COMMENT = '预警推送详情表 - 记录预警通知的推送操作详情和状态';

-- Create indexes for performance optimization
CREATE INDEX idx_alert_id ON alert_push_details(alert_id);
CREATE INDEX idx_alert_push_type ON alert_push_details(alert_id, push_type);
CREATE INDEX idx_push_time ON alert_push_details(push_time);
CREATE INDEX idx_push_status ON alert_push_details(push_status);
CREATE INDEX idx_enterprise_push_time ON alert_push_details(enterprise_id, push_time);
CREATE INDEX idx_account_push_type ON alert_push_details(account_info, push_type);
CREATE INDEX idx_enterprise_status_time ON alert_push_details(enterprise_id, push_status, push_time);
CREATE INDEX idx_retry_status ON alert_push_details(push_status, retry_count);

-- Add foreign key constraint to alert_results table
-- Note: This assumes alert_results table exists from previous migration
ALTER TABLE alert_push_details 
ADD CONSTRAINT fk_alert_push_details_alert_id 
FOREIGN KEY (alert_id) REFERENCES alert_results(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraints for data validation
ALTER TABLE alert_push_details 
ADD CONSTRAINT chk_retry_count_non_negative 
CHECK (retry_count >= 0);

ALTER TABLE alert_push_details 
ADD CONSTRAINT chk_retry_count_max_limit 
CHECK (retry_count <= 10);

-- Add constraint to ensure last_retry_time is set when retry_count > 0
-- Note: This is a complex constraint that might be better handled in application logic
-- ALTER TABLE alert_push_details 
-- ADD CONSTRAINT chk_retry_time_consistency 
-- CHECK ((retry_count = 0 AND last_retry_time IS NULL) OR (retry_count > 0 AND last_retry_time IS NOT NULL));

-- Create a view for push statistics (optional, for reporting purposes)
CREATE VIEW v_alert_push_statistics AS
SELECT 
    enterprise_id,
    DATE(push_time) as push_date,
    push_type,
    push_status,
    COUNT(*) as push_count,
    AVG(retry_count) as avg_retry_count
FROM alert_push_details
GROUP BY enterprise_id, DATE(push_time), push_type, push_status;

-- Add comments for better documentation
ALTER TABLE alert_push_details COMMENT = '预警推送详情表 - 存储预警通知的推送操作记录，支持多种推送类型和重试机制';

-- Insert sample data for testing (optional - remove in production)
-- INSERT INTO alert_push_details (
--     alert_id, enterprise_id, account_info, push_type, push_status, 
--     push_time, push_details, created_by
-- ) VALUES 
-- (1, 'enterprise123', 'admin', 'SYSTEM', 'SUCCESS', NOW(), '系统通知发送成功', 'system'),
-- (1, 'enterprise123', '***********', 'SMS', 'SUCCESS', NOW(), '短信发送成功', 'system'),
-- (1, 'enterprise123', '<EMAIL>', 'EMAIL', 'SUCCESS', NOW(), '邮件发送成功', 'system');
