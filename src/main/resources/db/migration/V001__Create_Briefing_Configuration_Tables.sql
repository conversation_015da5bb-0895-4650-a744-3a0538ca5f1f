-- Briefing Configuration Module Database Migration
-- Creates tables for sentiment monitoring briefing configurations
-- Version: 1.0
-- Date: 2025-06-27

-- Create briefing_configurations table
CREATE TABLE briefing_configurations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL COMMENT 'Configuration name',
    plan_id BIGINT COMMENT 'Associated plan ID',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the configuration is active',

    -- JSON configuration fields
    content_settings JSON COMMENT 'Content filtering settings as JSON',
    threshold_settings JSON COMMENT 'Briefing threshold conditions as JSO<PERSON>',
    level_settings JSON COMMENT 'Briefing level classification settings as JSO<PERSON>',

    -- Daily briefing settings
    daily_briefing_is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether daily briefing is enabled',
    daily_reception_settings JSON COMMENT 'Daily briefing notification settings as JSON',

    -- Weekly briefing settings
    weekly_briefing_is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether weekly briefing is enabled',
    weekly_reception_settings JSON COMMENT 'Weekly briefing notification settings as JSO<PERSON>',

    -- Monthly briefing settings
    monthly_briefing_is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether monthly briefing is enabled',
    monthly_reception_settings JSON COMMENT 'Monthly briefing notification settings as JSON',

    -- Indexes
    INDEX idx_plan_id (plan_id),
    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Briefing configuration main table';

-- Create additional indexes for performance optimization
CREATE INDEX idx_briefing_plan_active ON briefing_configurations (plan_id, is_active);

-- Add JSON functional indexes for frequently queried JSON fields (MySQL 8.0+)
-- These indexes improve performance when querying JSON fields
-- Note: These will be ignored in MySQL versions < 8.0

-- Index for content settings sensitivity type
ALTER TABLE briefing_configurations
ADD INDEX idx_content_sensitivity ((CAST(content_settings->'$.sensitivityType' AS CHAR(50))));

-- Index for content settings source types
ALTER TABLE briefing_configurations
ADD INDEX idx_content_source_types ((CAST(content_settings->'$.sourceTypes' AS CHAR(100) ARRAY)));

-- Create views for common queries
CREATE VIEW v_active_briefing_configurations AS
SELECT
    id,
    name,
    plan_id,
    is_active,
    content_settings,
    threshold_settings,
    level_settings,
    daily_briefing_is_active,
    daily_reception_settings,
    weekly_briefing_is_active,
    weekly_reception_settings,
    monthly_briefing_is_active，
    monthly_reception_settings
FROM briefing_configurations
WHERE is_active = TRUE;

-- Performance optimization: Analyze tables after creation
ANALYZE TABLE briefing_configurations;
