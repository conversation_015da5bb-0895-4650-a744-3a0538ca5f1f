# 测试环境配置
schedule:
  collector:
    enabled: false # 测试环境禁用数据收集
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    batch-size: 10 # 测试环境很小的批处理大小
    max-retries: 1 # 最少的重试次数
    retry-interval: 1000 # 最短的重试间隔
  cleaner:
    enabled: false # 测试环境禁用数据清洗
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    batch-size: 10 # 很小的批处理大小
  aggregator:
    enabled: false # 测试环境禁用数据聚合
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
  elasticsearch-sync:
    enabled: false # 测试环境禁用ES同步
    cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
  billing:
    enabled: false # 测试环境禁用计费系统定时任务
    expiration-check-cron: "-" # 禁用定时任务
    expiration-warning-cron: "-" # 禁用定时任务
    environment: "test" # 环境标识
    warning-days: 1 # 测试环境1天警告
    batch-size: 10 # 很小的批处理大小

# 测试环境日志配置
logging:
  level:
    root: WARN # 测试环境减少日志输出
    com.czb.hn: INFO
    org.springframework.scheduling: WARN # 减少定时任务日志

# 禁用外部服务
sina:
  api:
    enabled: false # 测试环境禁用新浪API

elasticsearch:
  enabled: false # 测试环境禁用Elasticsearch

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: false
    displayRequestDuration: true
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: true
