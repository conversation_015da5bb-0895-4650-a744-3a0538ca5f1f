//package com.czb.hn.client;
//
//import com.czb.hn.util.PdfJwtUtil;
//
//public class PdfTest {
//    public static void main(String[] args) {
//        String pdfToken = PdfJwtUtil.generatePdfToken("system-task");
////        String url = "https://lanhuapp.com/web/#/item/project/product?pid=1b2d460c-cb11-4572-89f1-aecd4a1ebac5&image_id=df2f799d-2ed6-409c-bd2c-e8059728c5e7&tid=959e8422-3cac-4522-aa87-e9ce84b8a590&docId=df2f799d-2ed6-409c-bd2c-e8059728c5e7&docType=axure&versionId=37d4d692-7da6-43ab-b25a-c79ac53d7282&pageId=05281d52303d4648825f8ef21ba3f7f9&parentId=67ce9551e2364cf189f9ffe85971b550&share_type=quickShare"; // 你要转成PDF的页面
////        String url = "http://localhost:3000/智眸舆情监测系统-蓝湖.html"; // 你要转成PDF的页面
////        String url = "http://localhost:3000/test-icon.html"; // 你要转成PDF的页面
//        String url = "https://ifs-ycjf-test.ycfin.net/service"; // 你要转成PDF的页面
//        PdfClient pdfClient = new PdfClient();
//        byte[] pdfBytes = pdfClient.generatePdf(url, pdfToken);
//
//        // 保存为本地文件
//        try (java.io.FileOutputStream fos = new java.io.FileOutputStream("test-chart.pdf")) {
//            fos.write(pdfBytes);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        System.out.println("PDF 生成完成！");
//    }
//}
