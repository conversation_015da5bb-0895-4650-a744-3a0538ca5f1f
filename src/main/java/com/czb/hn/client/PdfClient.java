package com.czb.hn.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.Map;

/**
 * PDF生成客户端，用于调用Puppeteer服务生成PDF
 */
@Component
public class PdfClient {
    private static final Logger log = LoggerFactory.getLogger(PdfClient.class);
    
    private final WebClient webClient;

    /**
     * 构造函数，从配置文件中读取服务地址和超时时间
     * 
     * @param baseUrl Puppeteer服务地址
     * @param timeoutSeconds 超时时间（秒）
     */
    public PdfClient(
            @Value("${pdf.service.base-url:http://127.0.0.1:3000}") String baseUrl,
            @Value("${pdf.service.timeout:60}") int timeoutSeconds) {
        
        log.info("初始化PDF客户端，服务地址: {}, 超时时间: {}秒", baseUrl, timeoutSeconds);
        
        // 增加缓冲区大小限制到 10MB
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();

        // 设置可配置的超时时间
        HttpClient httpClient = HttpClient.create()
                .responseTimeout(Duration.ofSeconds(timeoutSeconds));

        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .exchangeStrategies(strategies)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    /**
     * 生成PDF
     * 
     * @param url 要生成PDF的URL
     * @param shareCode 分享码
     * @return PDF字节数组
     * @throws IllegalArgumentException 参数错误时抛出
     */
    public byte[] generatePdf(String url, String shareCode) {
        String targetUrl = appendShareCode(url, shareCode);
        long startTime = System.currentTimeMillis();
        log.info("开始生成PDF，URL: {}", targetUrl);
        
        try {
            byte[] result = webClient.post()
                    .uri("/generate-pdf")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(Map.of("url", targetUrl))
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .block();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("PDF生成完成，耗时: {}毫秒，大小: {}字节", duration, result != null ? result.length : 0);
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("PDF生成失败，耗时: {}毫秒", duration, e);
            throw new IllegalArgumentException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带缩放参数的PDF生成
     * 
     * @param url 要生成PDF的URL
     * @param shareCode 分享码
     * @param scale 缩放比例
     * @return PDF字节数组
     * @throws IllegalArgumentException 参数错误时抛出
     */
    public byte[] generatePdf(String url, String shareCode, double scale) {
        String targetUrl = appendShareCode(url, shareCode);
        long startTime = System.currentTimeMillis();
        log.info("开始生成PDF，URL: {}，缩放比例: {}", targetUrl, scale);
        
        try {
            byte[] result = webClient.post()
                    .uri("/generate-pdf")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(Map.of(
                            "url", targetUrl,
                            "scale", scale
                    ))
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .block();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("PDF生成完成，耗时: {}毫秒，大小: {}字节", duration, result != null ? result.length : 0);
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("PDF生成失败，耗时: {}毫秒", duration, e);
            throw new IllegalArgumentException("PDF生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将分享码拼接到URL上
     */
    private String appendShareCode(String url, String shareCode) {
        if (url == null || shareCode == null) {
            return url;
        }
        
        return url.contains("?") 
                ? url + "&shareCode=" + shareCode 
                : url + "?shareCode=" + shareCode;
    }
}
