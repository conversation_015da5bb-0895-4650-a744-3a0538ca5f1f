package com.czb.hn.scheduler;

import com.czb.hn.service.sina.SinaTokenCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 新浪舆情通令牌清理定时任务
 * 定期清理过期的令牌记录，保持数据库整洁
 */
@Component
@ConditionalOnProperty(name = "sina.token.cache.cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class SinaTokenCleanupScheduler {

    private static final Logger logger = LoggerFactory.getLogger(SinaTokenCleanupScheduler.class);

    @Autowired
    private SinaTokenCacheService tokenCacheService;

    /**
     * 每天凌晨2点清理过期令牌
     */
    @Scheduled(cron = "${sina.token.cache.cleanup.cron:0 0 2 * * ?}")
    public void cleanupExpiredTokens() {
        try {
            logger.info("Starting scheduled cleanup of expired tokens...");
            tokenCacheService.cleanupExpiredTokens();
            logger.info("Completed scheduled cleanup of expired tokens");
        } catch (Exception e) {
            logger.error("Error during scheduled token cleanup: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时检查一次令牌状态（用于监控）
     */
    @Scheduled(cron = "${sina.token.cache.monitor.cron:0 0 * * * ?}")
    public void monitorTokenStatus() {
        try {
            logger.debug("Monitoring token cache status...");
            // 这里可以添加更多的监控逻辑，比如检查即将过期的令牌
            // 或者发送告警通知等
        } catch (Exception e) {
            logger.error("Error during token status monitoring: {}", e.getMessage(), e);
        }
    }
}
