package com.czb.hn.exception;

/**
 * Exception thrown when enterprise subscription is not found
 */
public class SubscriptionNotFoundException extends BillingException {

    private final String enterpriseIdentifier;

    public SubscriptionNotFoundException(String enterpriseIdentifier) {
        super(String.format("未找到企业订阅信息。企业标识: %s", enterpriseIdentifier), "SUBSCRIPTION_NOT_FOUND");
        this.enterpriseIdentifier = enterpriseIdentifier;
    }

    public SubscriptionNotFoundException(String enterpriseIdentifier, String customMessage) {
        super(customMessage, "SUBSCRIPTION_NOT_FOUND");
        this.enterpriseIdentifier = enterpriseIdentifier;
    }

    public String getEnterpriseIdentifier() {
        return enterpriseIdentifier;
    }
}
