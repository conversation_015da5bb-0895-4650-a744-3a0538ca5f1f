package com.czb.hn.exception;

import java.time.LocalDate;

/**
 * Exception thrown when enterprise subscription has expired
 */
public class SubscriptionExpiredException extends BillingException {

    private final String enterpriseIdentifier;
    private final LocalDate expiredDate;

    public SubscriptionExpiredException(String enterpriseIdentifier, LocalDate expiredDate) {
        super(String.format("企业订阅已过期。企业标识: %s, 过期日期: %s", enterpriseIdentifier, expiredDate), "SUBSCRIPTION_EXPIRED");
        this.enterpriseIdentifier = enterpriseIdentifier;
        this.expiredDate = expiredDate;
    }

    public SubscriptionExpiredException(String enterpriseIdentifier, LocalDate expiredDate, String customMessage) {
        super(customMessage, "SUBSCRIPTION_EXPIRED");
        this.enterpriseIdentifier = enterpriseIdentifier;
        this.expiredDate = expiredDate;
    }

    public String getEnterpriseIdentifier() {
        return enterpriseIdentifier;
    }

    public LocalDate getExpiredDate() {
        return expiredDate;
    }
}
