package com.czb.hn.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for billing-related exceptions
 * Provides consistent error responses for subscription and billing issues
 */
@RestControllerAdvice
@Slf4j
public class GlobalBillingExceptionHandler {

    /**
     * Handle subscription expired exceptions
     */
    @ExceptionHandler(SubscriptionExpiredException.class)
    public ResponseEntity<Map<String, Object>> handleSubscriptionExpired(SubscriptionExpiredException ex) {
        log.warn("Subscription expired: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "SUBSCRIPTION_EXPIRED",
                ex.getMessage(),
                HttpStatus.FORBIDDEN
        );
        
        // Add specific fields for subscription expired
        errorResponse.put("enterpriseIdentifier", ex.getEnterpriseIdentifier());
        errorResponse.put("expiredDate", ex.getExpiredDate());
        errorResponse.put("action", "请联系管理员续费");
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * Handle subscription not found exceptions
     */
    @ExceptionHandler(SubscriptionNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleSubscriptionNotFound(SubscriptionNotFoundException ex) {
        log.warn("Subscription not found: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "SUBSCRIPTION_NOT_FOUND",
                ex.getMessage(),
                HttpStatus.NOT_FOUND
        );
        
        // Add specific fields for subscription not found
        errorResponse.put("enterpriseIdentifier", ex.getEnterpriseIdentifier());
        errorResponse.put("action", "请联系管理员创建订阅");
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Handle general billing exceptions
     */
    @ExceptionHandler(BillingException.class)
    public ResponseEntity<Map<String, Object>> handleBillingException(BillingException ex) {
        log.error("Billing exception: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
                ex.getErrorCode(),
                ex.getMessage(),
                HttpStatus.BAD_REQUEST
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle illegal argument exceptions (validation errors)
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgument(IllegalArgumentException ex) {
        log.warn("Validation error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "VALIDATION_ERROR",
                ex.getMessage(),
                HttpStatus.BAD_REQUEST
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Create standardized error response
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message, HttpStatus status) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("status", status.value());
        errorResponse.put("timestamp", LocalDateTime.now());
        errorResponse.put("path", getCurrentRequestPath());
        
        return errorResponse;
    }

    /**
     * Get current request path (if available)
     */
    private String getCurrentRequestPath() {
        try {
            // Try to get current request path from Spring context
            return org.springframework.web.context.request.RequestContextHolder
                    .currentRequestAttributes()
                    .getAttribute(
                            org.springframework.web.context.request.RequestAttributes.REFERENCE_REQUEST,
                            org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST
                    ).toString();
        } catch (Exception e) {
            return "unknown";
        }
    }
}
