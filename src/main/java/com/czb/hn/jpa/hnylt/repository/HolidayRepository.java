package com.czb.hn.jpa.hnylt.repository;

import com.czb.hn.jpa.hnylt.entity.HolidayEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/04/01  10:59
 */
public interface HolidayRepository extends JpaRepository<HolidayEntity, Long> {

    Optional<HolidayEntity> findByDate(LocalDate date);

    List<HolidayEntity> findByDateIn(List<LocalDate> dateList);

    Page<HolidayEntity> findByConfigureTypeOrderByDateDesc(Integer configureType, Pageable pageable);

    @Query(value = "SELECT date FROM HolidayEntity " +
            "WHERE type = 1")
    List<LocalDate> findAllHolidays();
    @Query(value = "SELECT date FROM HolidayEntity " +
            "WHERE type = 2")
    List<LocalDate> findAllExtraWorkdays();

    @Query(value = " SELECT COUNT(*) FROM holiday_configuration " +
            " WHERE `date` BETWEEN :startDate AND :endDate " +
            " AND `type` = 1 ", nativeQuery = true)
    Integer getNonWorkingDays(@Param("startDate") LocalDate startDate, @Param("endDate")LocalDate endDate);

    @Query(value = "SELECT COUNT(*) FROM holiday_configuration " +
            "WHERE `date` BETWEEN :startDate AND :endDate " +
            "AND `type` = 2", nativeQuery = true)
    Integer getSpecialWorkingDays(@Param("startDate") LocalDate startDate, @Param("endDate")LocalDate endDate);
}
