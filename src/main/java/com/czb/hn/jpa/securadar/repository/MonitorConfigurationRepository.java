package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.MonitorConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MonitorConfigurationRepository extends JpaRepository<MonitorConfiguration, Long> {
    /**
     * Find monitor configurations by plan ID
     */
    @Query("SELECT mc FROM MonitorConfiguration mc WHERE mc.planId = :planId")
    MonitorConfiguration findByPlanId(Long planId);

    /**
     * Check if there are any monitor configurations for a given plan ID
     */
    @Query("SELECT COUNT(mc) > 0 FROM MonitorConfiguration mc WHERE mc.planId = :planId")
    Boolean existsByPlanId(Long planId);
}
