package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.ShareLinkEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ShareLinkRepository extends JpaRepository<ShareLinkEntity, Long> {
    Optional<ShareLinkEntity> findByShareCode(String shareCode);
}