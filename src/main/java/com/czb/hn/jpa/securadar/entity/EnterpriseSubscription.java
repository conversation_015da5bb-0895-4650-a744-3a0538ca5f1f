package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.SubscriptionStatus;
import com.czb.hn.enums.converter.SubscriptionStatusConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Enterprise Subscription Entity
 * Manages subscription status and expiration for enterprises
 */
@Entity
@Table(name = "enterprise_subscriptions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EnterpriseSubscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Enterprise ID for tenant isolation
     */
    @Column(name = "enterprise_id", length = 255)
    private String enterpriseId;

    /**
     * Enterprise unified social credit code (统一社会信用代码)
     * 18-digit code for enterprise identification in China
     */
    @Column(name = "enterprise_credit_code", length = 18)
    private String enterpriseCreditCode;

    /**
     * Subscription start date
     */
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    /**
     * Subscription end date
     */
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;

    /**
     * Subscription status
     */
    @Convert(converter = SubscriptionStatusConverter.class)
    @Column(name = "status", nullable = false, length = 20)
    private SubscriptionStatus status;

    /**
     * Auto renewal flag
     */
    @Column(name = "auto_renew")
    @Builder.Default
    private Boolean autoRenew = false;

    /**
     * Additional notes or comments
     */
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    /**
     * Creation timestamp
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * Last update timestamp
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * Check if subscription is currently active and not expired
     */
    public boolean isActiveAndValid() {
        if (status != SubscriptionStatus.ACTIVE) {
            return false;
        }

        LocalDate today = LocalDate.now();
        return endDate.isAfter(today) || endDate.isEqual(today);
    }

    /**
     * Check if subscription is expired
     */
    public boolean isExpired() {
        LocalDate today = LocalDate.now();
        return endDate.isBefore(today);
    }

    /**
     * Get days until expiration (negative if already expired)
     */
    public long getDaysUntilExpiration() {
        LocalDate today = LocalDate.now();
        return today.until(endDate).getDays();
    }

    /**
     * Check if subscription is expiring soon (within specified days)
     */
    public boolean isExpiringSoon(int days) {
        long daysUntilExpiration = getDaysUntilExpiration();
        return daysUntilExpiration >= 0 && daysUntilExpiration <= days;
    }
}
