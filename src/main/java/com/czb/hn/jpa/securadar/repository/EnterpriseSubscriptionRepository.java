package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.enums.SubscriptionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EnterpriseSubscription entity
 * Provides data access methods for enterprise subscription management
 */
@Repository
public interface EnterpriseSubscriptionRepository extends JpaRepository<EnterpriseSubscription, Long> {

    /**
     * Find subscription by enterprise ID
     */
    Optional<EnterpriseSubscription> findByEnterpriseId(String enterpriseId);

    /**
     * Find subscription by enterprise credit code
     */
    Optional<EnterpriseSubscription> findByEnterpriseCreditCode(String enterpriseCreditCode);

    /**
     * Find subscription by either enterprise ID or credit code
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.enterpriseId = :identifier OR es.enterpriseCreditCode = :identifier")
    Optional<EnterpriseSubscription> findByEnterpriseIdOrCreditCode(@Param("identifier") String identifier);

    /**
     * Find active subscription by enterprise ID
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.enterpriseId = :enterpriseId AND es.status = 'ACTIVE'")
    Optional<EnterpriseSubscription> findActiveByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    /**
     * Find active subscription by enterprise credit code
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.enterpriseCreditCode = :creditCode AND es.status = 'ACTIVE'")
    Optional<EnterpriseSubscription> findActiveByEnterpriseCreditCode(@Param("creditCode") String creditCode);

    /**
     * Find active subscription by either enterprise ID or credit code
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE (es.enterpriseId = :identifier OR es.enterpriseCreditCode = :identifier) AND es.status = 'ACTIVE'")
    Optional<EnterpriseSubscription> findActiveByEnterpriseIdOrCreditCode(@Param("identifier") String identifier);

    /**
     * Find all subscriptions by status
     */
    List<EnterpriseSubscription> findByStatus(SubscriptionStatus status);

    /**
     * Find subscriptions expiring before the given date
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.status = 'ACTIVE' AND es.endDate <= :date")
    List<EnterpriseSubscription> findExpiringBefore(@Param("date") LocalDate date);

    /**
     * Find subscriptions expiring within the specified number of days
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.status = 'ACTIVE' AND es.endDate BETWEEN CURRENT_DATE AND :endDate")
    List<EnterpriseSubscription> findExpiringWithinDays(@Param("endDate") LocalDate endDate);

    /**
     * Find expired subscriptions that are still marked as active
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.status = 'ACTIVE' AND es.endDate < CURRENT_DATE")
    List<EnterpriseSubscription> findExpiredButStillActive();

    /**
     * Check if enterprise has active subscription by enterprise ID
     */
    @Query("SELECT COUNT(es) > 0 FROM EnterpriseSubscription es WHERE es.enterpriseId = :enterpriseId AND es.status = 'ACTIVE' AND es.endDate >= CURRENT_DATE")
    boolean hasActiveSubscription(@Param("enterpriseId") String enterpriseId);

    /**
     * Check if enterprise has active subscription by credit code
     */
    @Query("SELECT COUNT(es) > 0 FROM EnterpriseSubscription es WHERE es.enterpriseCreditCode = :creditCode AND es.status = 'ACTIVE' AND es.endDate >= CURRENT_DATE")
    boolean hasActiveSubscriptionByCreditCode(@Param("creditCode") String creditCode);

    /**
     * Check if enterprise has active subscription by either ID or credit code
     */
    @Query("SELECT COUNT(es) > 0 FROM EnterpriseSubscription es WHERE (es.enterpriseId = :identifier OR es.enterpriseCreditCode = :identifier) AND es.status = 'ACTIVE' AND es.endDate >= CURRENT_DATE")
    boolean hasActiveSubscriptionByIdentifier(@Param("identifier") String identifier);

    /**
     * Count active subscriptions
     */
    @Query("SELECT COUNT(es) FROM EnterpriseSubscription es WHERE es.status = 'ACTIVE'")
    long countActiveSubscriptions();

    /**
     * Count expired subscriptions
     */
    @Query("SELECT COUNT(es) FROM EnterpriseSubscription es WHERE es.status = 'EXPIRED' OR (es.status = 'ACTIVE' AND es.endDate < CURRENT_DATE)")
    long countExpiredSubscriptions();

    /**
     * Find subscriptions by date range
     */
    @Query("SELECT es FROM EnterpriseSubscription es WHERE es.endDate BETWEEN :startDate AND :endDate")
    List<EnterpriseSubscription> findByEndDateBetween(@Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * Check if enterprise ID exists
     */
    boolean existsByEnterpriseId(String enterpriseId);

    /**
     * Check if enterprise credit code exists
     */
    boolean existsByEnterpriseCreditCode(String enterpriseCreditCode);
}
