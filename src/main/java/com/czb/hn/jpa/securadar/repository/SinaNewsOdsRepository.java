package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.SinaNewsOdsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 新浪舆情ODS数据仓库接口
 */
@Repository
public interface SinaNewsOdsRepository extends JpaRepository<SinaNewsOdsEntity, Long> {
    
    /**
     * 查找未处理的记录
     * 
     * @return 未处理的ODS记录列表
     */
    @Query("SELECT o FROM SinaNewsOdsEntity o WHERE o.processed = false ORDER BY o.captureTime ASC")
    List<SinaNewsOdsEntity> findUnprocessedRecords();
    
    /**
     * 统计未处理的记录数
     * 
     * @return 未处理的记录数
     */
    @Query("SELECT COUNT(o) FROM SinaNewsOdsEntity o WHERE o.processed = false")
    long countUnprocessedRecords();
    
    /**
     * 根据内容ID查找记录
     * 
     * @param contentId 内容ID
     * @return ODS记录
     */
    Optional<SinaNewsOdsEntity> findByContentId(String contentId);
    
    /**
     * 根据发布时间范围查找记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return ODS记录列表
     */
    @Query("SELECT o FROM SinaNewsOdsEntity o WHERE o.publishTime BETWEEN :startTime AND :endTime")
    List<SinaNewsOdsEntity> findByPublishTimeBetween(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据来源类型查找记录
     * 
     * @param originType 来源类型
     * @return ODS记录列表
     */
    @Query("SELECT o FROM SinaNewsOdsEntity o WHERE o.originType = :originType AND o.processed = false")
    List<SinaNewsOdsEntity> findByOriginTypeAndNotProcessed(@Param("originType") String originType);
    
    /**
     * 根据情感类型查找记录
     * 
     * @param emotion 情感类型
     * @return ODS记录列表
     */
    @Query("SELECT o FROM SinaNewsOdsEntity o WHERE o.emotion = :emotion AND o.processed = false")
    List<SinaNewsOdsEntity> findByEmotionAndNotProcessed(@Param("emotion") String emotion);
    
    /**
     * 根据数据偏移量查找记录
     */
    @Query("SELECT o FROM SinaNewsOdsEntity o WHERE o.dataOffset > :offset ORDER BY o.dataOffset ASC")
    List<SinaNewsOdsEntity> findByDataOffsetGreaterThan(@Param("offset") Long offset);
    
    /**
     * 查找最大数据偏移量
     */
    @Query("SELECT MAX(o.dataOffset) FROM SinaNewsOdsEntity o")
    Long findMaxDataOffset();
} 