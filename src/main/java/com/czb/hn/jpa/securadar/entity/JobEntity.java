package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 任务实体类
 */
@Entity
@Table(name = "job")
@Data
public class JobEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 方案ID */
    @Column(name = "plan_id")
    private Long planId;

    /** 任务名称 */
    @Column(name = "name", nullable = false)
    private String name;

    /** 是否启用 */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    /** 任务周期类型（DAILY/WEEKLY/MONTHLY） */
    @Column(name = "cycle_type", length = 20)
    private String cycleType;

    /** 执行日（周几/每月几号） */
    @Column(name = "execution_day", length = 10)
    private String executionDay;

    /** cron表达式 */
    @Column(name = "cron", nullable = false)
    private String cron;

    /** 任务处理器（如 bulletinGeneratorJob#generate） */
    @Column(name = "handler", nullable = false)
    private String handler;

    /** 任务参数（JSON） */
    @Column(name = "job_params", columnDefinition = "TEXT")
    private String jobParams;

    /** 首次生效时间 */
    @Column(name = "begin_time")
    private LocalDateTime beginTime;

    /** 结束时间 */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /** 创建时间 */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    /** 更新时间 */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
} 