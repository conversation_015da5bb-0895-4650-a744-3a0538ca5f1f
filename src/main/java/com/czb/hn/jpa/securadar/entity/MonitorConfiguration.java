package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Data
@Table(name = "monitor_configurations", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_name", columnList = "name")
})
public class MonitorConfiguration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 255)
    private String name;

    @Column(name = "plan_id")
    private Long planId;

    @Column(name = "time", nullable = false)
    private Integer time;

    @Column(name = "start_time")
    private String startTime;

    @Column(name = "end_time")
    private String endTime;

    @Column(name = "sort_rule", nullable = false)
    private Integer sortRule;

    @Column(name = "sensitivity_type", nullable = false)
    private Integer sensitivityType;

    @Column(name = "similarity_display_rule", nullable = false)
    private Boolean SimilarityDisplayRule;

    @Column(name = "match_method", nullable = false)
    private Integer MatchMethod;

    @Column(name = "media_type")
    private List<String> MediaType;

    @Column(name = "media_type_second")
    private List<String> MediaTypeSecond;

    @Column(name = "content_type", nullable = false)
    private List<Integer> ContentType;

    @Column(name = "is_original", nullable = false)
    private Integer isOriginal;

    @Column(name = "image_text_mode", nullable = false)
    private Integer imageTextMode;

    @Column(name = "second_trades")
    private List<String> SecondTrades;

    @Column(name = "author_followers_count_min")
    private Long authorFollowersCountMin;

    @Column(name = "author_followers_count_max")
    private Long authorFollowersCountMax;

    @Column(name = "media_level")
    private List<String> MediaLevel;
}
