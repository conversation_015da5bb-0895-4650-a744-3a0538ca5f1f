package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for AlertNotificationQueue entity
 * Provides data access methods for notification queue management
 */
@Repository
public interface AlertNotificationQueueRepository extends JpaRepository<AlertNotificationQueue, Long> {

        /**
         * Find notifications ready to be processed
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.scheduledTime <= :currentTime AND anq.attemptCount < 3 " +
                        "ORDER BY anq.scheduledTime ASC")
        List<AlertNotificationQueue> findReadyToProcess(
                        @Param("status") NotificationStatus status,
                        @Param("currentTime") LocalDateTime currentTime);

        /**
         * Find notifications ready for retry
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.nextRetryTime <= :currentTime AND anq.attemptCount < 3 " +
                        "ORDER BY anq.nextRetryTime ASC")
        List<AlertNotificationQueue> findReadyForRetry(
                        @Param("status") NotificationStatus status,
                        @Param("currentTime") LocalDateTime currentTime);

        /**
         * Find notifications by enterprise ID and status
         */
        Page<AlertNotificationQueue> findByEnterpriseIdAndStatusOrderByScheduledTimeDesc(
                        String enterpriseId, NotificationStatus status, Pageable pageable);

        /**
         * Find notifications by enterprise ID within time range
         */
        Page<AlertNotificationQueue> findByEnterpriseIdAndScheduledTimeBetweenOrderByScheduledTimeDesc(
                        String enterpriseId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        // Note: findByAlertIdOrderByScheduledTimeDesc method removed as alertId field
        // no longer exists

        /**
         * Find no-alert notifications (where notification_type is NO_ALERT)
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE anq.notificationType = 'NO_ALERT' " +
                        "ORDER BY anq.scheduledTime DESC")
        List<AlertNotificationQueue> findNoAlertNotifications();

        /**
         * Find notifications by configuration ID
         */
        Page<AlertNotificationQueue> findByConfigurationIdOrderByScheduledTimeDesc(
                        Long configurationId, Pageable pageable);

        /**
         * Count notifications by status and enterprise
         */
        long countByEnterpriseIdAndStatus(String enterpriseId, NotificationStatus status);

        /**
         * Count notifications by status and type
         */
        long countByStatusAndNotificationType(NotificationStatus status, NotificationType notificationType);

        /**
         * Find overdue notifications (scheduled time passed but still pending)
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.scheduledTime < :overdueTime " +
                        "ORDER BY anq.scheduledTime ASC")
        List<AlertNotificationQueue> findOverdueNotifications(
                        @Param("status") NotificationStatus status,
                        @Param("overdueTime") LocalDateTime overdueTime);

        /**
         * Find stuck notifications (processing for too long)
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.processedAt < :stuckTime " +
                        "ORDER BY anq.processedAt ASC")
        List<AlertNotificationQueue> findStuckNotifications(
                        @Param("status") NotificationStatus status,
                        @Param("stuckTime") LocalDateTime stuckTime);

        /**
         * Delete old completed notifications
         */
        @Modifying
        @Query("DELETE FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.processedAt < :cutoffDate")
        int deleteOldCompletedNotifications(
                        @Param("status") NotificationStatus status,
                        @Param("cutoffDate") LocalDateTime cutoffDate);

        /**
         * Delete old failed notifications that cannot be retried
         */
        @Modifying
        @Query("DELETE FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = :status AND anq.attemptCount >= 3 AND anq.updatedAt < :cutoffDate")
        int deleteOldFailedNotifications(
                        @Param("status") NotificationStatus status,
                        @Param("cutoffDate") LocalDateTime cutoffDate);

        // Note: existsByAlertIdAndConfigurationId method removed as alertId field no
        // longer exists

        /**
         * Check if no-alert notification exists for configuration in time range
         */
        @Query("SELECT COUNT(anq) > 0 FROM AlertNotificationQueue anq WHERE " +
                        "anq.configurationId = :configurationId AND " +
                        "anq.notificationType = 'NO_ALERT' AND anq.scheduledTime BETWEEN :startTime AND :endTime")
        boolean existsNoAlertNotificationInTimeRange(
                        @Param("configurationId") Long configurationId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Find notifications with complex filtering
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "(:enterpriseId IS NULL OR anq.enterpriseId = :enterpriseId) AND " +
                        "(:configurationId IS NULL OR anq.configurationId = :configurationId) AND " +
                        "(:status IS NULL OR anq.status = :status) AND " +
                        "(:notificationType IS NULL OR anq.notificationType = :notificationType) AND " +
                        "(:startTime IS NULL OR anq.scheduledTime >= :startTime) AND " +
                        "(:endTime IS NULL OR anq.scheduledTime <= :endTime) " +
                        "ORDER BY anq.scheduledTime DESC")
        Page<AlertNotificationQueue> findWithFilters(
                        @Param("enterpriseId") String enterpriseId,
                        @Param("configurationId") Long configurationId,
                        @Param("status") NotificationStatus status,
                        @Param("notificationType") NotificationType notificationType,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * Get notification statistics for enterprise
         */
        @Query("SELECT " +
                        "COUNT(CASE WHEN anq.status = 'PENDING' THEN 1 END) as pendingCount, " +
                        "COUNT(CASE WHEN anq.status = 'PROCESSING' THEN 1 END) as processingCount, " +
                        "COUNT(CASE WHEN anq.status = 'COMPLETED' THEN 1 END) as completedCount, " +
                        "COUNT(CASE WHEN anq.status = 'FAILED' THEN 1 END) as failedCount " +
                        "FROM AlertNotificationQueue anq WHERE anq.enterpriseId = :enterpriseId " +
                        "AND anq.scheduledTime BETWEEN :startTime AND :endTime")
        Object[] getNotificationStatistics(
                        @Param("enterpriseId") String enterpriseId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Reset stuck notifications back to pending
         */
        @Modifying
        @Query("UPDATE AlertNotificationQueue anq SET anq.status = 'PENDING', anq.processedAt = NULL " +
                        "WHERE anq.status = 'PROCESSING' AND anq.processedAt < :stuckTime")
        int resetStuckNotifications(@Param("stuckTime") LocalDateTime stuckTime);

        /**
         * Find notifications scheduled for a specific time window
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.status = 'PENDING' AND anq.scheduledTime BETWEEN :startTime AND :endTime " +
                        "ORDER BY anq.scheduledTime ASC")
        List<AlertNotificationQueue> findScheduledInTimeWindow(
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Find pending notifications for a configuration within a time window
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE " +
                        "anq.configurationId = :configurationId AND anq.status = 'PENDING' " +
                        "AND anq.scheduledTime BETWEEN :startTime AND :endTime " +
                        "ORDER BY anq.scheduledTime ASC")
        List<AlertNotificationQueue> findPendingNotificationInTimeWindow(
                        @Param("configurationId") Long configurationId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Find the most recent notification for a configuration
         */
        @Query("SELECT anq FROM AlertNotificationQueue anq WHERE anq.configurationId = :configurationId " +
                        "ORDER BY anq.scheduledTime DESC")
        Optional<AlertNotificationQueue> findTopByConfigurationIdOrderByScheduledTimeDesc(
                        @Param("configurationId") Long configurationId);
}
