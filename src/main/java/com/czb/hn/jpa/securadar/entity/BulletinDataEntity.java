package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 简报数据实体
 */
@Entity
@Table(name = "bulletin_data")
@Data
public class BulletinDataEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "job_id", nullable = false)
    private Long jobId;
    
    @Column(name = "bulletin_date", nullable = false)
    private LocalDate bulletinDate;
    
    @Column(name = "bulletin_type", nullable = false, length = 20)
    private String bulletinType;
    
    @Column(name = "status", nullable = false, length = 20)
    private String status;
    
    @Column(name = "generated_time", nullable = false)
    private LocalDateTime generatedTime;
    
    @Column(name = "sent_time")
    private LocalDateTime sentTime;
    
    @Column(name = "bulletin_content")
    @Lob
    private byte[] bulletinContent;
    
    @Column(name = "error_message", length = 500)
    private String errorMessage;
} 