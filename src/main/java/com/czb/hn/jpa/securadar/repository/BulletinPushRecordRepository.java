package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.BulletinPushRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简报推送记录数据访问接口
 */
@Repository
public interface BulletinPushRecordRepository extends JpaRepository<BulletinPushRecordEntity, Long> {
    
    /**
     * 根据生成记录ID查询推送记录
     *
     * @param generationId 生成记录ID
     * @return 推送记录列表
     */
    List<BulletinPushRecordEntity> findByGenerationId(Long generationId);
    
    /**
     * 根据生成记录ID查询推送记录（分页）
     *
     * @param generationId 生成记录ID
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    Page<BulletinPushRecordEntity> findByGenerationId(Long generationId, Pageable pageable);
    
    /**
     * 根据推送时间范围查询推送记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 推送记录列表
     */
    @Query("SELECT p FROM BulletinPushRecordEntity p WHERE p.pushTime BETWEEN :startTime AND :endTime ORDER BY p.pushTime DESC")
    List<BulletinPushRecordEntity> findByPushTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据推送时间范围查询推送记录（分页）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    @Query("SELECT p FROM BulletinPushRecordEntity p WHERE p.pushTime BETWEEN :startTime AND :endTime ORDER BY p.pushTime DESC")
    Page<BulletinPushRecordEntity> findByPushTimeBetween(
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime,
        Pageable pageable
    );
    
    /**
     * 根据推送类型查询推送记录
     *
     * @param pushType 推送类型
     * @return 推送记录列表
     */
    List<BulletinPushRecordEntity> findByPushType(String pushType);
    
    /**
     * 根据推送类型查询推送记录（分页）
     *
     * @param pushType 推送类型
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    Page<BulletinPushRecordEntity> findByPushType(String pushType, Pageable pageable);
    
    /**
     * 根据推送方式查询推送记录
     *
     * @param pushMethod 推送方式
     * @return 推送记录列表
     */
    List<BulletinPushRecordEntity> findByPushMethod(String pushMethod);
    
    /**
     * 根据推送方式查询推送记录（分页）
     *
     * @param pushMethod 推送方式
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    Page<BulletinPushRecordEntity> findByPushMethod(String pushMethod, Pageable pageable);
    
    /**
     * 根据状态查询推送记录
     *
     * @param status 状态
     * @return 推送记录列表
     */
    List<BulletinPushRecordEntity> findByStatus(String status);
    
    /**
     * 根据状态查询推送记录（分页）
     *
     * @param status 状态
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    Page<BulletinPushRecordEntity> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据生成记录ID和推送类型查询推送记录
     *
     * @param generationId 生成记录ID
     * @param pushType 推送类型
     * @return 推送记录列表
     */
    List<BulletinPushRecordEntity> findByGenerationIdAndPushType(Long generationId, String pushType);
    
    /**
     * 根据生成记录ID和推送类型查询推送记录（分页）
     *
     * @param generationId 生成记录ID
     * @param pushType 推送类型
     * @param pageable 分页参数
     * @return 推送记录分页结果
     */
    Page<BulletinPushRecordEntity> findByGenerationIdAndPushType(Long generationId, String pushType, Pageable pageable);
} 