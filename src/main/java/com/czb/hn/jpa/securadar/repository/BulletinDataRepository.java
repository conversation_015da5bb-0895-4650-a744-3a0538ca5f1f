package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.BulletinDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 简报数据仓库接口
 */
@Repository
public interface BulletinDataRepository extends JpaRepository<BulletinDataEntity, Long> {
    
    /**
     * 查询指定任务指定日期的简报数据
     */
    Optional<BulletinDataEntity> findByJobIdAndBulletinDate(Long jobId, LocalDate bulletinDate);
    
    /**
     * 查询指定任务的所有未发送简报
     */
    @Query("SELECT b FROM BulletinDataEntity b WHERE b.jobId = :jobId AND b.status = 'GENERATED' AND b.bulletinDate <= :beforeDate ORDER BY b.bulletinDate")
    List<BulletinDataEntity> findUnsentBulletins(@Param("jobId") Long jobId, @Param("beforeDate") LocalDate beforeDate);
    
    /**
     * 根据任务ID和简报日期更新状态
     */
    @Modifying
    @Transactional
    @Query("UPDATE BulletinDataEntity b SET b.status = :status, b.sentTime = :sentTime WHERE b.jobId = :jobId AND b.bulletinDate = :bulletinDate")
    void updateStatus(@Param("jobId") Long jobId, @Param("bulletinDate") LocalDate bulletinDate, @Param("status") String status, @Param("sentTime") LocalDateTime sentTime);
} 