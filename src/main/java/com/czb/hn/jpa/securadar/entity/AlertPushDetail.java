package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Alert Push Detail Entity
 * Stores detailed records of alert notification push operations
 * Tracks push attempts for each alert across different notification channels
 */
@Entity
@Table(name = "alert_push_details", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_plan_push_type", columnList = "plan_id,push_type"),
        @Index(name = "idx_push_time", columnList = "push_time"),
        @Index(name = "idx_push_status", columnList = "push_status"),
        @Index(name = "idx_enterprise_push_time", columnList = "enterprise_id,push_time"),
        @Index(name = "idx_account_push_type", columnList = "account_info,push_type")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlertPushDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "plan_id", nullable = false)
    private Long planId;

    @Column(name = "alert_config_snapshot_id")
    private Long alertConfigSnapshotId;

    /**
     * Enterprise ID for tenant isolation
     */
    @Column(name = "enterprise_id", nullable = false, length = 255)
    private String enterpriseId;

    /**
     * Account information (username, phone number, or email)
     */
    @Column(name = "account_info", nullable = false, length = 255)
    private String accountInfo;

    /**
     * Push type (EMAIL, SMS, SYSTEM)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "push_type", nullable = false, length = 20)
    private PushType pushType;

    /**
     * Push status (SUCCESS, FAILURE)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "push_status", nullable = false, length = 20)
    private PushStatus pushStatus;

    /**
     * Push timestamp
     */
    @Column(name = "push_time", nullable = false)
    private LocalDateTime pushTime;

    /**
     * Error message if push failed
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * Additional push details or metadata
     */
    @Column(name = "push_details", columnDefinition = "TEXT")
    private String pushDetails;

    /**
     * Number of retry attempts
     */
    @Column(name = "retry_count", nullable = false)
    @Builder.Default
    private Integer retryCount = 0;

    /**
     * Last retry timestamp
     */
    @Column(name = "last_retry_time")
    private LocalDateTime lastRetryTime;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * User who initiated the push operation
     */
    @Column(name = "created_by", length = 255)
    private String createdBy;

    /**
     * Check if this push was successful
     */
    public boolean isSuccessful() {
        return pushStatus == PushStatus.SUCCESS;
    }

    /**
     * Check if this push failed
     */
    public boolean isFailed() {
        return pushStatus == PushStatus.FAILURE;
    }

    /**
     * Check if this push can be retried
     */
    public boolean canRetry() {
        return isFailed() && retryCount < 3; // Maximum 3 retry attempts
    }

    /**
     * Increment retry count and update last retry time
     */
    public void incrementRetryCount() {
        this.retryCount++;
        this.lastRetryTime = LocalDateTime.now();
    }
}
