package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.DetailedSourceType;
import com.czb.hn.enums.EmotionType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MatchType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.UserVerificationType;
import com.czb.hn.enums.converter.ContentCategoryConverter;
import com.czb.hn.enums.converter.ContentTypeConverter;
import com.czb.hn.enums.converter.DetailedSourceTypeConverter;
import com.czb.hn.enums.converter.EmotionTypeConverter;
import com.czb.hn.enums.converter.InformationSensitivityTypeConverter;
import com.czb.hn.enums.converter.MatchTypeConverter;
import com.czb.hn.enums.converter.MediaLevelConverter;
import com.czb.hn.enums.converter.ResultViewTypeConverter;
import com.czb.hn.enums.converter.SourceTypeConverter;
import com.czb.hn.enums.converter.UserVerificationTypeConverter;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 新浪舆情DWD数据实体
 * 存储清洗后的数据，用于数据分析和聚合统计
 */
@Entity
@Data
@Table(name = "sina_news_dwd_test", indexes = {
        @Index(name = "idx_content_id", columnList = "content_id", unique = true),
        @Index(name = "idx_text_id", columnList = "text_id"),
        @Index(name = "idx_publish_time", columnList = "publish_time"),
        @Index(name = "idx_media_type", columnList = "media_type"),
        @Index(name = "idx_emotion", columnList = "emotion"),
        @Index(name = "idx_processed", columnList = "processed_to_dws")
})
public class SinaNewsDwdEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "content_id", nullable = false, length = 64, unique = true)
    private String contentId; // 内容ID，唯一标识

    @Column(name = "text_id", length = 64)
    private String textId; // 微博ID或其他平台的内容ID

    @Column(name = "title", length = 255)
    private String title; // 内容标题，清洗后

    @Column(name = "content", columnDefinition = "LONGTEXT")
    private String content; // 正文内容，清洗后（用于搜索和分析）

    @Column(name = "summary", columnDefinition = "TEXT")
    private String summary; // 内容摘要，清洗后

    @Column(name = "url", length = 512)
    private String url; // 原文链接

    @Column(name = "short_url", columnDefinition = "TEXT")
    private String shortUrl; // 短链接，JSON格式

    @Column(name = "source", length = 64)
    private String source; // 来源（发布设备）

    @Column(name = "source_website", length = 64)
    private String sourceWebsite; // 来源网站

    @Column(name = "capture_website", length = 64)
    private String captureWebsite; // 采集网站

    @Convert(converter = SourceTypeConverter.class)
    @Column(name = "media_type")
    private SourceType mediaType; // 媒体类型（hdlt：互动论坛、wb：微博、wx：微信等）

    @Convert(converter = DetailedSourceTypeConverter.class)
    @Column(name = "media_type_second")
    private DetailedSourceType mediaTypeSecond; // 二级媒体类型（央级数字报、省级数字报等）

    @Convert(converter = MediaLevelConverter.class)
    @Column(name = "media_level")
    private MediaLevel mediaLevel; // 媒体级别（央级、省级、地市、重点、中小、企业商业）

    @Column(name = "news_column", length = 255)
    private String column; // 栏目

    @Column(name = "publish_time")
    private LocalDateTime publishTime; // 发布时间

    @Column(name = "capture_time")
    private LocalDateTime captureTime; // 采集时间

    @Column(name = "process_time")
    private LocalDateTime processTime; // 处理时间（数据清洗的时间）

    // 作者信息
    @Column(name = "author", length = 255)
    private String author; // 作者名称

    @Column(name = "author_id", length = 64)
    private String authorId; // 作者ID（账号）

    @Column(name = "author_gender", length = 16)
    private String authorGender; // 作者性别（m-男，f-女）

    @Column(name = "author_profile_url", length = 512)
    private String authorProfileUrl; // 作者头像URL

    @Column(name = "author_home_page", length = 512)
    private String authorHomePage; // 作者主页

    @Column(name = "author_province", length = 64)
    private String authorProvince; // 作者所在省份

    @Column(name = "author_city", length = 64)
    private String authorCity; // 作者所在城市

    @Column(name = "author_verified")
    private Boolean authorVerified; // 作者是否认证

    @Convert(converter = UserVerificationTypeConverter.class)
    @Column(name = "author_verified_type")
    private UserVerificationType authorVerifiedType; // 作者认证类型（-1：普通, 0：橙V, 1-7：蓝V等）

    @Column(name = "author_tags", length = 255)
    private String authorTags; // 作者标签

    @Column(name = "author_content_count")
    private Long authorContentCount; // 作者内容数量

    @Column(name = "author_following_count")
    private Long authorFollowingCount; // 作者关注数量

    @Column(name = "author_followers_count")
    private Long authorFollowersCount; // 作者粉丝数量

    @Column(name = "author_create_time")
    private LocalDateTime authorCreateTime; // 作者账号创建时间

    // 地理位置信息
    @Column(name = "publish_province", length = 64)
    private String publishProvince; // 发布省份

    @Column(name = "publish_city", length = 64)
    private String publishCity; // 发布城市

    @Column(name = "content_province", length = 512)
    private String contentProvince; // 内容涉及省份

    @Column(name = "content_city", length = 512)
    private String contentCity; // 内容涉及城市

    @Column(name = "location", length = 255)
    private String location; // 定位地

    // 备案信息
    @Column(name = "filing_province", length = 64)
    private String filingProvince; // 备案省份

    @Column(name = "web_filing_number", length = 255)
    private String webFilingNumber; // 网站备案号

    @Column(name = "web_filing_units", length = 255)
    private String webFilingUnits; // 网站备案单位

    @Column(name = "second_trades", columnDefinition = "TEXT")
    private String secondTrades; // 行业标签 娱乐，金融，其他等）

    // 情感分析
    @Convert(converter = EmotionTypeConverter.class)
    @Column(name = "emotion")
    private EmotionType emotion; // 情绪（六元情绪：中性，喜悦，悲伤，愤怒，惊奇，恐惧）

    @Column(name = "sentiment_score")
    private Double sentimentScore; // 情感得分（-1到1，负值表示负面情绪，正值表示正面情绪）

    @Convert(converter = InformationSensitivityTypeConverter.class)
    @Column(name = "sensitivity_type")
    private InformationSensitivityType sensitivityType; // 敏感度级别（0：全部 1：敏感 2：非敏感 3:中性）

    @Column(name = "sensitivity_score", length = 16)
    private String sensitivityScore; // 敏感度得分

    @Convert(converter = ContentTypeConverter.class)
    @Column(name = "content_types")
    private ContentType contentTypes; // 内容类型（1:文本 2:图片 3:短链 4:视频）

    @Column(name = "annotations", columnDefinition = "TEXT")
    private String annotations; // 注解信息（元数据信息）

    // 互动数据
    @Column(name = "comment_count")
    private Long commentCount; // 评论数

    @Column(name = "forward_count")
    private Long forwardCount; // 转发数

    @Column(name = "praise_count")
    private Long praiseCount; // 点赞数

    @Column(name = "share_count")
    private Long shareCount; // 分享数

    @Column(name = "collection_count")
    private Long collectionCount; // 收藏数

    @Column(name = "answer_count")
    private Long answerCount; // 回答数

    @Column(name = "looking_count")
    private Long lookingCount; // 浏览数

    @Column(name = "interaction_count")
    private Long interactionCount; // 总互动量（评论+转发+点赞+分享等）

    // 相似性信息
    @Column(name = "similarity_num")
    private Long similarityNum; // 相似内容数量

    @Column(name = "similarity_tag", length = 64)
    private String similarityTag; // 相似性标签（用于聚类）

    // 转发信息
    @Convert(converter = ContentCategoryConverter.class)
    @Column(name = "is_original")
    private ContentCategory isOriginal; // 是否原创（1:原创 2:转发）

    @Column(name = "is_forward")
    private Boolean isForward; // 是否转发（true:转发 false:非转发）

    @Column(name = "wb_forward_type", length = 64)
    private String wbForwardType; // 微博转发类型

    @Column(name = "root_content_id", length = 64)
    private String rootContentId; // 根内容ID（转发内容的原始ID）

    @Column(name = "comment_text", columnDefinition = "MEDIUMTEXT")
    private String commentText; // 评论文本（清洗后）

    @Column(name = "comment_forward_text", columnDefinition = "MEDIUMTEXT")
    private String commentForwardText; // 转发评论文本（清洗后）

    // 匹配信息
    @Convert(converter = MatchTypeConverter.class)
    @Column(name = "match_type")
    private MatchType matchType; // 匹配类型（1-关键词 2-用户）

    @Column(name = "match_info", length = 255)
    private String matchInfo; // 匹配到的内容（如关键词、用户等）

    @Column(name = "match_ticket", length = 64)
    private String matchTicket; // 匹配的方案标识

    @Column(name = "match_name", length = 255)
    private String matchName; // 匹配方案名称

    // 媒体分类
    @Convert(converter = ResultViewTypeConverter.class)
    @Column(name = "result_view")
    private ResultViewType resultView; // 结果呈现（1:正常，2:噪音）

    // 图片和视频信息
    @Column(name = "images", columnDefinition = "TEXT")
    private String images; // 图片URL，JSON格式

    @Column(name = "ocr_contents", columnDefinition = "MEDIUMTEXT")
    private String ocrContents; // OCR内容，JSON格式（图片识别内容）

    @Column(name = "video_url", columnDefinition = "TEXT")
    private String videoUrl; // 视频URL

    @Column(name = "video_cover_url", length = 512)
    private String videoCoverUrl; // 视频封面URL

    @Column(name = "video_subtitle_text", columnDefinition = "TEXT")
    private String videoSubtitleText; // 视频字幕文本（清洗后）

    @Column(name = "video_audio_text", columnDefinition = "TEXT")
    private String videoAudioText; // 视频音频文本（清洗后）

    @Column(name = "video_background_text", columnDefinition = "TEXT")
    private String videoBackgroundText; // 视频背景文本（清洗后）

    @Column(name = "video_dynamic_subtitle_text", columnDefinition = "TEXT")
    private String videoDynamicSubtitleText; // 视频动态字幕文本（清洗后）

    @Column(name = "video_static_subtitle_text", columnDefinition = "TEXT")
    private String videoStaticSubtitleText; // 视频静态字幕文本（清洗后）

    @Column(name = "info_category", length = 64)
    private String infoCategory; // 信息分类

    @Convert(converter = MediaLevelConverter.class)
    @Column(name = "info_level")
    private MediaLevel infoLevel; // 信息级别

    @Column(name = "area_involved", length = 255)
    private String areaInvolved; // 涉及区域（内容涉及的地理区域）

    // NER信息
    @Column(name = "ner_info_ext", columnDefinition = "TEXT")
    private String nerInfoExt; // 命名实体识别信息扩展

    // 源记录关联
    @Column(name = "ods_id")
    private Long odsId; // ODS记录ID（关联原始数据）

    // 处理标记
    @Column(name = "processed_to_dws")
    private Boolean processedToDws = false; // 是否已处理到DWS层（数据聚合层）
}