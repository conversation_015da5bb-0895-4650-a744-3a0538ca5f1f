package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 简报推送记录实体类
 */
@Entity
@Table(name = "bulletin_push_record")
@Getter
@Setter
public class BulletinPushRecordEntity {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的生成记录ID
     */
    @Column(name = "generation_id", nullable = false)
    private Long generationId;
    
    /**
     * 推送账号（邮箱或手机号）
     */
    @Column(name = "account", nullable = false)
    private String account;
    
    /**
     * 推送类型（邮件、短信）
     */
    @Column(name = "push_type", nullable = false)
    private String pushType;
    
    /**
     * 推送方式（手动、自动）
     */
    @Column(name = "push_method", nullable = false)
    private String pushMethod;
    
    /**
     * 推送时间
     */
    @Column(name = "push_time", nullable = false)
    private LocalDateTime pushTime;
    
    /**
     * 状态（成功、失败）
     */
    @Column(name = "status", nullable = false)
    private String status;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}