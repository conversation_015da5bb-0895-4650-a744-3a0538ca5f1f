package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertConfigurationSnapshot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for AlertConfigurationSnapshot entity
 * Provides data access methods for snapshot management and version control
 */
@Repository
public interface AlertConfigurationSnapshotRepository extends JpaRepository<AlertConfigurationSnapshot, Long> {

    /**
     * Find all snapshots for a specific configuration ordered by version descending
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId ORDER BY acs.versionNumber DESC")
    List<AlertConfigurationSnapshot> findByConfigurationIdOrderByVersionDesc(@Param("configurationId") Long configurationId);

    /**
     * Find all snapshots for a specific configuration with pagination
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId ORDER BY acs.versionNumber DESC")
    Page<AlertConfigurationSnapshot> findByConfigurationId(@Param("configurationId") Long configurationId, Pageable pageable);

    /**
     * Find the currently active snapshot for a configuration
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId AND acs.isActive = true")
    Optional<AlertConfigurationSnapshot> findActiveByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Find snapshot by configuration ID and version number
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId AND acs.versionNumber = :versionNumber")
    Optional<AlertConfigurationSnapshot> findByConfigurationIdAndVersion(@Param("configurationId") Long configurationId, 
                                                                         @Param("versionNumber") Integer versionNumber);

    /**
     * Find the latest snapshot for a configuration
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId ORDER BY acs.versionNumber DESC LIMIT 1")
    Optional<AlertConfigurationSnapshot> findLatestByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Get the maximum version number for a configuration
     */
    @Query("SELECT MAX(acs.versionNumber) FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId")
    Optional<Integer> findMaxVersionByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Count snapshots for a configuration
     */
    @Query("SELECT COUNT(acs) FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId")
    Long countByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Find snapshots created within date range
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.createdAt BETWEEN :startDate AND :endDate ORDER BY acs.createdAt DESC")
    Page<AlertConfigurationSnapshot> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                           @Param("endDate") LocalDateTime endDate, 
                                                           Pageable pageable);

    /**
     * Find snapshots by operation type
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.operationType = :operationType ORDER BY acs.createdAt DESC")
    Page<AlertConfigurationSnapshot> findByOperationType(@Param("operationType") String operationType, Pageable pageable);

    /**
     * Find snapshots created by specific user
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.createdBy = :createdBy ORDER BY acs.createdAt DESC")
    Page<AlertConfigurationSnapshot> findByCreatedBy(@Param("createdBy") String createdBy, Pageable pageable);

    /**
     * Deactivate all snapshots for a configuration
     */
    @Modifying
    @Query("UPDATE AlertConfigurationSnapshot acs SET acs.isActive = false WHERE acs.configurationId = :configurationId")
    void deactivateAllByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Activate specific snapshot and deactivate others
     */
    @Modifying
    @Query("UPDATE AlertConfigurationSnapshot acs SET acs.isActive = CASE WHEN acs.id = :snapshotId THEN true ELSE false END WHERE acs.configurationId = :configurationId")
    void activateSnapshotAndDeactivateOthers(@Param("configurationId") Long configurationId, @Param("snapshotId") Long snapshotId);

    /**
     * Delete old snapshots keeping only the latest N versions
     */
    @Modifying
    @Query("DELETE FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId AND acs.versionNumber < (SELECT MAX(acs2.versionNumber) - :keepVersions FROM AlertConfigurationSnapshot acs2 WHERE acs2.configurationId = :configurationId)")
    void deleteOldSnapshots(@Param("configurationId") Long configurationId, @Param("keepVersions") Integer keepVersions);

    /**
     * Find snapshots with data size larger than threshold
     */
    @Query("SELECT acs FROM AlertConfigurationSnapshot acs WHERE acs.dataSize > :sizeThreshold ORDER BY acs.dataSize DESC")
    Page<AlertConfigurationSnapshot> findLargeSnapshots(@Param("sizeThreshold") Long sizeThreshold, Pageable pageable);

    /**
     * Calculate total storage used by snapshots for a configuration
     */
    @Query("SELECT COALESCE(SUM(acs.dataSize), 0) FROM AlertConfigurationSnapshot acs WHERE acs.configurationId = :configurationId")
    Long calculateTotalStorageByConfigurationId(@Param("configurationId") Long configurationId);

    /**
     * Find configurations with most snapshots
     */
    @Query("SELECT acs.configurationId, COUNT(acs) as snapshotCount FROM AlertConfigurationSnapshot acs GROUP BY acs.configurationId ORDER BY snapshotCount DESC")
    Page<Object[]> findConfigurationsWithMostSnapshots(Pageable pageable);
}
