package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.BulletinContentDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;

/**
 * 简报内容数据访问接口
 */
@Repository
public interface BulletinContentDataRepository extends JpaRepository<BulletinContentDataEntity, Long> {
    
    /**
     * 根据生成记录ID查询简报内容数据
     *
     * @param generationId 生成记录ID
     * @return 简报内容数据
     */
    Optional<BulletinContentDataEntity> findByGenerationId(Long generationId);
}