package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.SinaTokenCache;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 新浪舆情通令牌缓存数据仓库接口
 */
@Repository
public interface SinaTokenCacheRepository extends JpaRepository<SinaTokenCache, Long> {

    /**
     * 根据应用ID查找活跃的令牌
     * 
     * @param appId 应用ID
     * @return 活跃的令牌缓存
     */
    @Query("SELECT t FROM SinaTokenCache t WHERE t.appId = :appId AND t.isActive = true AND t.expireTime > :currentTime ORDER BY t.createdTime DESC")
    Optional<SinaTokenCache> findActiveTokenByAppId(@Param("appId") String appId,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据应用ID查找最新的令牌（无论是否过期）
     * 
     * @param appId 应用ID
     * @return 最新的令牌缓存
     */
    @Query("SELECT t FROM SinaTokenCache t WHERE t.appId = :appId ORDER BY t.createdTime DESC")
    Optional<SinaTokenCache> findLatestTokenByAppId(@Param("appId") String appId);

    /**
     * 统计指定应用ID在指定日期的令牌获取次数
     * 
     * @param appId     应用ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 获取次数
     */
    @Query("SELECT COUNT(t) FROM SinaTokenCache t WHERE t.appId = :appId AND t.createdDate >= :startDate AND t.createdDate < :endDate")
    long countTokenFetchesByAppIdAndDate(@Param("appId") String appId, @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * 获取指定应用ID在指定日期的所有令牌记录
     * 
     * @param appId     应用ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 令牌记录列表
     */
    @Query("SELECT t FROM SinaTokenCache t WHERE t.appId = :appId AND t.createdDate >= :startDate AND t.createdDate < :endDate ORDER BY t.createdTime DESC")
    List<SinaTokenCache> findTokensByAppIdAndDate(@Param("appId") String appId,
            @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 将指定应用ID的所有令牌设置为非活跃状态
     * 
     * @param appId 应用ID
     */
    @Modifying
    @Query("UPDATE SinaTokenCache t SET t.isActive = false, t.updatedAt = :currentTime WHERE t.appId = :appId AND t.isActive = true")
    void deactivateAllTokensByAppId(@Param("appId") String appId, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新令牌的最后使用时间
     * 
     * @param id           令牌ID
     * @param lastUsedTime 最后使用时间
     */
    @Modifying
    @Query("UPDATE SinaTokenCache t SET t.lastUsedTime = :lastUsedTime, t.updatedAt = :currentTime WHERE t.id = :id")
    void updateLastUsedTime(@Param("id") Long id, @Param("lastUsedTime") LocalDateTime lastUsedTime,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 删除过期的令牌记录（清理任务）
     * 
     * @param expireTime 过期时间阈值
     */
    @Modifying
    @Query("DELETE FROM SinaTokenCache t WHERE t.expireTime < :expireTime AND t.isActive = false")
    void deleteExpiredTokens(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查找即将过期的活跃令牌（用于提前刷新）
     * 
     * @param thresholdTime 阈值时间
     * @return 即将过期的令牌列表
     */
    @Query("SELECT t FROM SinaTokenCache t WHERE t.isActive = true AND t.expireTime < :thresholdTime AND t.expireTime > :currentTime")
    List<SinaTokenCache> findTokensNearExpiry(@Param("thresholdTime") LocalDateTime thresholdTime,
            @Param("currentTime") LocalDateTime currentTime);
}
