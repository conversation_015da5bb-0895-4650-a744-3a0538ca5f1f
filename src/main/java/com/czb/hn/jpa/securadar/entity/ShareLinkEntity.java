package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分享链接实体
 */
@Data
@Entity
@Table(name = "share_link")
public class ShareLinkEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "share_code", unique = true, nullable = false, length = 64)
    private String shareCode;

    @Column(name = "origin_url", length = 512)
    private String originUrl;

    @Column(name = "expire_time", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime expireTime;

    @Column(name = "status", nullable = false)
    private Integer status = 0; // 0正常，1撤销

    @Column(name = "create_user_id", length = 64)
    private String createUserId;

    @Column(name = "created_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime UpdatedAt;


}