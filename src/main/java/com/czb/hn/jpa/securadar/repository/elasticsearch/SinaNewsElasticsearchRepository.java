package com.czb.hn.jpa.securadar.repository.elasticsearch;

import com.czb.hn.document.SinaNewsDocument;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 新浪舆情Elasticsearch数据仓库接口
 */
@Repository
public interface SinaNewsElasticsearchRepository extends ElasticsearchRepository<SinaNewsDocument, String> {
    
    /**
     * 根据媒体类型查找文档
     */
    List<SinaNewsDocument> findByMediaType(String mediaType);
    
    /**
     * 根据情感类型查找文档
     */
    List<SinaNewsDocument> findByEmotion(String emotion);
    
    /**
     * 根据敏感度级别查找文档
     */
    List<SinaNewsDocument> findBySensitivityType(Integer sensitivityType);

    
    /**
     * 根据发布时间范围查找文档
     */
    List<SinaNewsDocument> findByPublishTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
} 