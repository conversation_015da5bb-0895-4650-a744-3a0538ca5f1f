package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * 简报内容数据实体，用于存储简报的结构化数据
 */
@Entity
@Table(name = "bulletin_content_data")
@Getter
@Setter
public class BulletinContentDataEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的简报生成记录ID
     */
    @Column(name = "generation_id", nullable = false)
    private Long generationId;
    
    /**
     * 简报开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    /**
     * 简报结束时间
     */
    @Column(name = "end_time", nullable = false)
    private LocalDateTime endTime;
    
    /**
     * 监测概述JSON
     */
    @Column(name = "overview_data", columnDefinition = "TEXT")
    private String overviewData;
    
    /**
     * 敏感信息趋势图JSON
     */
    @Column(name = "histogram_data", columnDefinition = "TEXT")
    private String histogramData;
    
    /**
     * 媒体来源分布JSON
     */
    @Column(name = "media_distribution_data", columnDefinition = "TEXT")
    private String mediaDistributionData;
    
    /**
     * 媒体参与详情JSON
     */
    @Column(name = "media_tier_data", columnDefinition = "TEXT")
    private String mediaTierData;
    
    /**
     * 高频词JSON
     */
    @Column(name = "high_frequency_word_data", columnDefinition = "TEXT")
    private String highFrequencyWordData;
    
    /**
     * 情感分布JSON
     */
    @Column(name = "emotion_distribution_data", columnDefinition = "TEXT")
    private String emotionDistributionData;
    
    /**
     * 敏感信息导读JSON
     */
    @Column(name = "sensitive_info_summary_data", columnDefinition = "TEXT")
    private String sensitiveInfoSummaryData;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}