package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * User Login Record Entity
 * Stores enterprise-level user login activities for tracking last login time
 * and login frequency
 * Supports 30-day data retention with cleanup policies
 */
@Entity
@Data
@Table(name = "user_login_records", indexes = {
        @Index(name = "idx_user_id", columnList = "user_id"),
        @Index(name = "idx_enterprise_id", columnList = "enterprise_id"),
        @Index(name = "idx_enterprise_code", columnList = "enterprise_code"),
        @Index(name = "idx_login_time", columnList = "login_time"),
        @Index(name = "idx_user_login_time", columnList = "user_id, login_time"),
        @Index(name = "idx_enterprise_login_time", columnList = "enterprise_id, login_time"),
        @Index(name = "idx_enterprise_code_time", columnList = "enterprise_code, login_time"),
        @Index(name = "idx_created_at", columnList = "createdAt")
})
public class UserLoginRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * User ID from OnePass authentication
     */
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * User display name (redundant storage for query convenience)
     */
    @Column(name = "user_name", length = 255)
    private String userName;

    /**
     * Enterprise ID for enterprise-level statistics
     */
    @Column(name = "enterprise_id", length = 64)
    private String enterpriseId;

    /**
     * Enterprise code for enterprise identification
     */
    @Column(name = "enterprise_code", length = 64)
    private String enterpriseCode;

    /**
     * Login timestamp
     */
    @Column(name = "login_time", nullable = false)
    private LocalDateTime loginTime;

    /**
     * Record creation timestamp
     */
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
}
