package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 新浪舆情通令牌缓存实体
 * 用于持久化存储访问令牌，避免服务重启导致的令牌丢失
 * 支持加密存储和获取次数限制
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "sina_token_cache", indexes = {
        @Index(name = "idx_app_id", columnList = "app_id"),
        @Index(name = "idx_expire_time", columnList = "expire_time"),
        @Index(name = "idx_created_date", columnList = "created_date"),
        @Index(name = "idx_is_active", columnList = "is_active")
})
public class SinaTokenCache {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 应用ID
     */
    @Column(name = "app_id", nullable = false, length = 64)
    private String appId;

    /**
     * 加密后的访问令牌
     */
    @Column(name = "encrypted_access_token", nullable = false, columnDefinition = "TEXT")
    private String encryptedAccessToken;

    /**
     * 加密后的刷新令牌
     */
    @Column(name = "encrypted_refresh_token", columnDefinition = "TEXT")
    private String encryptedRefreshToken;

    /**
     * 令牌过期时间
     */
    @Column(name = "expire_time", nullable = false)
    private LocalDateTime expireTime;

    /**
     * 令牌创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    /**
     * 令牌创建日期（用于统计当天获取次数）
     */
    @Column(name = "created_date", nullable = false)
    private LocalDateTime createdDate;

    /**
     * 是否为活跃令牌
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    /**
     * 当天获取令牌次数
     */
    @Column(name = "daily_fetch_count", nullable = false)
    private Integer dailyFetchCount;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 备注信息
     */
    @Column(name = "remarks", length = 500)
    private String remarks;

    /**
     * 创建时间戳
     */
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间戳
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (createdDate == null) {
            createdDate = LocalDateTime.now().toLocalDate().atStartOfDay();
        }
        if (isActive == null) {
            isActive = true;
        }
        if (dailyFetchCount == null) {
            dailyFetchCount = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
