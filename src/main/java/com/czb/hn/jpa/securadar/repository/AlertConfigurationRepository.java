package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for AlertConfiguration entity
 * Provides data access methods for alert configuration management
 */
@Repository
public interface AlertConfigurationRepository extends JpaRepository<AlertConfiguration, Long> {

    /**
     * Find all active alert configurations
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.isActive = true AND ac.enabled = true")
    List<AlertConfiguration> findAllActiveConfigurations();

    /**
     * Find alert configurations by enterprise ID
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.enterpriseId = :enterpriseId AND ac.isActive = true")
    List<AlertConfiguration> findByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    /**
     * Find alert configurations by plan ID
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.planId = :planId AND ac.isActive = true")
    List<AlertConfiguration> findByPlanId(@Param("planId") Long planId);

    /**
     * Find enabled alert configurations by enterprise ID
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.enterpriseId = :enterpriseId AND ac.enabled = true AND ac.isActive = true")
    List<AlertConfiguration> findEnabledByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    /**
     * Find enabled alert configurations by plan ID
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.planId = :planId AND ac.enabled = true AND ac.isActive = true")
    List<AlertConfiguration> findEnabledByPlanId(@Param("planId") Long planId);

    /**
     * Find alert configurations by name pattern
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.name LIKE %:namePattern% AND ac.isActive = true")
    Page<AlertConfiguration> findByNameContaining(@Param("namePattern") String namePattern, Pageable pageable);

    /**
     * Find alert configurations created within date range
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.createdAt BETWEEN :startDate AND :endDate AND ac.isActive = true")
    Page<AlertConfiguration> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * Find alert configurations updated within date range
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.updatedAt BETWEEN :startDate AND :endDate AND ac.isActive = true")
    Page<AlertConfiguration> findByUpdatedAtBetween(@Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * Count active configurations by enterprise ID
     */
    @Query("SELECT COUNT(ac) FROM AlertConfiguration ac WHERE ac.enterpriseId = :enterpriseId AND ac.isActive = true")
    Long countByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    /**
     * Count enabled configurations by enterprise ID
     */
    @Query("SELECT COUNT(ac) FROM AlertConfiguration ac WHERE ac.enterpriseId = :enterpriseId AND ac.enabled = true AND ac.isActive = true")
    Long countEnabledByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    /**
     * Find configurations that need snapshot update (modified but no recent
     * snapshot)
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.updatedAt > ac.lastSnapshotAt OR ac.lastSnapshotAt IS NULL")
    List<AlertConfiguration> findConfigurationsNeedingSnapshot();

    /**
     * Check if configuration name exists for enterprise (excluding specific ID)
     */
    @Query("SELECT COUNT(ac) > 0 FROM AlertConfiguration ac WHERE ac.name = :name AND ac.enterpriseId = :enterpriseId AND ac.id != :excludeId AND ac.isActive = true")
    boolean existsByNameAndEnterpriseIdExcludingId(@Param("name") String name,
            @Param("enterpriseId") String enterpriseId,
            @Param("excludeId") Long excludeId);

    /**
     * Check if configuration name exists for enterprise
     */
    @Query("SELECT COUNT(ac) > 0 FROM AlertConfiguration ac WHERE ac.name = :name AND ac.enterpriseId = :enterpriseId AND ac.isActive = true")
    boolean existsByNameAndEnterpriseId(@Param("name") String name, @Param("enterpriseId") String enterpriseId);

    /**
     * Find active configuration by ID
     */
    @Query("SELECT ac FROM AlertConfiguration ac WHERE ac.id = :id AND ac.isActive = true")
    Optional<AlertConfiguration> findActiveById(@Param("id") Long id);

    /**
     * Soft delete configuration by setting isActive to false
     */
    @Modifying(clearAutomatically = true, flushAutomatically = true)
    @Query("UPDATE AlertConfiguration ac SET ac.isActive = false, ac.updatedAt = CURRENT_TIMESTAMP, ac.updatedBy = :updatedBy WHERE ac.id = :id")
    void softDeleteById(@Param("id") Long id, @Param("updatedBy") String updatedBy);
}
