package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.JobEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务仓库接口
 */
@Repository
public interface JobRepository extends JpaRepository<JobEntity, Long> {
    
    /**
     * 根据任务名称查询任务
     *
     * @param name 任务名称
     * @return 任务实体
     */
    JobEntity findByName(String name);
    
    /**
     * 根据方案ID查询任务列表
     *
     * @param planId 方案ID
     * @return 任务列表
     */
    List<JobEntity> findByPlanId(Long planId);

    List<JobEntity> findByEnabledTrue();

    JobEntity findByNameAndPlanId(String name, Long planId);
} 