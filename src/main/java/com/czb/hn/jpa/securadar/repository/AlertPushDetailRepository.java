package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AlertPushDetail entity
 * Provides data access methods for alert push detail management
 * with tenant isolation and performance optimization
 */
@Repository
public interface AlertPushDetailRepository extends JpaRepository<AlertPushDetail, Long> {

        /**
         * Find all push details for a specific plan
         */
        List<AlertPushDetail> findByPlanIdOrderByPushTimeDesc(Long planId);

        /**
         * Find push details by plan ID and push status
         */
        List<AlertPushDetail> findByPlanIdAndPushStatusOrderByPushTimeDesc(Long planId, PushStatus pushStatus);

        /**
         * Find push details by enterprise ID with pagination
         */
        Page<AlertPushDetail> findByEnterpriseIdOrderByPushTimeDesc(String enterpriseId, Pageable pageable);

        /**
         * Find push details by enterprise ID and push type
         */
        Page<AlertPushDetail> findByEnterpriseIdAndPushTypeOrderByPushTimeDesc(
                        String enterpriseId, PushType pushType, Pageable pageable);

        /**
         * Find push details by enterprise ID and push status
         */
        Page<AlertPushDetail> findByEnterpriseIdAndPushStatusOrderByPushTimeDesc(
                        String enterpriseId, PushStatus pushStatus, Pageable pageable);

        /**
         * Find push details by enterprise ID within time range
         */
        Page<AlertPushDetail> findByEnterpriseIdAndPushTimeBetweenOrderByPushTimeDesc(
                        String enterpriseId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * Find failed push details that can be retried
         */
        @Query("SELECT apd FROM AlertPushDetail apd WHERE apd.pushStatus = :status AND apd.retryCount < 3")
        List<AlertPushDetail> findRetryablePushDetails(@Param("status") PushStatus status);

        /**
         * Find push details by account info and push type
         */
        List<AlertPushDetail> findByAccountInfoAndPushTypeOrderByPushTimeDesc(String accountInfo, PushType pushType);

        /**
         * Count push details by plan ID
         */
        long countByPlanId(Long planId);

        /**
         * Count successful push details by plan ID
         */
        long countByPlanIdAndPushStatus(Long planId, PushStatus pushStatus);

        /**
         * Find push details with complex filtering
         */
        @Query("SELECT apd FROM AlertPushDetail apd WHERE " +
                        "(:enterpriseId IS NULL OR apd.enterpriseId = :enterpriseId) AND " +
                        "(:planId IS NULL OR apd.planId = :planId) AND " +
                        "(:pushType IS NULL OR apd.pushType = :pushType) AND " +
                        "(:pushStatus IS NULL OR apd.pushStatus = :pushStatus) AND " +
                        "(:startTime IS NULL OR apd.pushTime >= :startTime) AND " +
                        "(:endTime IS NULL OR apd.pushTime <= :endTime) " +
                        "ORDER BY apd.pushTime DESC")
        Page<AlertPushDetail> findWithFilters(
                        @Param("enterpriseId") String enterpriseId,
                        @Param("planId") Long planId,
                        @Param("pushType") PushType pushType,
                        @Param("pushStatus") PushStatus pushStatus,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * Check if push detail exists for plan and push type
         */
        boolean existsByPlanIdAndPushType(Long planId, PushType pushType);

        /**
         * Delete push details older than specified date
         */
        @Query("DELETE FROM AlertPushDetail apd WHERE apd.pushTime < :cutoffDate")
        void deleteOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

        /**
         * Find latest push detail for each push type by plan ID
         */
        @Query("SELECT apd FROM AlertPushDetail apd WHERE apd.planId = :planId AND " +
                        "apd.pushTime = (SELECT MAX(apd2.pushTime) FROM AlertPushDetail apd2 " +
                        "WHERE apd2.planId = :planId AND apd2.pushType = apd.pushType)")
        List<AlertPushDetail> findLatestPushDetailsByPlanId(@Param("planId") Long planId);
}
