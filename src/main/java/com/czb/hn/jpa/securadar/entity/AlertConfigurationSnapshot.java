package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Alert Configuration Snapshot Entity
 * Stores versioned snapshots of alert configurations for audit and rollback purposes
 * Every create/update operation on AlertConfiguration generates a new snapshot
 */
@Entity
@Data
@Table(name = "alert_configuration_snapshots", indexes = {
    @Index(name = "idx_config_id", columnList = "configurationId"),
    @Index(name = "idx_version", columnList = "configurationId, versionNumber"),
    @Index(name = "idx_active", columnList = "configurationId, isActive"),
    @Index(name = "idx_created_at", columnList = "createdAt")
})
public class AlertConfigurationSnapshot {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "configuration_id", nullable = false)
    private Long configurationId;

    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;

    /**
     * Complete snapshot data stored as JSON
     * Contains the full AlertConfiguration state at the time of snapshot creation
     */
    @Column(name = "snapshot_data", columnDefinition = "JSON", nullable = false)
    private String snapshotData;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", length = 255)
    private String createdBy;

    @Column(name = "change_reason", length = 500)
    private String changeReason;

    /**
     * Indicates if this is the currently active snapshot
     * Only one snapshot per configuration should be active at a time
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * Operation type that triggered this snapshot
     */
    @Column(name = "operation_type", length = 50)
    private String operationType; // CREATE, UPDATE, ROLLBACK

    /**
     * Size of the snapshot data in bytes for monitoring purposes
     */
    @Column(name = "data_size")
    private Long dataSize;

    /**
     * Checksum for data integrity verification
     */
    @Column(name = "checksum", length = 64)
    private String checksum;

    /**
     * Foreign key relationship to AlertConfiguration
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "configuration_id", insertable = false, updatable = false)
    private AlertConfiguration alertConfiguration;
}
