package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.Plan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PlanRepository extends JpaRepository<Plan, Long> {

    @Override
    Optional<Plan> findById(Long id);

    @Override
    List<Plan> findAll();

    @Query("SELECT p FROM Plan p WHERE p.enterpriseId = :enterpriseId")
    List<Plan> findByVisibleEnterpriseId(@Param("enterpriseId") String enterpriseId);
} 