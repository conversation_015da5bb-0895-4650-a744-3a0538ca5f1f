package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.UserLoginRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * User Login Record Repository
 * Provides data access methods for enterprise-level user login tracking and
 * statistics
 */
@Repository
public interface UserLoginRecordRepository extends JpaRepository<UserLoginRecord, Long> {

    /**
     * Find the most recent login record for a specific user
     * 
     * @param userId User ID
     * @return Most recent login record
     */
    Optional<UserLoginRecord> findTopByUserIdOrderByLoginTimeDesc(String userId);

    /**
     * Count login records for a user within a specific time period
     * 
     * @param userId User ID
     * @param after  Start time (inclusive)
     * @return Number of login records
     */
    long countByUserIdAndLoginTimeAfter(String userId, LocalDateTime after);

    /**
     * Count login records for a user within a specific time range
     * 
     * @param userId User ID
     * @param after  Start time (inclusive)
     * @param before End time (exclusive)
     * @return Number of login records
     */
    long countByUserIdAndLoginTimeBetween(String userId, LocalDateTime after, LocalDateTime before);

    /**
     * Delete login records older than specified time, but preserve the latest
     * record for each user
     * This method implements the cleanup policy: keep 30 days of data but always
     * keep at least one record per user
     * 
     * @param cutoffTime Records older than this time will be deleted
     * @return Number of deleted records
     */
    @Modifying
    @Query(value = """
            DELETE FROM user_login_records
            WHERE login_time < :cutoffTime
            AND id NOT IN (
                SELECT max_id FROM (
                    SELECT MAX(id) as max_id
                    FROM user_login_records
                    GROUP BY user_id
                ) as latest_records
            )
            """, nativeQuery = true)
    int deleteOldRecordsButKeepLatest(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find all login records for a user within a specific time range
     * 
     * @param userId User ID
     * @param after  Start time (inclusive)
     * @param before End time (exclusive)
     * @return List of login records
     */
    @Query("SELECT r FROM UserLoginRecord r WHERE r.userId = :userId AND r.loginTime >= :after AND r.loginTime < :before ORDER BY r.loginTime DESC")
    java.util.List<UserLoginRecord> findByUserIdAndLoginTimeBetween(@Param("userId") String userId,
            @Param("after") LocalDateTime after,
            @Param("before") LocalDateTime before);

    /**
     * Count total login records older than specified time
     * Used for monitoring cleanup operations
     * 
     * @param cutoffTime Cutoff time
     * @return Number of old records
     */
    long countByLoginTimeBefore(LocalDateTime cutoffTime);
}
