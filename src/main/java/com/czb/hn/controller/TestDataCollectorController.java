package com.czb.hn.controller;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.sina.data.SinaDataResponseDto;
import com.czb.hn.service.collector.SinaNewsCollectorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测试数据采集控制器
 * 仅在本地开发环境或测试环境中启用
 */
@RestController
@RequestMapping("/test/collector")
@Tag(name = "TestDataCollectorController", description = "用于测试数据采集功能的接口")
@ConditionalOnProperty(name = "sina.api.use.local.file", havingValue = "true")
public class TestDataCollectorController {

    private static final Logger logger = LoggerFactory.getLogger(TestDataCollectorController.class);

    @Autowired
    private SinaNewsCollectorService sinaNewsCollectorService;

    @GetMapping("/fetch")
    @Operation(summary = "测试数据获取", description = "从本地文件或远程API获取测试数据")
    public ApiResponse<List<SinaDataResponseDto>> testFetchData(
            @Parameter(description = "票据ID", example = "test-ticket")
            @RequestParam(defaultValue = "test-ticket") String ticket,
            @Parameter(description = "偏移量", example = "1")
            @RequestParam(defaultValue = "1") Long offset,
            @Parameter(description = "限制数量", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {

        try {
            logger.info("Testing data fetch with ticket: {}, offset: {}, limit: {}", ticket, offset, limit);

            List<SinaDataResponseDto> data = sinaNewsCollectorService.fetchDataByTicket(ticket, offset, limit);

            logger.info("Successfully fetched {} records", data.size());

            return ApiResponse.success(data);

        } catch (Exception e) {
            logger.error("Error testing data fetch: {}", e.getMessage(), e);
            return ApiResponse.error("数据获取测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/save-to-ods")
    @Operation(summary = "测试保存到ODS", description = "获取数据并保存到ODS层进行测试")
    public ApiResponse<String> testSaveToOds(
            @Parameter(description = "票据ID", example = "test-ticket")
            @RequestParam(defaultValue = "test-ticket") String ticket,
            @Parameter(description = "偏移量", example = "1")
            @RequestParam(defaultValue = "1") Long offset,
            @Parameter(description = "限制数量", example = "3")
            @RequestParam(defaultValue = "3") Integer limit) {

        try {
            logger.info("Testing save to ODS with ticket: {}, offset: {}, limit: {}", ticket, offset, limit);

            // 获取数据
            List<SinaDataResponseDto> data = sinaNewsCollectorService.fetchDataByTicket(ticket, offset, limit);
            
            if (data.isEmpty()) {
                return ApiResponse.success("没有获取到数据，无需保存");
            }

            // 保存到ODS
            int savedCount = sinaNewsCollectorService.saveToOds(data);

            String message = String.format("成功获取 %d 条数据，保存 %d 条到ODS", data.size(), savedCount);
            logger.info(message);

            return ApiResponse.success(message);

        } catch (Exception e) {
            logger.error("Error testing save to ODS: {}", e.getMessage(), e);
            return ApiResponse.error("保存到ODS测试失败: " + e.getMessage());
        }
    }

    @GetMapping("/latest-offset")
    @Operation(summary = "获取最新偏移量", description = "获取当前ODS中的最新偏移量")
    public ApiResponse<Long> getLatestOffset() {
        try {
            Long latestOffset = sinaNewsCollectorService.getLatestOffset();
            logger.info("Latest offset: {}", latestOffset);
            return ApiResponse.success(latestOffset);
        } catch (Exception e) {
            logger.error("Error getting latest offset: {}", e.getMessage(), e);
            return ApiResponse.error("获取最新偏移量失败: " + e.getMessage());
        }
    }
}
