package com.czb.hn.document;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.InnerField;
import org.springframework.data.elasticsearch.annotations.MultiField;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 新浪舆情Elasticsearch文档实体
 * 用于存储DWD层数据到Elasticsearch中
 */
@Data
@Getter
@Setter
@Document(indexName = "sina_news")
@Setting(shards = 3, replicas = 1)
public class SinaNewsDocument {

    @Id
    private String contentId; // 内容ID作为文档ID

    @Field(type = FieldType.Keyword)
    private String textId;

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String content;

    @MultiField(
            mainField = @Field(type = FieldType.Text, analyzer = "ik_smart", searchAnalyzer = "ik_smart"),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword, ignoreAbove = 256)
    )
    private String summary;

    @Field(type = FieldType.Keyword)
    private String url;

    @Field(type = FieldType.Keyword)
    private String source;

    @Field(type = FieldType.Keyword)
    private String sourceWebsite;

    @Field(type = FieldType.Keyword)
    private String captureWebsite;

    @Field(type = FieldType.Keyword)
    private String mediaType; // 来源类型

    @Field(type = FieldType.Keyword)
    private String mediaTypeSecond; // 二级来源类型

    @Field(type = FieldType.Keyword)
    private String mediaLevel; // 媒体级别 信源级别

    @Field(type = FieldType.Keyword)
    private String newsColumn; // 避免使用column关键字

    @Field(type = FieldType.Date, format = {}, pattern = "uuuu-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime publishTime; // 监测时间

    @Field(type = FieldType.Date, format = {}, pattern = "uuuu-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime captureTime;

    @Field(type = FieldType.Date, format = {}, pattern = "uuuu-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime processTime;
    // 作者信息
    @Field(type = FieldType.Keyword)
    private String author;

    @Field(type = FieldType.Keyword)
    private String authorId;

    @Field(type = FieldType.Keyword)
    private String authorGender;

    @Field(type = FieldType.Keyword)
    private String authorProfileUrl;

    @Field(type = FieldType.Keyword)
    private String authorHomePage;

    @Field(type = FieldType.Keyword)
    private String authorProvince;

    @Field(type = FieldType.Keyword)
    private String authorCity;

    @Field(type = FieldType.Boolean)
    private Boolean authorVerified;

    @Field(type = FieldType.Keyword)
    private String authorVerifiedType; // 认证类型

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String authorTags;

    @Field(type = FieldType.Long)
    private Long authorContentCount;

    @Field(type = FieldType.Long)
    private Long authorFollowingCount;

    @Field(type = FieldType.Long)
    private Long authorFollowersCount; // 关注数 // 粉丝数

    // 地理位置信息
    @Field(type = FieldType.Keyword)
    private String publishProvince;

    @Field(type = FieldType.Keyword)
    private String publishCity;

    @Field(type = FieldType.Keyword)
    private String contentProvince;

    @Field(type = FieldType.Keyword)
    private String contentCity;

    @Field(type = FieldType.Keyword)
    private String location;

    @Field(type = FieldType.Keyword)
    private List<String> secondTrades; // 行业信息

    // 情感分析
    @Field(type = FieldType.Keyword)
    private String emotion; // 群体情绪

    @Field(type = FieldType.Float)
    private Double sentimentScore;

    @Field(type = FieldType.Integer)
    private Integer sensitivityType; // 信息属性（1：敏感 2：非敏感 3:中性）

    @Field(type = FieldType.Keyword)
    private String sensitivityScore;

    @Field(type = FieldType.Integer)
    private Integer contentTypes; // 微博类型 内容包含

    // 互动数据
    @Field(type = FieldType.Long)
    private Long commentCount; // 评论数

    @Field(type = FieldType.Long)
    private Long forwardCount; // 转发数

    @Field(type = FieldType.Long)
    private Long praiseCount; // 点赞数

    @Field(type = FieldType.Long)
    private Long shareCount; // 分享数

    @Field(type = FieldType.Long)
    private Long collectionCount;// 收藏数

    @Field(type = FieldType.Long)
    private Long answerCount; // 回复数

    @Field(type = FieldType.Long)
    private Long lookingCount; // 阅读数

    @Field(type = FieldType.Long)
    private Long interactionCount;

    // 相似性信息
    @Field(type = FieldType.Long)
    private Long similarityNum; // 相似文章数

    @Field(type = FieldType.Keyword)
    private String similarityTag; // 相似性标签

    // 转发信息
    @Field(type = FieldType.Integer)
    private Integer isOriginal; // 内容类型（1:原创 2:转发）

    @Field(type = FieldType.Boolean)
    private Boolean isForward;

    @Field(type = FieldType.Keyword)
    private String wbForwardType;

    @Field(type = FieldType.Keyword)
    private String rootContentId;

    // 匹配信息
    @Field(type = FieldType.Integer)
    private Integer matchType;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String matchInfo;

    @Field(type = FieldType.Keyword)
    private String matchTicket;

    @Field(type = FieldType.Keyword)
    private String matchName;

    @Field(type = FieldType.Integer)
    private Integer resultView; // 结果呈现（1:正常，2:噪音）

    // 媒体信息
    @Field(type = FieldType.Text)
    private String images;

    @Field(type = FieldType.Text)
    private String videoUrl;

    @Field(type = FieldType.Keyword)
    private String videoCoverUrl;

    // OCR识别内容
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String ocrContent;

    // 视频内容（转录文本）
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String videoContent;

    // 音频内容（转录文本）
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String audioContent;

    // 原始数据关联
    @Field(type = FieldType.Long)
    private Long odsId;

    @Field(type = FieldType.Long)
    private Long dwdId;
}