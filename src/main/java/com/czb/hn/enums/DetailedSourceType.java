package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Detailed Source Type Enumeration
 * 详细来源类型枚举 - String存储
 *
 * API字段: contentExt.originTypeSecond
 * 存储方式: String存储 (API代码)
 * API值: xw, zw, wbxw, wxzw, app, zmt, 等详细分类代码
 */
public enum DetailedSourceType implements StandardEnum<String> {
    // Website Sources (网站)
    NEWS_WEBSITES("xw", "新闻网站"),
    GOVERNMENT_WEBSITES("zw", "政务网站"),
    INDUSTRY_WEBSITES("wzhangye", "行业网站"),
    INSTITUTIONAL_ORGANIZATIONS("wzorg", "机构组织"),
    CORPORATE_WEBSITES("wzqy", "企业官网"),
    FOREIGN_WEB_MEDIA("waimei", "境外网媒"),
    OTHER_WEBSITES("wzqita", "其他网站"),

    // Weibo Sources (微博)
    WEIBO_MEDIA_ACCOUNTS("wbxw", "微博媒体号"),
    WEIBO_GOVERNMENT_ACCOUNTS("wbzw", "微博政务号"),
    WEIBO_CAMPUS_ACCOUNTS("wbschool", "微博校园号"),
    WEIBO_GROUP_ACCOUNTS("wbteam", "微博团体号"),
    WEIBO_CORPORATE_ACCOUNTS("wbcompany", "微博企业号"),
    WEIBO_WEBSITE_ACCOUNTS("wbweb", "微博网站号"),
    WEIBO_GOLD_V_ACCOUNTS("wbgoldv", "金V号"),
    WEIBO_ORANGE_V_ACCOUNTS("wborangev", "橙V号"),
    WEIBO_PERSONAL_ACCOUNTS("wbpersonal", "微博个人号"),

    // WeChat Sources (微信)
    WECHAT_MEDIA_ACCOUNTS("wxxw", "微信媒体号"),
    WECHAT_GOVERNMENT_ACCOUNTS("wxzw", "微信政务号"),
    WECHAT_INSTITUTIONAL_ACCOUNTS("wxorg", "微信机构号"),
    WECHAT_CORPORATE_ACCOUNTS("wxcompany", "微信企业号"),
    WECHAT_PERSONAL_ACCOUNTS("wxpersonal", "微信个人号"),

    // Interactive Forum Sources (互动论坛)
    MESSAGE_BOARDS("liuyanban", "留言版"),
    COMPLAINTS_REPORTS("tousu", "投诉爆料"),
    FORUMS("lt", "论坛"),
    QA_PLATFORMS("wenda", "问答"),
    TIEBA_FORUMS("tieba", "贴吧"),
    BLOGS("blog", "博客"),

    // Mobile App Sources (客户端)
    MOBILE_APPLICATIONS("app", "APP"),
    SELF_MEDIA_PLATFORMS("zmt", "自媒体"),

    // Digital Newspaper Sources (数字报)
    NATIONAL_DIGITAL_NEWSPAPERS("szbyj", "央级数字报"),
    PROVINCIAL_DIGITAL_NEWSPAPERS("szbsj", "省级数字报"),
    MUNICIPAL_DIGITAL_NEWSPAPERS("szbdj", "地市数字报"),
    OTHER_DIGITAL_NEWSPAPERS("szbqita", "其他数字报"),

    // Video Sources (视频)
    SHORT_VIDEOS("xsp", "短视频"),
    VIDEO_WEBSITES("spwz", "视频网站"),
    TV_VIDEOS("diantai", "电视视频");

    private final String value;
    private final String description;

    DetailedSourceType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching Sina API format)
     * 
     * @param value String value from API
     * @return Corresponding enum value
     */
    public static DetailedSourceType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return OTHER_WEBSITES; // Default fallback
        }

        String trimmedValue = value.trim();
        for (DetailedSourceType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return OTHER_WEBSITES; // Default fallback
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static DetailedSourceType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return OTHER_WEBSITES;
        }

        String trimmedValue = value.trim();
        for (DetailedSourceType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return OTHER_WEBSITES; // Default fallback
    }

    /**
     * Check if this is a government/official source
     * 
     * @return true if government source, false otherwise
     */
    public boolean isGovernmentSource() {
        return this == GOVERNMENT_WEBSITES || this == WEIBO_GOVERNMENT_ACCOUNTS ||
                this == WECHAT_GOVERNMENT_ACCOUNTS || this == NATIONAL_DIGITAL_NEWSPAPERS ||
                this == PROVINCIAL_DIGITAL_NEWSPAPERS || this == MUNICIPAL_DIGITAL_NEWSPAPERS;
    }

    /**
     * Check if this is a media organization source
     * 
     * @return true if media source, false otherwise
     */
    public boolean isMediaSource() {
        return this == NEWS_WEBSITES || this == WEIBO_MEDIA_ACCOUNTS ||
                this == WECHAT_MEDIA_ACCOUNTS || this == NATIONAL_DIGITAL_NEWSPAPERS ||
                this == PROVINCIAL_DIGITAL_NEWSPAPERS || this == MUNICIPAL_DIGITAL_NEWSPAPERS ||
                this == OTHER_DIGITAL_NEWSPAPERS;
    }

    /**
     * Check if this is a personal/individual source
     * 
     * @return true if personal source, false otherwise
     */
    public boolean isPersonalSource() {
        return this == WEIBO_PERSONAL_ACCOUNTS || this == WECHAT_PERSONAL_ACCOUNTS ||
                this == BLOGS || this == SELF_MEDIA_PLATFORMS;
    }

    @Override
    public String toString() {
        return description;
    }
}
