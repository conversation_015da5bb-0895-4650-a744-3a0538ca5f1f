package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Subscription Status Enumeration
 * 订阅状态枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: ACTIVE=激活, EXPIRED=过期, SUSPENDED=暂停, CANCELLED=取消
 */
public enum SubscriptionStatus implements StandardEnum<String> {
    ACTIVE("ACTIVE", "激活"),
    EXPIRED("EXPIRED", "过期"),
    SUSPENDED("SUSPENDED", "暂停"),
    CANCELLED("CANCELLED", "取消");

    private final String value;
    private final String description;

    SubscriptionStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Check if subscription is active
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * Check if subscription is expired
     */
    public boolean isExpired() {
        return this == EXPIRED;
    }

    /**
     * Convert from string value using smart conversion
     * 
     * @param value String value to convert
     * @return Corresponding enum value, or EXPIRED as default
     */
    public static SubscriptionStatus fromString(String value) {
        return StandardEnum.smartConvertWithDefault(SubscriptionStatus.class, value, EXPIRED);
    }

    @Override
    public String toString() {
        return description;
    }
}
