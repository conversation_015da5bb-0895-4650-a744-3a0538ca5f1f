package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Bulletin Status Enumeration
 * 简报状态枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: GENERATED=已生成, SENT=已发送, FAILED=发送失败
 */
public enum BulletinStatus implements StandardEnum<String> {
    GENERATED("GENERATED", "已生成"),
    SENT("SENT", "已发送"),
    FAILED("FAILED", "发送失败");

    private final String value;
    private final String description;

    BulletinStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}