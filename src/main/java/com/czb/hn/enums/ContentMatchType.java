package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

public enum ContentMatchType implements StandardEnum<String> {
    TITLE("1", "标题"),
    MAIN_TXT("2", "正文");

    private final String value;
    private final String description;

    ContentMatchType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching Sina API format)
     * 
     * @param value String value from API (1 or 2)
     * @return Corresponding enum value
     */
    public static ContentMatchType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return TITLE; // Default to keyword match
        }

        String trimmedValue = value.trim();
        for (ContentMatchType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return TITLE; // Default fallback
    }

    /**
     * Convert from integer value (for API compatibility)
     * 
     * @param value Integer value (1 or 2)
     * @return Corresponding enum value
     */
    public static ContentMatchType fromInteger(Integer value) {
        if (value == null) {
            return TITLE;
        }

        return switch (value) {
            case 1 -> TITLE;
            case 2 -> MAIN_TXT;
            default -> TITLE    ; // Default fallback
        };
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static ContentMatchType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return TITLE;
        }

        String trimmedValue = value.trim();
        for (ContentMatchType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return TITLE; // Default fallback
    }


    @Override
    public String toString() {
        return description;
    }
}
