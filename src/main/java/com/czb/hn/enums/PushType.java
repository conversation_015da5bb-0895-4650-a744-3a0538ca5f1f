package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Push Type Enumeration
 * 推送类型枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: EMAIL=邮件, SMS=短信, SYSTEM=系统
 */
public enum PushType implements StandardEnum<String> {
    EMAIL("EMAIL", "邮件"),
    SMS("SMS", "短信");

    private final String value;
    private final String description;

    PushType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Get enum value from string using smart conversion
     * 
     * @param value String value to convert
     * @return Corresponding enum value, or SYSTEM as default
     */
    public static PushType fromString(String value) {
        return StandardEnum.smartConvertWithDefault(PushType.class, value, null);
    }
}
