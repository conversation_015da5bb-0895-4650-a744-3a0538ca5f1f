package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Push Status Enumeration
 * 推送状态枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: SUCCESS=成功, FAILURE=失败
 */
public enum PushStatus implements StandardEnum<String> {
    SUCCESS("SUCCESS", "成功"),
    FAILURE("FAILURE", "失败"),
    PENDING("PENDING", "推送中");

    private final String value;
    private final String description;

    PushStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Get enum value from string (case-insensitive)
     * 
     * @param value String value to convert
     * @return Corresponding enum value
     * @throws IllegalArgumentException if value is not valid
     */
    public static PushStatus fromString(String value) {
        return StandardEnum.smartConvertWithDefault(PushStatus.class, value, FAILURE);
    }

    /**
     * Check if the status represents a successful push
     * 
     * @return true if status is SUCCESS, false otherwise
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * Check if the status represents a failed push
     * 
     * @return true if status is FAILURE, false otherwise
     */
    public boolean isFailure() {
        return this == FAILURE;
    }
}
