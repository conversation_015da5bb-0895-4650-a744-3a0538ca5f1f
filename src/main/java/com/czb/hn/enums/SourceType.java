package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Source Type Enumeration
 * 来源类型枚举 - 字符存储
 *
 * API字段: contentExt.newOriginType
 * 存储方式: 字符存储 (API代码)
 * API值: hdlt, wb, wx, zmtapp, sp, szb, wz
 */
public enum SourceType implements StandardEnum<String> {
    INTERACTIVE_FORUMS("hdlt", "互动论坛"),
    WEIBO("wb", "微博"),
    WECHAT("wx", "微信"),
    MOBILE_APPS("zmtapp", "客户端"),
    VIDEO("sp", "视频"),
    DIGITAL_NEWSPAPERS("szb", "数字报"),
    WEBSITES("wz", "网站");

    private final String value;
    private final String description;

    SourceType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching Sina API format)
     * 
     * @param value String value from API
     * @return Corresponding enum value
     */
    public static SourceType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return WEBSITES; // Default to websites
        }

        String trimmedValue = value.trim();
        for (SourceType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return WEBSITES; // Default fallback
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static SourceType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return WEBSITES;
        }

        String trimmedValue = value.trim();
        for (SourceType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return WEBSITES; // Default fallback
    }

    /**
     * Check if this is a social media platform
     * 
     * @return true if Weibo or WeChat, false otherwise
     */
    public boolean isSocialMedia() {
        return this == WEIBO || this == WECHAT;
    }

    /**
     * Check if this is a traditional media source
     * 
     * @return true if websites or digital newspapers, false otherwise
     */
    public boolean isTraditionalMedia() {
        return this == WEBSITES || this == DIGITAL_NEWSPAPERS;
    }

    /**
     * Check if this is an interactive platform
     * 
     * @return true if forums or mobile apps, false otherwise
     */
    public boolean isInteractivePlatform() {
        return this == INTERACTIVE_FORUMS || this == MOBILE_APPS;
    }

    /**
     * Check if this source type supports multimedia content
     * 
     * @return true if supports images/videos, false otherwise
     */
    public boolean supportsMultimedia() {
        return this == WEIBO || this == WECHAT || this == VIDEO || this == MOBILE_APPS;
    }

    /**
     * Get credibility score for this source type
     * 
     * @return credibility score (0.0-1.0, higher = more credible)
     */
    public double getCredibilityScore() {
        return switch (this) {
            case DIGITAL_NEWSPAPERS -> 0.9; // Highest credibility
            case WEBSITES -> 0.8;
            case WEIBO, WECHAT -> 0.6; // Social media - moderate credibility
            case MOBILE_APPS -> 0.5;
            case INTERACTIVE_FORUMS -> 0.4;
            case VIDEO -> 0.3; // Lowest credibility - often user-generated
        };
    }

    /**
     * Get processing priority (higher = process first)
     * 
     * @return processing priority level
     */
    public int getProcessingPriority() {
        return switch (this) {
            case DIGITAL_NEWSPAPERS, WEBSITES -> 5; // Highest priority
            case WEIBO, WECHAT -> 4; // High priority - major social platforms
            case MOBILE_APPS -> 3;
            case INTERACTIVE_FORUMS -> 2;
            case VIDEO -> 1; // Lowest priority - complex processing
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
