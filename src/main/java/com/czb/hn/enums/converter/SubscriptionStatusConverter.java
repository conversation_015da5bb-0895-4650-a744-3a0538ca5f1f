package com.czb.hn.enums.converter;

import com.czb.hn.enums.SubscriptionStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for SubscriptionStatus
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "ACTIVE": ACTIVE (激活)
 * - "EXPIRED": EXPIRED (过期)
 * - "SUSPENDED": SUSPENDED (暂停)
 * - "CANCELLED": CANCELLED (取消)
 */
@Converter(autoApply = true)
public class SubscriptionStatusConverter implements AttributeConverter<SubscriptionStatus, String> {

    @Override
    public String convertToDatabaseColumn(SubscriptionStatus attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public SubscriptionStatus convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return SubscriptionStatus.fromString(dbData);
    }
}
