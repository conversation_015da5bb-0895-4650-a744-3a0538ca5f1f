package com.czb.hn.enums.converter;

import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.base.StandardEnum;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for ContentCategory
 * Converts between enum and integer value for database storage
 * 
 * 数据库存储映射：
 * - 1: ORIGINAL (原创)
 * - 2: FORWARD (转发)
 */
@Converter(autoApply = true)
public class ContentCategoryConverter implements AttributeConverter<ContentCategory, Integer> {

    @Override
    public Integer convertToDatabaseColumn(ContentCategory attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public ContentCategory convertToEntityAttribute(Integer dbData) {
        if (dbData == null) {
            return null;
        }
        return StandardEnum.smartConvertWithDefault(ContentCategory.class, dbData, ContentCategory.ORIGINAL);
    }
}
