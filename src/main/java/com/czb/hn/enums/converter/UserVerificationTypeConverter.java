package com.czb.hn.enums.converter;

import com.czb.hn.enums.UserVerificationType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for UserVerificationType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "-1": REGULAR (普通)
 * - "0": ORANGE_V (橙V)
 * - "1": BLUE_V (蓝V)
 * - "200": EXPERT (达人)
 * - "600": GOLD_V (金V)
 */
@Converter(autoApply = true)
public class UserVerificationTypeConverter implements AttributeConverter<UserVerificationType, String> {

    @Override
    public String convertToDatabaseColumn(UserVerificationType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public UserVerificationType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return UserVerificationType.fromString(dbData);
    }
}
