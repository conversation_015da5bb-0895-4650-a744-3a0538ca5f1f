package com.czb.hn.enums.converter;

import com.czb.hn.enums.ContentType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for ContentType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "1": TEXT (文本)
 * - "2": IMAGE (图片)
 * - "3": SHORT_LINK (短链)
 * - "4": VIDEO (视频)
 */
@Converter(autoApply = true)
public class ContentTypeConverter implements AttributeConverter<ContentType, String> {

    @Override
    public String convertToDatabaseColumn(ContentType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public ContentType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return ContentType.fromString(dbData);
    }
}
