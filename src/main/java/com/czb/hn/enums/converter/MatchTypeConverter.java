package com.czb.hn.enums.converter;

import com.czb.hn.enums.MatchType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for MatchType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "1": KEYWORD (关键词)
 * - "2": USER (用户)
 */
@Converter(autoApply = true)
public class MatchTypeConverter implements AttributeConverter<MatchType, String> {

    @Override
    public String convertToDatabaseColumn(MatchType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public MatchType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return MatchType.fromString(dbData);
    }
}
