package com.czb.hn.enums.converter;

import com.czb.hn.enums.EmotionType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for EmotionType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "中性": NEUTRAL
 * - "喜悦": JOY
 * - "悲伤": SADNESS
 * - "愤怒": ANGER
 * - "惊奇": SURPRISE
 * - "恐惧": FEAR
 */
@Converter(autoApply = true)
public class EmotionTypeConverter implements AttributeConverter<EmotionType, String> {

    @Override
    public String convertToDatabaseColumn(EmotionType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public EmotionType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return EmotionType.fromChineseValue(dbData);
    }
}
