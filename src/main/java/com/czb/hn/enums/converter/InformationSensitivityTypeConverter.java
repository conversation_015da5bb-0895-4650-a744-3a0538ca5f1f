package com.czb.hn.enums.converter;

import com.czb.hn.enums.InformationSensitivityType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for InformationSensitivityType
 * Converts between enum and integer value for database storage
 * 
 * 数据库存储映射：
 * - 0: ALL (全部)
 * - 1: SENSITIVE (敏感)
 * - 2: NON_SENSITIVE (非敏感)
 * - 3: NEUTRAL (中性)
 */
@Converter(autoApply = true)
public class InformationSensitivityTypeConverter implements AttributeConverter<InformationSensitivityType, Integer> {

    @Override
    public Integer convertToDatabaseColumn(InformationSensitivityType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public InformationSensitivityType convertToEntityAttribute(Integer dbData) {
        if (dbData == null) {
            return null;
        }
        return InformationSensitivityType.fromValue(dbData);
    }
}
