package com.czb.hn.enums.converter;

import com.czb.hn.enums.ResultViewType;
import com.czb.hn.enums.base.StandardEnum;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for ResultViewType
 * Converts between enum and integer value for database storage
 * 
 * 数据库存储映射：
 * - 1: NORMAL (正常)
 * - 2: NOISE (噪音)
 */
@Converter(autoApply = true)
public class ResultViewTypeConverter implements AttributeConverter<ResultViewType, Integer> {

    @Override
    public Integer convertToDatabaseColumn(ResultViewType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public ResultViewType convertToEntityAttribute(Integer dbData) {
        if (dbData == null) {
            return null;
        }
        return StandardEnum.smartConvertWithDefault(ResultViewType.class, dbData, ResultViewType.NORMAL);
    }
}
