package com.czb.hn.enums.converter;

import com.czb.hn.enums.DetailedSourceType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for DetailedSourceType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "xw": NEWS_WEBSITES (新闻网站)
 * - "zw": GOVERNMENT_WEBSITES (政务网站)
 * - "wbxw": WEIBO_MEDIA_ACCOUNTS (微博媒体号)
 * - "wxzw": WECHAT_GOVERNMENT_ACCOUNTS (微信政务号)
 * - 等等...
 */
@Converter(autoApply = true)
public class DetailedSourceTypeConverter implements AttributeConverter<DetailedSourceType, String> {

    @Override
    public String convertToDatabaseColumn(DetailedSourceType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public DetailedSourceType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return DetailedSourceType.fromString(dbData);
    }
}
