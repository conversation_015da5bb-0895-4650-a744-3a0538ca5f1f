package com.czb.hn.enums.converter;

import com.czb.hn.enums.SourceType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for SourceType
 * Converts between enum and string value for database storage
 * 
 * 数据库存储映射：
 * - "wz": WEBSITES (网站)
 * - "wb": WEIBO (微博)
 * - "wx": WECHAT (微信)
 * - "hdlt": INTERACTIVE_FORUMS (互动论坛)
 * - "app": MOBILE_APPS (客户端)
 * - "szb": DIGITAL_NEWSPAPERS (数字报)
 * - "sp": VIDEO (视频)
 */
@Converter(autoApply = true)
public class SourceTypeConverter implements AttributeConverter<SourceType, String> {

    @Override
    public String convertToDatabaseColumn(SourceType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public SourceType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return SourceType.fromString(dbData);
    }
}
