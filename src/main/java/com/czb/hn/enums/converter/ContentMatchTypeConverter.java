package com.czb.hn.enums.converter;

import com.czb.hn.enums.ContentMatchType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA Converter for MatchType
 * Converts between enum and string value for database storage
 *
 */
@Converter(autoApply = true)
public class ContentMatchTypeConverter implements AttributeConverter<ContentMatchType, String> {

    @Override
    public String convertToDatabaseColumn(ContentMatchType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public ContentMatchType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return ContentMatchType.fromString(dbData);
    }
}
