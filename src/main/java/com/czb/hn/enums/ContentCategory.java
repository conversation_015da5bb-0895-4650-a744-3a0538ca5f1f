package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Content Category Enumeration
 * 内容类别枚举 - Integer存储
 *
 * API字段: contentExt.isOriginal
 * 存储方式: Integer存储 (API值)
 * API值: 1=原创, 2=转发
 */
public enum ContentCategory implements StandardEnum<Integer> {
    ORIGINAL(1, "原创"),
    FORWARD(2, "转发");

    private final Integer value;
    private final String description;

    ContentCategory(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from legacy Boolean value to enum
     *
     * @param isOriginal Legacy Boolean value (true = original, false = forward)
     * @return Corresponding enum value
     */
    public static ContentCategory fromBoolean(Boolean isOriginal) {
        if (isOriginal == null) {
            return ORIGINAL; // 默认原创
        }
        return isOriginal ? ORIGINAL : FORWARD;
    }

    /**
     * Convert from string value (matching Elasticsearch document format)
     *
     * @param isOriginal String value ("1" = original, "2" = forward)
     * @return Corresponding enum value
     */
    public static ContentCategory fromString(String isOriginal) {
        return StandardEnum.smartConvertWithDefault(ContentCategory.class, isOriginal, ORIGINAL);
    }

    /**
     * Convert from integer value (matching Sina API format)
     *
     * @param value Integer value (1 = original, 2 = forward)
     * @return Corresponding enum value
     */
    public static ContentCategory fromInteger(Integer value) {
        return StandardEnum.smartConvertWithDefault(ContentCategory.class, value, ORIGINAL);
    }

    /**
     * Convert to Boolean for backward compatibility
     *
     * @return Boolean representation (true for ORIGINAL, false for FORWARD)
     */
    public Boolean toBoolean() {
        return switch (this) {
            case ORIGINAL -> true;
            case FORWARD -> false;
        };
    }

    /**
     * Get integer value for API compatibility
     *
     * @return Integer value (1 = original, 2 = forward)
     */
    public Integer toInteger() {
        return getValue(); // 直接返回value值
    }

    /**
     * Check if this is original content
     * 
     * @return true if original, false otherwise
     */
    public boolean isOriginal() {
        return this == ORIGINAL;
    }

    /**
     * Check if this is forwarded content
     * 
     * @return true if forwarded, false otherwise
     */
    public boolean isForward() {
        return this == FORWARD;
    }
}
