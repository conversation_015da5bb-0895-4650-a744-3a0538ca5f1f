package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * User Verification Type Enumeration
 * 用户认证类型枚举 - String存储
 *
 * API字段: userExt.verifiedType
 * 存储方式: String存储 (API值)
 * API值: "-1"=普通, "0"=橙V, "1-7"=蓝V, "200/220"=达人, "600"=金V
 */
public enum UserVerificationType implements StandardEnum<String> {
    REGULAR("-1", "普通"),
    ORANGE_V("0", "橙V"),
    BLUE_V("1", "蓝V"), // 代表所有蓝V类型(1-7)
    EXPERT("200", "达人"), // 代表所有达人类型(200,220)
    GOLD_V("600", "金V");

    private final String value;
    private final String description;

    UserVerificationType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from integer value (matching Sina API format)
     *
     * @param value Integer value from API
     * @return Corresponding enum value
     */
    public static UserVerificationType fromInteger(Integer value) {
        if (value == null) {
            return REGULAR;
        }

        // Handle merged enum values and convert to string for StandardEnum
        String stringValue = switch (value) {
            case -1 -> "-1";
            case 0 -> "0";
            case 1, 2, 3, 4, 5, 6, 7 -> "1"; // All Blue V types merged to "1"
            case 200, 220 -> "200"; // All Expert types merged to "200"
            case 600 -> "600";
            default -> "-1"; // Default to regular
        };

        return StandardEnum.smartConvertWithDefault(UserVerificationType.class, stringValue, REGULAR);
    }

    /**
     * Convert from string value (for API compatibility)
     * 
     * @param value String representation of the integer value
     * @return Corresponding enum value
     */
    public static UserVerificationType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return REGULAR;
        }

        try {
            int intValue = Integer.parseInt(value.trim());
            return fromInteger(intValue);
        } catch (NumberFormatException e) {
            return REGULAR;
        }
    }

    /**
     * Check if this is a verified account (any type of V)
     * 
     * @return true if verified, false otherwise
     */
    public boolean isVerified() {
        return this != REGULAR;
    }

    /**
     * Check if this is a Blue V account
     *
     * @return true if Blue V, false otherwise
     */
    public boolean isBlueV() {
        return this == BLUE_V;
    }

    /**
     * Check if this is an expert/influencer account
     *
     * @return true if expert, false otherwise
     */
    public boolean isExpert() {
        return this == EXPERT;
    }

    /**
     * Get verification level for sorting/comparison
     * 
     * @return verification level (higher = more prestigious)
     */
    public int getVerificationLevel() {
        return switch (this) {
            case GOLD_V -> 4;
            case ORANGE_V -> 3;
            case EXPERT -> 2;
            case BLUE_V -> 1;
            case REGULAR -> 0;
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
