package com.czb.hn.enums.base;

import java.util.Optional;

/**
 * 标准枚举接口
 *
 * 最精简设计：
 * 1. 所有枚举值都写到value中，不使用序号
 * 2. 所有业务逻辑和存储逻辑均使用value值
 * 3. 根据接口文档的实际值类型确定存储类型
 *
 * @param <T> 枚举值类型（根据API接口文档确定：Integer或String）
 */
public interface StandardEnum<T> {

    /**
     * 获取存储值
     * - 所有枚举值都写到value中，不使用序号
     * - 所有业务逻辑和存储逻辑均使用此值
     * - 直接对应API接口文档中的值
     *
     * @return 存储值
     */
    T getValue();

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    String getDescription();

    /**
     * 智能转换方法 - 核心转换逻辑
     * 
     * 支持的输入格式：
     * 1. 描述信息匹配
     * 2. getValue()值匹配
     * 3. 枚举名匹配
     * 
     * @param enumClass 目标枚举类
     * @param input     输入值
     * @param <E>       枚举类型
     * @return 转换后的枚举值
     */
    static <E extends Enum<E> & StandardEnum<?>> Optional<E> smartConvert(Class<E> enumClass, Object input) {
        if (input == null) {
            return Optional.empty();
        }

        E[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null || enumConstants.length == 0) {
            return Optional.empty();
        }

        // 1. 如果输入已经是目标枚举类型，直接返回
        if (enumClass.isInstance(input)) {
            return Optional.of(enumClass.cast(input));
        }

        String inputStr = input.toString().trim();
        if (inputStr.isEmpty()) {
            return Optional.empty();
        }

        // 2. 尝试按描述匹配
        for (E enumValue : enumConstants) {
            if (inputStr.equals(enumValue.getDescription())) {
                return Optional.of(enumValue);
            }
        }

        // 3. 尝试按getValue匹配
        for (E enumValue : enumConstants) {
            Object value = enumValue.getValue();
            if (value != null && inputStr.equals(value.toString())) {
                return Optional.of(enumValue);
            }
        }

        // 4. 尝试按数值匹配（序号类型枚举）
        if (input instanceof Integer) {
            Integer intValue = (Integer) input;
            for (E enumValue : enumConstants) {
                Object value = enumValue.getValue();
                if (value instanceof Integer && value.equals(intValue)) {
                    return Optional.of(enumValue);
                }
            }
        }

        // 5. 尝试数值字符串转换
        try {
            int intValue = Integer.parseInt(inputStr);
            for (E enumValue : enumConstants) {
                Object value = enumValue.getValue();
                if (value instanceof Integer && value.equals(intValue)) {
                    return Optional.of(enumValue);
                }
            }
        } catch (NumberFormatException ignored) {
            // 不是数值，继续其他匹配方式
        }

        // 6. 尝试按枚举名匹配（大小写不敏感）
        try {
            return Optional.of(Enum.valueOf(enumClass, inputStr.toUpperCase()));
        } catch (IllegalArgumentException ignored) {
            // 枚举名不匹配
        }

        return Optional.empty();
    }

    /**
     * 智能转换方法（带默认值）
     * 
     * @param enumClass    目标枚举类
     * @param input        输入值
     * @param defaultValue 默认值
     * @param <E>          枚举类型
     * @return 转换后的枚举值，如果无法转换则返回默认值
     */
    static <E extends Enum<E> & StandardEnum<?>> E smartConvertWithDefault(Class<E> enumClass, Object input,
            E defaultValue) {
        return smartConvert(enumClass, input).orElse(defaultValue);
    }
}
