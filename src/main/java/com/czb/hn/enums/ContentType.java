package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Content Type Enumeration
 * 内容类型枚举 - String存储
 *
 * API字段: contentExt.contentTypes
 * 存储方式: String存储 (API值)
 * API值: "1"=文本, "2"=图片, "3"=短链, "4"=视频
 */
public enum ContentType implements StandardEnum<String> {
    TEXT("1", "文本"),
    IMAGE("2", "图片"),
    SHORT_URL("3", "短链"),
    VIDEO("4", "视频");

    private final String value;
    private final String description;

    ContentType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching Sina API format)
     * 
     * @param value String value from API (1-4)
     * @return Corresponding enum value
     */
    public static ContentType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return TEXT; // Default to text
        }

        String trimmedValue = value.trim();
        for (ContentType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return TEXT; // Default fallback
    }

    /**
     * Convert from integer value (for API compatibility)
     * 
     * @param value Integer value (1-4)
     * @return Corresponding enum value
     */
    public static ContentType fromInteger(Integer value) {
        if (value == null) {
            return TEXT;
        }

        return switch (value) {
            case 1 -> TEXT;
            case 2 -> IMAGE;
            case 3 -> SHORT_URL;
            case 4 -> VIDEO;
            default -> TEXT; // Default fallback
        };
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static ContentType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return TEXT;
        }

        String trimmedValue = value.trim();
        for (ContentType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return TEXT; // Default fallback
    }

    /**
     * Check if this content type is multimedia
     * 
     * @return true if image or video, false otherwise
     */
    public boolean isMultimedia() {
        return this == IMAGE || this == VIDEO;
    }

    /**
     * Check if this content type is text-based
     * 
     * @return true if text or short URL, false otherwise
     */
    public boolean isTextBased() {
        return this == TEXT || this == SHORT_URL;
    }

    /**
     * Check if this content type requires special processing
     * 
     * @return true if image or video (may need OCR/analysis), false otherwise
     */
    public boolean requiresSpecialProcessing() {
        return this == IMAGE || this == VIDEO;
    }

    /**
     * Get processing priority (higher = process first)
     * 
     * @return processing priority level
     */
    public int getProcessingPriority() {
        return switch (this) {
            case TEXT -> 4; // Highest priority - easiest to process
            case SHORT_URL -> 3;
            case IMAGE -> 2; // May need OCR
            case VIDEO -> 1; // Lowest priority - most complex
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
