package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Media Level Enumeration
 * 媒体级别枚举 - String存储
 *
 * API字段: contentExt.originLevel
 * 存储方式: String存储 (中文值)
 * API值: 央级, 省级, 地市, 重点, 中小, 企业商业
 */
public enum MediaLevel implements StandardEnum<String> {
    NATIONAL("央级"),
    PROVINCIAL("省级"),
    MUNICIPAL("地市"),
    BUSINESS("商业"),
    SMALL_MEDIUM("中小"),
    CORPORATE("企业资讯");

    private final String value;

    MediaLevel(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return value;
    }

    /**
     * Convert from Chinese string value (matching Sina API format)
     * 
     * @param value Chinese media level string from API
     * @return Corresponding enum value
     */
    public static MediaLevel fromChineseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return SMALL_MEDIUM; // Default to small/medium
        }

        String trimmedValue = value.trim();
        for (MediaLevel level : values()) {
            if (level.value.equals(trimmedValue)) {
                return level;
            }
        }
        return SMALL_MEDIUM; // Default fallback
    }

    /**
     * Convert from English string value (for API compatibility)
     * Supports both CENTRAL and NATIONAL for backward compatibility
     * 
     * @param value English media level string
     * @return Corresponding enum value
     */
    public static MediaLevel fromEnglishValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return SMALL_MEDIUM;
        }

        String trimmedValue = value.trim().toUpperCase();

        // Handle legacy CENTRAL value
        if ("CENTRAL".equals(trimmedValue)) {
            return NATIONAL;
        }

        // Try to match by enum name
        try {
            return MediaLevel.valueOf(trimmedValue);
        } catch (IllegalArgumentException e) {
            return SMALL_MEDIUM; // Default fallback
        }
    }

    /**
     * Convert from English description (for API compatibility)
     * 
     * @param value English description
     * @return Corresponding enum value
     */
    public static MediaLevel fromEnglishDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return SMALL_MEDIUM;
        }

        // 由于新设计只保留中文值，英文描述匹配改为使用StandardEnum智能转换
        return StandardEnum.smartConvertWithDefault(MediaLevel.class, value, SMALL_MEDIUM);
    }

    /**
     * Check if this is a government-level media
     * 
     * @return true if national, provincial, or municipal, false otherwise
     */
    public boolean isGovernmentLevel() {
        return this == NATIONAL || this == PROVINCIAL || this == MUNICIPAL;
    }

    /**
     * Check if this is a commercial media
     * 
     * @return true if corporate, false otherwise
     */
    public boolean isCommercial() {
        return this == CORPORATE;
    }

    /**
     * Get authority level for ranking/sorting
     * 
     * @return authority level (higher = more authoritative)
     */
    public int getAuthorityLevel() {
        return switch (this) {
            case NATIONAL -> 6; // Highest authority
            case PROVINCIAL -> 5;
            case MUNICIPAL -> 4;
            case BUSINESS -> 3;
            case SMALL_MEDIUM -> 2;
            case CORPORATE -> 1; // Lowest authority
        };
    }


    @Override
    public String toString() {
        return value;
    }
}
