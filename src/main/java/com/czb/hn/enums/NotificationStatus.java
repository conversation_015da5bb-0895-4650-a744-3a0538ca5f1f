package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Notification Status Enumeration
 * 通知状态枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: PENDING=待处理, PROCESSING=处理中, COMPLETED=已完成, FAILED=失败
 */
public enum NotificationStatus implements StandardEnum<String> {
    PENDING("PENDING", "待处理"),
    PROCESSING("PROCESSING", "处理中"),
    COMPLETED("COMPLETED", "已完成"),
    FAILED("FAILED", "失败");

    private final String value;
    private final String description;

    NotificationStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Get enum value from string (case-insensitive)
     * 
     * @param value String value to convert
     * @return Corresponding enum value
     * @throws IllegalArgumentException if value is not valid
     */
    public static NotificationStatus fromString(String value) {
        return StandardEnum.smartConvertWithDefault(NotificationStatus.class, value, PENDING);
    }

    /**
     * Check if the status represents a pending notification
     */
    public boolean isPending() {
        return this == PENDING;
    }

    /**
     * Check if the status represents a processing notification
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }

    /**
     * Check if the status represents a completed notification
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * Check if the status represents a failed notification
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * Check if the notification can be processed
     */
    public boolean canProcess() {
        return this == PENDING;
    }

    /**
     * Check if the notification can be retried
     */
    public boolean canRetry() {
        return this == FAILED;
    }
}
