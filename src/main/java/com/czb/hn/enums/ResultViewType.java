package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Result View Type Enumeration
 * 结果呈现类型枚举 - Integer存储
 *
 * API字段: contentExt.isNormarlData
 * 存储方式: Integer存储 (API值)
 * API值: 1=正常, 2=噪音
 */
public enum ResultViewType implements StandardEnum<Integer> {
    NORMAL(1, "正常"),
    NOISE(2, "噪音");

    private final Integer value;
    private final String description;

    ResultViewType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from integer value (matching Sina API format)
     * 
     * @param value Integer value from API (1 = normal, 2 = noise)
     * @return Corresponding enum value
     */
    public static ResultViewType fromInteger(Integer value) {
        if (value == null) {
            return NORMAL; // Default to normal
        }

        return switch (value) {
            case 1 -> NORMAL;
            case 2 -> NOISE;
            default -> NORMAL; // Default fallback
        };
    }

    /**
     * Convert from string value (for API compatibility)
     * 
     * @param value String representation of the integer value
     * @return Corresponding enum value
     */
    public static ResultViewType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NORMAL;
        }

        try {
            int intValue = Integer.parseInt(value.trim());
            return fromInteger(intValue);
        } catch (NumberFormatException e) {
            return NORMAL;
        }
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static ResultViewType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NORMAL;
        }

        String trimmedValue = value.trim();
        for (ResultViewType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return NORMAL; // Default fallback
    }

    /**
     * Check if this content is valid/normal
     * 
     * @return true if normal content, false if noise
     */
    public boolean isValid() {
        return this == NORMAL;
    }

    /**
     * Check if this content is noise/invalid
     * 
     * @return true if noise content, false if normal
     */
    public boolean isNoise() {
        return this == NOISE;
    }

    /**
     * Get quality score for filtering
     * 
     * @return quality score (1.0 for normal, 0.0 for noise)
     */
    public double getQualityScore() {
        return switch (this) {
            case NORMAL -> 1.0;
            case NOISE -> 0.0;
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
