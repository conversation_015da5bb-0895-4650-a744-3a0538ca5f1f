package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Notification Type Enumeration
 * 通知类型枚举 - String存储
 *
 * 存储方式: String存储 (枚举名称)
 * 值: ALERT=预警通知, INFO_PUSH=信息补推, NO_ALERT=无预警通知
 */
public enum NotificationType implements StandardEnum<String> {
    ALERT("ALERT", "预警通知"),
    INFO_PUSH("INFO_PUSH", "信息补推"),
    NO_ALERT("NO_ALERT", "无预警通知");

    private final String value;
    private final String description;

    NotificationType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Get enum value from string (case-insensitive)
     * 
     * @param value String value to convert
     * @return Corresponding enum value
     * @throws IllegalArgumentException if value is not valid
     */
    public static NotificationType fromString(String value) {
        return StandardEnum.smartConvertWithDefault(NotificationType.class, value, NO_ALERT);
    }

    /**
     * Check if this is an alert notification
     */
    public boolean isAlert() {
        return this == ALERT;
    }

    /**
     * Check if this is an info push notification
     */
    public boolean isInfoPush() {
        return this == INFO_PUSH;
    }

    /**
     * Check if this is a no-alert notification
     */
    public boolean isNoAlert() {
        return this == NO_ALERT;
    }
}
