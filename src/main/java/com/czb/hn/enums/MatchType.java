package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Match Type Enumeration
 * 匹配类型枚举 - String存储
 *
 * API字段: matchInfo.type
 * 存储方式: String存储 (API值)
 * API值: "1"=关键词, "2"=用户
 */
public enum MatchType implements StandardEnum<String> {
    KEYWORD("1", "关键词"),
    USER("2", "用户");

    private final String value;
    private final String description;

    MatchType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Convert from string value (matching Sina API format)
     * 
     * @param value String value from API (1 or 2)
     * @return Corresponding enum value
     */
    public static MatchType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return KEYWORD; // Default to keyword match
        }

        String trimmedValue = value.trim();
        for (MatchType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return KEYWORD; // Default fallback
    }

    /**
     * Convert from integer value (for API compatibility)
     * 
     * @param value Integer value (1 or 2)
     * @return Corresponding enum value
     */
    public static MatchType fromInteger(Integer value) {
        if (value == null) {
            return KEYWORD;
        }

        return switch (value) {
            case 1 -> KEYWORD;
            case 2 -> USER;
            default -> KEYWORD; // Default fallback
        };
    }

    /**
     * Convert from Chinese description
     * 
     * @param value Chinese description
     * @return Corresponding enum value
     */
    public static MatchType fromChineseDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return KEYWORD;
        }

        String trimmedValue = value.trim();
        for (MatchType type : values()) {
            if (type.description.equals(trimmedValue)) {
                return type;
            }
        }
        return KEYWORD; // Default fallback
    }

    /**
     * Check if this is a keyword-based match
     * 
     * @return true if keyword match, false otherwise
     */
    public boolean isKeywordMatch() {
        return this == KEYWORD;
    }

    /**
     * Check if this is a user-based match
     * 
     * @return true if user match, false otherwise
     */
    public boolean isUserMatch() {
        return this == USER;
    }

    /**
     * Get match precision level
     * 
     * @return precision level (higher = more precise)
     */
    public int getPrecisionLevel() {
        return switch (this) {
            case USER -> 2; // Higher precision - specific user
            case KEYWORD -> 1; // Lower precision - content-based
        };
    }

    /**
     * Get processing priority (higher = process first)
     * 
     * @return processing priority level
     */
    public int getProcessingPriority() {
        return switch (this) {
            case USER -> 2; // Higher priority - user-specific alerts
            case KEYWORD -> 1; // Lower priority - general content alerts
        };
    }

    @Override
    public String toString() {
        return description;
    }
}
