package com.czb.hn.enums;

import com.czb.hn.enums.base.StandardEnum;

/**
 * Emotion Type Enumeration
 * 情绪类型枚举 - String存储
 *
 * API字段: contentExt.emotion
 * 存储方式: String存储 (中文值)
 * API值: 中性, 喜悦, 悲伤, 愤怒, 惊奇, 恐惧
 */
public enum EmotionType implements StandardEnum<String> {
    NEUTRAL("中性", 0.5),
    JOY("喜悦", 0.8),
    SADNESS("悲伤", 0.2),
    ANGER("愤怒", 0.1),
    SURPRISE("惊奇", 0.6),
    FEAR("恐惧", 0.2);

    private final String value;
    private final double sentimentScore;

    EmotionType(String value, double sentimentScore) {
        this.value = value;
        this.sentimentScore = sentimentScore;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return value;
    }

    public double getSentimentScore() {
        return sentimentScore;
    }

    /**
     * Convert from Chinese string value (matching Sina API format)
     * 
     * @param value Chinese emotion string from API
     * @return Corresponding enum value
     */
    public static EmotionType fromChineseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NEUTRAL;
        }

        String trimmedValue = value.trim();
        for (EmotionType type : values()) {
            if (type.value.equals(trimmedValue)) {
                return type;
            }
        }
        return NEUTRAL; // Default fallback
    }

    /**
     * Convert from English description (for API compatibility)
     * 
     * @param value English emotion description
     * @return Corresponding enum value
     */
    public static EmotionType fromEnglishDescription(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NEUTRAL;
        }

        // 由于新设计只保留中文值，英文描述匹配改为使用StandardEnum智能转换
        return StandardEnum.smartConvertWithDefault(EmotionType.class, value, NEUTRAL);
    }

    /**
     * Check if this emotion is positive
     * 
     * @return true if positive emotion, false otherwise
     */
    public boolean isPositive() {
        return this == JOY || this == SURPRISE;
    }

    /**
     * Check if this emotion is negative
     * 
     * @return true if negative emotion, false otherwise
     */
    public boolean isNegative() {
        return this == SADNESS || this == ANGER || this == FEAR;
    }

    /**
     * Check if this emotion is neutral
     * 
     * @return true if neutral emotion, false otherwise
     */
    public boolean isNeutral() {
        return this == NEUTRAL;
    }

    /**
     * Get emotion polarity for analysis
     * 
     * @return 1 for positive, -1 for negative, 0 for neutral
     */
    public int getPolarity() {
        if (isPositive())
            return 1;
        if (isNegative())
            return -1;
        return 0;
    }

    /**
     * Get emotion intensity level
     * 
     * @return intensity level (0-3, higher = more intense)
     */
    public int getIntensityLevel() {
        return switch (this) {
            case ANGER -> 3;
            case FEAR, SADNESS -> 2;
            case JOY, SURPRISE -> 1;
            case NEUTRAL -> 0;
        };
    }

    @Override
    public String toString() {
        return value;
    }
}
