package com.czb.hn.interceptor;

import com.czb.hn.service.business.BillingService;
import com.czb.hn.util.UserContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Billing interceptor for enterprise access control
 * Automatically checks enterprise subscription status before API access
 */
@Component
@Slf4j
public class BillingInterceptor implements HandlerInterceptor {

    @Autowired
    private BillingService billingService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        log.debug("Billing interceptor checking request: {} {}", request.getMethod(), request.getRequestURI());

        try {
            // 获取当前企业标识符
            String enterpriseIdentifier = getCurrentEnterpriseCodeEntifier(request);

            if (enterpriseIdentifier == null || enterpriseIdentifier.trim().isEmpty()) {
                log.warn("No enterprise identifier found in request: {} - User authenticated: {}",
                        request.getRequestURI(), UserContext.isAuthenticatedWithFallback(request));
                sendErrorResponse(response, HttpStatus.BAD_REQUEST, "ENTERPRISE_ID_MISSING",
                        "企业标识信息缺失，请确保已正确登录并关联企业");
                return false;
            }

            // 核心检查：企业是否有访问权限
            if (!billingService.hasAccess(enterpriseIdentifier)) {
                log.warn("Access denied for enterprise: {} on {}", enterpriseIdentifier, request.getRequestURI());
                sendErrorResponse(response, HttpStatus.FORBIDDEN, "SUBSCRIPTION_EXPIRED",
                        "订阅已过期或无效，请联系管理员续费后使用");
                return false;
            }

            log.debug("Access granted for enterprise: {} on {}", enterpriseIdentifier, request.getRequestURI());
            return true;

        } catch (Exception e) {
            log.error("Error in billing interceptor for request: {} {}", request.getMethod(), request.getRequestURI(),
                    e);
            sendErrorResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, "BILLING_CHECK_ERROR", "计费检查异常，请稍后重试");
            return false;
        }
    }

    /**
     * Get current enterprise identifier from request
     * Tries multiple sources: user context, request parameters, headers
     */
    private String getCurrentEnterpriseCodeEntifier(HttpServletRequest request) {
        // 1. 优先从用户上下文获取企业ID（ThreadLocal + Session回退）
        try {
            String enterpriseCode = UserContext.getCurrentEnterpriseCodeWithFallback(request);
            if (enterpriseCode != null && !enterpriseCode.trim().isEmpty()) {
                log.debug("Found enterprise Code from user context: {}", enterpriseCode);
                return enterpriseCode.trim();
            }
        } catch (Exception e) {
            log.debug("Could not get enterprise Code from user context: {}", e.getMessage());
        }

        log.debug("No enterprise identifier found in request");
        return null;
    }

    /**
     * Send error response in JSON format
     */
    private void sendErrorResponse(HttpServletResponse response, HttpStatus status, String errorCode, String message)
            throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("timestamp", System.currentTimeMillis());

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}
