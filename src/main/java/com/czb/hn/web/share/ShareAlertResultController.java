package com.czb.hn.web.share;

import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.dto.alert.AlertStatisticsDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.AlertSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.time.LocalDateTime;

/**
 * Share Alert Result Controller
 * 分享预警结果管理控制器，提供分享的预警结果的查询API
 */
@RestController
@RequestMapping("/share/alert-results")
@Tag(name = "ShareAlertResultController", description = "分享的预警结果查询API")
@Slf4j
public class ShareAlertResultController {

        @Autowired
        private AlertSearchService alertSearchService;

        /**
         * 搜索分享的预警结果
         * 支持时间范围、信息敏感性类型、预警级别、来源信息、内容类别和关键词搜索
         * 结果按预警时间降序排列（最新的在前）
         */
        @PostMapping("/search")
        @Operation(summary = "搜索分享的预警结果", description = "支持多维度过滤和关键词搜索，结果按预警时间降序排列")
        @ApiResponses(value = {
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "搜索成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AlertSearchResultDto.class))),
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "服务器内部错误")
        })
        public ResponseEntity<ApiResponse<AlertSearchResultDto>> searchAlerts(
                        @Valid @RequestBody AlertSearchCriteriaDto criteria) {

                try {
                        log.info("Searching alerts with criteria: planId={}, sensitivityType={}, sourceType={}, warningLevel={}",
                                        criteria.planId(), criteria.informationSensitivityType(),
                                        criteria.sourceType(), criteria.warningLevel());

                        AlertSearchResultDto result = alertSearchService.searchAlerts(criteria);
                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "搜索成功", result));

                } catch (IllegalArgumentException e) {
                        log.warn("Invalid search criteria: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "搜索条件无效: " + e.getMessage(), null));
                } catch (Exception e) {
                        log.error("Error searching alerts: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
                }
        }

        @GetMapping("/statistics")
        @Operation(summary = "获取分享的预警统计信息", description = "获取指定时间段内指定方案的预警统计信息")
        @ApiResponses(value = {
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AlertStatisticsDto.class))),
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
                        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "服务器内部错误")
        })
        public ResponseEntity<ApiResponse<AlertStatisticsDto>> getAlertStatistics(
                        @Parameter(description = "方案ID") @RequestParam Long planId,
                        @Parameter(description = "开始时间  yyyy-MM-dd HH:mm:ss") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                        @Parameter(description = "结束时间  yyyy-MM-dd HH:mm:ss") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  LocalDateTime endTime) {

                try {
                        log.info("Getting statistics for plan ID: {}, start time: {}, end time: {}", planId, startTime,
                                        endTime);

                        AlertStatisticsDto result = alertSearchService.getAlertStatistics(planId, startTime, endTime);
                        return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", result));

                } catch (IllegalArgumentException e) {
                        log.warn("Invalid search criteria: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                        .body(new ApiResponse<>("ERROR", "搜索条件无效: " + e.getMessage(), null));
                } catch (Exception e) {
                        log.error("Error getting alert statistics: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new ApiResponse<>("ERROR", "获取失败: " + e.getMessage(), null));
                }
        }

}
