package com.czb.hn.web.controllers;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.response.briefing.BriefingSummaryDto;
import com.czb.hn.service.bulletin.BulletinContentDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 简报内容数据控制器
 */
@RestController
@RequestMapping("/api/bulletin/content")
@Tag(name = "BulletinContentController", description = "简报内容数据查询接口")
public class BulletinContentController {
    
    private final BulletinContentDataService bulletinContentDataService;

    public BulletinContentController(BulletinContentDataService bulletinContentDataService) {
        this.bulletinContentDataService = bulletinContentDataService;
    }

    /**
     * 获取简报内容数据
     *
     * @param generationId 生成记录ID
     * @return 简报内容数据
     */
    @GetMapping("/{generationId}")
    @Operation(summary = "获取简报内容数据", description = "根据生成记录ID获取简报的结构化内容数据")
    public ResponseEntity<ApiResponse<BriefingSummaryDto>> getBulletinContentData(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            BriefingSummaryDto data = bulletinContentDataService.getBulletinContentData(generationId);
            if (data == null) {
                return ResponseEntity.ok(ApiResponse.success(null));
            }
            return ResponseEntity.ok(ApiResponse.success(data));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error("获取简报内容数据失败: " + e.getMessage()));
        }
    }
}