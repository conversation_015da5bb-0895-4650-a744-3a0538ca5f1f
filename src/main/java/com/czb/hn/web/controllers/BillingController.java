package com.czb.hn.web.controllers;

import com.czb.hn.dto.billing.CreateSubscriptionRequest;
import com.czb.hn.dto.billing.ExtendSubscriptionRequest;
import com.czb.hn.dto.billing.SubscriptionStatusDto;
import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.service.business.BillingService;
import com.czb.hn.service.scheduled.SubscriptionExpirationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Billing management controller
 * Provides APIs for subscription management and access control
 */
@RestController
@RequestMapping("/api/billing")
@Tag(name = "BillingController", description = "企业订阅和计费管理相关接口")
@Slf4j
public class BillingController {

    @Autowired
    private BillingService billingService;

    @Autowired
    private SubscriptionExpirationService expirationService;

    // ==================== 订阅查询接口 ====================

    @GetMapping("/subscription/status")
    @Operation(summary = "查询企业订阅状态", description = "根据企业ID或信用代码查询订阅状态")
    public ResponseEntity<SubscriptionStatusDto> getSubscriptionStatus(
            @Parameter(description = "企业标识符（企业ID或信用代码）", example = "enterprise_demo_001") @RequestParam String enterpriseIdentifier) {

        log.info("Querying subscription status for: {}", enterpriseIdentifier);

        Optional<EnterpriseSubscription> subscription = billingService.getCurrentSubscription(enterpriseIdentifier);

        if (subscription.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        SubscriptionStatusDto dto = convertToStatusDto(subscription.get());
        return ResponseEntity.ok(dto);
    }

    @GetMapping("/subscription/check")
    @Operation(summary = "检查企业访问权限", description = "检查企业是否有有效的订阅和访问权限")
    public ResponseEntity<Map<String, Object>> checkAccess(
            @Parameter(description = "企业标识符（企业ID或信用代码）", example = "enterprise_demo_001") @RequestParam String enterpriseIdentifier) {

        log.info("Checking access for: {}", enterpriseIdentifier);

        boolean hasAccess = billingService.hasAccess(enterpriseIdentifier);

        Map<String, Object> result = new HashMap<>();
        result.put("enterpriseIdentifier", enterpriseIdentifier);
        result.put("hasAccess", hasAccess);
        result.put("timestamp", System.currentTimeMillis());

        if (hasAccess) {
            Optional<EnterpriseSubscription> subscription = billingService.getCurrentSubscription(enterpriseIdentifier);
            subscription.ifPresent(sub -> {
                result.put("subscriptionStatus", sub.getStatus().name());
                result.put("endDate", sub.getEndDate());
                result.put("daysUntilExpiration", sub.getDaysUntilExpiration());
            });
        }

        return ResponseEntity.ok(result);
    }

    // ==================== 订阅管理接口 ====================

    @PostMapping("/subscription")
    @Operation(summary = "创建企业订阅", description = "为企业创建新的订阅")
    public ResponseEntity<SubscriptionStatusDto> createSubscription(
            @Valid @RequestBody CreateSubscriptionRequest request) {

        log.info("Creating subscription for enterprise: {}", request.enterpriseId());

        EnterpriseSubscription subscription = billingService.createSubscription(
                request.enterpriseId(),
                request.enterpriseCreditCode(),
                request.startDate(),
                request.endDate());

        SubscriptionStatusDto dto = convertToStatusDto(subscription);
        return ResponseEntity.ok(dto);
    }

    @PutMapping("/subscription/extend")
    @Operation(summary = "延长企业订阅", description = "延长企业订阅的到期时间")
    public ResponseEntity<SubscriptionStatusDto> extendSubscription(
            @Parameter(description = "企业标识符（企业ID或信用代码）", example = "enterprise_demo_001") @RequestParam String enterpriseIdentifier,
            @Valid @RequestBody ExtendSubscriptionRequest request) {

        log.info("Extending subscription for enterprise: {} to {}", enterpriseIdentifier, request.newEndDate());

        EnterpriseSubscription subscription = billingService.extendSubscription(
                enterpriseIdentifier,
                request.newEndDate());

        SubscriptionStatusDto dto = convertToStatusDto(subscription);
        return ResponseEntity.ok(dto);
    }

    @PutMapping("/subscription/cancel")
    @Operation(summary = "取消企业订阅", description = "取消企业的订阅")
    public ResponseEntity<SubscriptionStatusDto> cancelSubscription(
            @Parameter(description = "企业标识符（企业ID或信用代码）", example = "enterprise_demo_001") @RequestParam String enterpriseIdentifier) {

        log.info("Cancelling subscription for enterprise: {}", enterpriseIdentifier);

        EnterpriseSubscription subscription = billingService.cancelSubscription(enterpriseIdentifier);

        SubscriptionStatusDto dto = convertToStatusDto(subscription);
        return ResponseEntity.ok(dto);
    }

    @PutMapping("/subscription/reactivate")
    @Operation(summary = "重新激活企业订阅", description = "重新激活企业的订阅")
    public ResponseEntity<SubscriptionStatusDto> reactivateSubscription(
            @Parameter(description = "企业标识符（企业ID或信用代码）", example = "enterprise_demo_001") @RequestParam String enterpriseIdentifier,
            @Valid @RequestBody ExtendSubscriptionRequest request) {

        log.info("Reactivating subscription for enterprise: {} until {}", enterpriseIdentifier, request.newEndDate());

        EnterpriseSubscription subscription = billingService.reactivateSubscription(
                enterpriseIdentifier,
                request.newEndDate());

        SubscriptionStatusDto dto = convertToStatusDto(subscription);
        return ResponseEntity.ok(dto);
    }

    // ==================== 统计和管理接口 ====================

    @GetMapping("/subscriptions/expiring")
    @Operation(summary = "获取即将过期的订阅", description = "获取指定天数内即将过期的订阅列表")
    public ResponseEntity<List<SubscriptionStatusDto>> getExpiringSubscriptions(
            @Parameter(description = "天数", example = "7") @RequestParam(defaultValue = "7") int days) {

        log.info("Getting subscriptions expiring within {} days", days);

        List<EnterpriseSubscription> subscriptions = billingService.getExpiringSubscriptions(days);
        List<SubscriptionStatusDto> dtos = subscriptions.stream()
                .map(this::convertToStatusDto)
                .collect(Collectors.toList());

        return ResponseEntity.ok(dtos);
    }

    @PostMapping("/subscriptions/update-expired")
    @Operation(summary = "手动更新过期订阅状态", description = "手动触发过期订阅状态更新（通常由定时任务自动执行）")
    public ResponseEntity<Map<String, Object>> updateExpiredSubscriptions() {
        log.info("Manual trigger: Updating expired subscriptions");

        int updatedCount = expirationService.manualExpirationCheck();

        Map<String, Object> result = new HashMap<>();
        result.put("updatedCount", updatedCount);
        result.put("timestamp", System.currentTimeMillis());
        result.put("triggerType", "manual");

        return ResponseEntity.ok(result);
    }

    @GetMapping("/subscriptions/expiration-stats")
    @Operation(summary = "获取过期统计信息", description = "获取订阅过期相关的统计数据")
    public ResponseEntity<SubscriptionExpirationService.ExpirationStatistics> getExpirationStatistics() {
        log.info("Getting expiration statistics");

        SubscriptionExpirationService.ExpirationStatistics stats = expirationService.getExpirationStatistics();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取订阅统计信息", description = "获取订阅的统计数据")
    public ResponseEntity<Map<String, Long>> getStatistics() {
        log.info("Getting subscription statistics");

        Map<String, Long> statistics = billingService.getSubscriptionStatistics();
        return ResponseEntity.ok(statistics);
    }

    // ==================== 辅助方法 ====================

    /**
     * Convert EnterpriseSubscription entity to SubscriptionStatusDto
     */
    private SubscriptionStatusDto convertToStatusDto(EnterpriseSubscription subscription) {
        return new SubscriptionStatusDto(
                subscription.getEnterpriseId(),
                subscription.getEnterpriseCreditCode(),
                subscription.getStartDate(),
                subscription.getEndDate(),
                subscription.getStatus().name(),
                subscription.isActiveAndValid(),
                subscription.getDaysUntilExpiration(),
                subscription.getAutoRenew(),
                subscription.getNotes());
    }
}
