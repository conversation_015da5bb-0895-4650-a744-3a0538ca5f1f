package com.czb.hn.web.controllers;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.user.UserLoginStatsDTO;
import com.czb.hn.service.user.UserLoginRecordService;
import com.czb.hn.util.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * User Login Statistics Controller
 * Provides APIs for user login statistics and tracking
 */
@Slf4j
@RestController
@RequestMapping("/user/login-stats")
@Tag(name = "UserLoginStatsController", description = "提供用户登录统计相关的API")
public class UserLoginStatsController {

    @Autowired
    private UserLoginRecordService userLoginRecordService;

    @GetMapping("/current")
    @Operation(summary = "获取当前用户登录统计", description = "获取当前登录用户的登录统计信息")
    public ResponseEntity<ApiResponse<UserLoginStatsDTO>> getCurrentUserLoginStats() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            UserLoginStatsDTO stats = userLoginRecordService.getUserLoginStats(currentUserId);
            if (stats == null) {
                return ResponseEntity.ok(ApiResponse.error("用户暂无登录记录"));
            }

            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("Failed to get current user login stats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取登录统计失败: " + e.getMessage()));
        }
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取指定用户登录统计", description = "获取指定用户的登录统计信息")
    public ResponseEntity<ApiResponse<UserLoginStatsDTO>> getUserLoginStats(@PathVariable String userId) {
        try {
            UserLoginStatsDTO stats = userLoginRecordService.getUserLoginStats(userId);
            if (stats == null) {
                return ResponseEntity.ok(ApiResponse.error("用户暂无登录记录"));
            }

            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("Failed to get login stats for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取登录统计失败: " + e.getMessage()));
        }
    }

    @GetMapping("/last-login-time")
    @Operation(summary = "获取当前用户上次登录时间", description = "获取当前用户的上次登录时间")
    public ResponseEntity<ApiResponse<LocalDateTime>> getCurrentUserLastLoginTime() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            LocalDateTime lastLoginTime = userLoginRecordService.getLastLoginTime(currentUserId);
            return ResponseEntity.ok(ApiResponse.success(lastLoginTime));
        } catch (Exception e) {
            log.error("Failed to get current user last login time", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取上次登录时间失败: " + e.getMessage()));
        }
    }

    @GetMapping("/login-count/{days}")
    @Operation(summary = "获取当前用户指定天数内登录次数", description = "获取当前用户在指定天数内的登录次数")
    public ResponseEntity<ApiResponse<Long>> getCurrentUserLoginCount(@PathVariable int days) {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            if (days <= 0 || days > 365) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("天数参数必须在1-365之间"));
            }

            long loginCount = userLoginRecordService.getLoginCountInDays(currentUserId, days);
            return ResponseEntity.ok(ApiResponse.success(loginCount));
        } catch (Exception e) {
            log.error("Failed to get current user login count for {} days", days, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取登录次数失败: " + e.getMessage()));
        }
    }
}
