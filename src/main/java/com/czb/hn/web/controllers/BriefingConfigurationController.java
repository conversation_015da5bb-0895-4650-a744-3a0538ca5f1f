package com.czb.hn.web.controllers;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.briefing.BriefingConfigurationCreateDto;
import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.BriefingConfigurationUpdateDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.BriefingConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/briefing-configuration")
@Tag(name = "BriefingConfigurationController", description = "简报配置管理")
public class BriefingConfigurationController {

    private static final Logger logger = LoggerFactory.getLogger(BriefingConfigurationController.class);

    @Autowired
    private BriefingConfigurationService briefingConfigurationService;

    @PostMapping
    @Operation(summary = "创建简报配置")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Configuration created successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = BriefingConfigurationResponseDto.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<BriefingConfigurationResponseDto>> createConfiguration(
            @RequestBody BriefingConfigurationCreateDto createDto) {
        try {
            logger.info("Creating briefing configuration: {}", createDto.name());
            BriefingConfigurationResponseDto response = briefingConfigurationService.createConfiguration(createDto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(new ApiResponse<>("SUCCESS", "Briefing configuration created successfully", response));
        } catch (IllegalArgumentException e) {
                logger.warn("Invalid input for creating briefing configuration: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponse<>("ERROR", "Invalid input: " + e.getMessage(), null));
        } catch (Exception e) {
                logger.error("Error creating briefing configuration: {}", e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>("ERROR", "Failed to create briefing configuration: " + e.getMessage(),
                                null));
        }
    }

    @GetMapping("/{planId}")
    @Operation(summary = "根据方案id获取简报配置")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Configuration retrieved successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = BriefingConfigurationResponseDto.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Configuration not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<BriefingConfigurationResponseDto>> getConfiguration(
            @Parameter(description = "Plan ID") @PathVariable Long planId) {
        try {
            logger.info("Getting briefing configuration with ID: {}", planId);
            BriefingConfigurationResponseDto response = briefingConfigurationService.getConfigurationsByPlanId(planId);
            return ResponseEntity.ok(
                    new ApiResponse<>("SUCCESS", "Configuration retrieved successfully", response));
        } catch (IllegalArgumentException e) {
            logger.warn("Configuration not found with ID: {}", planId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        } catch (Exception e) {
            logger.error("Error getting alert configuration with ID {}: {}", planId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR",
                            "Failed to get briefing configuration: " + e.getMessage(), null));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新简报配置")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Configuration updated successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = BriefingConfigurationResponseDto.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Configuration not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<BriefingConfigurationResponseDto>> updateConfiguration(
            @Parameter(description = "Configuration ID") @PathVariable Long id,
            @RequestBody BriefingConfigurationUpdateDto updateDto) {
        try {
            logger.info("Updating briefing configuration with ID: {}", id);
            BriefingConfigurationResponseDto response = briefingConfigurationService.updateConfiguration(id, updateDto);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "Configuration updated successfully", response));
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid input for updating briefing configuration {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "Invalid input: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("Error updating briefing configuration with ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "Failed to update briefing configuration: " + e.getMessage(), null));
        }
    }
}
