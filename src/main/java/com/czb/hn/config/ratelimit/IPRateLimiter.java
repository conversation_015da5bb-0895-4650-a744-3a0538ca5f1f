package com.czb.hn.config.ratelimit;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.RateLimiter;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 基于IP地址的限流器
 * 用于防止同一IP高频访问API
 */
@Component
public class IPRateLimiter {
    
    /**
     * 存储IP地址对应的限流器
     * 每个IP地址有独立的速率限制
     */
    private final LoadingCache<String, RateLimiter> ipLimiters;
    
    /**
     * 默认每个IP的请求速率（次/秒）
     */
    private static final double DEFAULT_RATE = 2.0;
    
    /**
     * 缓存过期时间（分钟）
     * 长时间未访问的IP限流器将被清理
     */
    private static final int EXPIRE_MINUTES = 30;
    
    public IPRateLimiter() {
        // 初始化IP限流器缓存
        ipLimiters = CacheBuilder.newBuilder()
                .maximumSize(10000)  // 最多缓存10000个IP限流器
                .expireAfterAccess(EXPIRE_MINUTES, TimeUnit.MINUTES)  // 30分钟不活动则过期
                .build(new CacheLoader<>() {
                    @Override
                    public RateLimiter load(String ip) {
                        return RateLimiter.create(DEFAULT_RATE);
                    }
                });
    }
    
    /**
     * 检查指定IP是否允许访问
     *
     * @param ip IP地址
     * @param permitsPerSecond 每秒允许的请求数
     * @param timeout 获取令牌的等待时间
     * @param timeUnit 时间单位
     * @return 是否允许访问
     */
    public boolean tryAcquire(String ip, double permitsPerSecond, long timeout, TimeUnit timeUnit) {
        try {
            // 获取IP对应的限流器
            RateLimiter limiter = ipLimiters.get(ip);
            
            // 如果配置的速率与当前不同，则更新
            if (Math.abs(limiter.getRate() - permitsPerSecond) > 0.001) {
                limiter.setRate(permitsPerSecond);
            }
            
            // 尝试获取令牌
            return limiter.tryAcquire(timeout, timeUnit);
        } catch (ExecutionException e) {
            // 异常情况默认允许访问，避免限流器故障影响正常业务
            return true;
        }
    }
} 