package com.czb.hn.config.ratelimit;

import com.czb.hn.dto.response.ApiResponse;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.RateLimiter;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 限流切面实现
 */
@Aspect
@Component
public class RateLimiterAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(RateLimiterAspect.class);
    
    @Autowired
    private IPRateLimiter ipRateLimiter;
    
    private final LoadingCache<String, RateLimiter> limiterCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<>() {
                @Override
                public RateLimiter load(String key) {
                    // 默认限流
                    return RateLimiter.create(1);
                }
            });
    
    @Around("@annotation(com.czb.hn.config.ratelimit.RateLimit)")
    public Object rateLimit(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RateLimit rateLimit = method.getAnnotation(RateLimit.class);
        
        if (rateLimit == null) {
            return joinPoint.proceed();
        }
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        String ipAddress = getIpAddress(request);
        String requestURI = request != null ? request.getRequestURI() : "unknown";
        String methodKey = method.getDeclaringClass().getName() + "." + method.getName();
        
        boolean allowRequest = true;
        String limitReason = "";
        
        // 全局接口限流
        if (rateLimit.permits() > 0) {
            try {
                RateLimiter rateLimiter = limiterCache.get(methodKey);
                // 更新限流器速率（如果配置有变化）
                if (rateLimiter.getRate() != rateLimit.permits()) {
                    rateLimiter.setRate(rateLimit.permits());
                }
                
                // 尝试获取令牌
                if (!rateLimiter.tryAcquire(rateLimit.timeout(), rateLimit.timeUnit())) {
                    allowRequest = false;
                    limitReason = "全局接口限流";
                }
            } catch (ExecutionException e) {
                logger.error("获取全局限流器失败", e);
                // 出错时继续检查IP限流
            }
        }
        
        // 如果全局限流通过，继续检查IP限流
        if (allowRequest && rateLimit.ipPermits() > 0) {
            boolean ipAllowed = ipRateLimiter.tryAcquire(
                ipAddress, 
                rateLimit.ipPermits(), 
                rateLimit.timeout(), 
                rateLimit.timeUnit()
            );
            
            if (!ipAllowed) {
                allowRequest = false;
                limitReason = "IP限流";
            }
        }
        
        // 如果限流生效，返回限流响应
        if (!allowRequest) {
            logger.warn("API限流: {} 请求被限流 ({}), URI: {}, IP: {}, 限流速率: {}个请求/秒", 
                    methodKey, limitReason, requestURI, ipAddress, rateLimit.permits());
            
            return ResponseEntity
                    .status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(ApiResponse.error(rateLimit.description() + " (" + limitReason + ")"));
        }
        
        logger.debug("API请求通过限流检查: {}, URI: {}, IP: {}", methodKey, requestURI, ipAddress);
        return joinPoint.proceed();
    }
    
    /**
     * 获取请求真实IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
} 