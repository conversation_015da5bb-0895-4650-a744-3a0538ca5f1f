package com.czb.hn.config.ratelimit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 自定义限流注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    /**
     * 全局限流：每秒允许的请求数
     */
    double permits() default 1.0;
    
    /**
     * IP限流：每个IP每秒允许的请求数
     * 设为0时不启用IP限流
     */
    double ipPermits() default 0.0;
    
    /**
     * 获取令牌的等待时间
     */
    long timeout() default 0;
    
    /**
     * 等待时间单位
     */
    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;
    
    /**
     * 限流描述，用于日志和错误消息
     */
    String description() default "API rate limit exceeded";
} 