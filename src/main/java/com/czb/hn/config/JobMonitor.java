package com.czb.hn.config;

import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.jpa.securadar.repository.JobRepository;
import com.czb.hn.service.job.JobInfo;
import com.czb.hn.service.job.impl.JobInfoImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

@Component
public class JobMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(JobMonitor.class);
    
    private final JobRepository jobRepository;
    
    private final TaskScheduler taskScheduler;
    
    private final ApplicationContext applicationContext;
    
    private final Map<Long, JobEntity> runningJobs = new ConcurrentHashMap<>();
    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    public JobMonitor(JobRepository jobRepository, TaskScheduler taskScheduler, ApplicationContext applicationContext) {
        this.jobRepository = jobRepository;
        this.taskScheduler = taskScheduler;
        this.applicationContext = applicationContext;
    }

    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorJobChanges() {
        log.debug("开始检查任务变更");
        
        // 获取所有启用的任务
        List<JobEntity> activeJobs = jobRepository.findByEnabledTrue();
        
        // 检查新增或修改的任务
        for (JobEntity job : activeJobs) {
            JobEntity existingJob = runningJobs.get(job.getId());
            
            // 任务是新增或已修改
            if (existingJob == null || !isJobEquals(existingJob, job)) {
                updateScheduledTask(job);
                runningJobs.put(job.getId(), job);
            }
        }
        
        // 检查已删除或禁用的任务
        Set<Long> activeJobIds = activeJobs.stream()
            .map(JobEntity::getId)
            .collect(Collectors.toSet());
        
        runningJobs.keySet().stream()
            .filter(id -> !activeJobIds.contains(id))
            .forEach(this::cancelScheduledTask);
    }
    
    private boolean isJobEquals(JobEntity job1, JobEntity job2) {
        // 比较关键属性是否相同
        return Objects.equals(job1.getCron(), job2.getCron()) &&
               Objects.equals(job1.getHandler(), job2.getHandler()) &&
               Objects.equals(job1.getJobParams(), job2.getJobParams()) &&
               Objects.equals(job1.getEnabled(), job2.getEnabled()) &&
               Objects.equals(job1.getUpdateTime(), job2.getUpdateTime());
    }
    
    private void updateScheduledTask(JobEntity job) {
        // 先取消已存在的任务
        cancelScheduledTask(job.getId());
        
        try {
            // 解析处理器信息
            String[] handlerInfo = job.getHandler().split("#");
            String beanName = handlerInfo[0];
            String methodName = handlerInfo[1];
            
            // 获取相应的bean和方法
            Object targetBean = applicationContext.getBean(beanName);
            Method targetMethod = findTargetMethod(targetBean.getClass(), methodName);
            
            // 创建任务执行器
            Runnable taskRunner = () -> {
                try {
                    // 创建JobInfo对象
                    JobInfo jobInfo = new JobInfoImpl(job);
                    
                    // 反射调用目标方法
                    targetMethod.invoke(targetBean, jobInfo);
                } catch (Exception e) {
                    log.error("任务执行异常: {}", job.getName(), e);
                }
            };
            
            // 使用Cron表达式调度任务
            CronTrigger trigger = new CronTrigger(job.getCron());
            ScheduledFuture<?> scheduledTask = taskScheduler.schedule(taskRunner, trigger);
            
            // 保存调度任务的引用
            scheduledTasks.put(job.getId(), scheduledTask);
            
            log.info("任务已调度: {}, cron: {}", job.getName(), job.getCron());
            
        } catch (Exception e) {
            log.error("任务调度失败: {}", job.getName(), e);
        }
    }
    
    private void cancelScheduledTask(Long jobId) {
        ScheduledFuture<?> scheduledTask = scheduledTasks.remove(jobId);
        if (scheduledTask != null) {
            scheduledTask.cancel(false);
            runningJobs.remove(jobId);
            log.info("任务已取消: {}", jobId);
        }
    }
    
    private Method findTargetMethod(Class<?> targetClass, String methodName) {
        for (Method method : targetClass.getDeclaredMethods()) {
            if (method.getName().equals(methodName) && 
                method.getParameterCount() == 1 && 
                JobInfo.class.isAssignableFrom(method.getParameterTypes()[0])) {
                return method;
            }
        }
        throw new IllegalArgumentException("找不到匹配的方法: " + methodName);
    }
}