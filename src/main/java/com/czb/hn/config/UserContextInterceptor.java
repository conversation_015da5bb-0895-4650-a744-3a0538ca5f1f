package com.czb.hn.config;

import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContext;
import com.czb.hn.dto.user.LoginUserContextHolder;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户上下文拦截器
 * 负责在请求处理前恢复用户认证信息，整合Spring Security和LoginUserContext
 */
@Component
public class UserContextInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(UserContextInterceptor.class);
    private static final String CURRENT_USER_KEY = "CURRENT_USER";
    private static final String SPRING_SECURITY_CONTEXT = HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 清除之前的上下文
        SecurityContextHolder.clearContext();
        LoginUserContextHolder.clearContext();

        HttpSession session = request.getSession(false);
        if (session != null) {
            try {
                // 1. 首先尝试从会话中恢复LoginUser（优先）
                LoginUser loginUser = (LoginUser) session.getAttribute(CURRENT_USER_KEY);
                if (loginUser != null) {
                    // 设置到LoginUserContextHolder
                    LoginUserContext userContext = new LoginUserContext();
                    userContext.setUser(loginUser);
                    LoginUserContextHolder.setContext(userContext);
                    log.debug("Restored LoginUser from session: {}", loginUser.getUserId());

                    // 2. 从SecurityContext恢复认证信息
                    // SecurityContext会在Spring Security过滤器链中自动从会话恢复，
                    // 但我们可以检查并确保它正确设置
                    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                    if (authentication == null) {
                        // 如果SecurityContext中没有认证但有LoginUser，创建一个认证对象
                        log.debug("Creating authentication from LoginUser: {}", loginUser.getUserId());

                        // 从LoginUser中提取角色信息，创建授权列表
                        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                        if (loginUser.getUserRoles() != null) {
                            authorities = loginUser.getUserRoles().stream()
                                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getRoleName()))
                                    .collect(Collectors.toList());
                        }

                        // 创建认证对象并设置到SecurityContext
                        authentication = new UsernamePasswordAuthenticationToken(
                                loginUser.getUserId(), null, authorities);

                        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
                        securityContext.setAuthentication(authentication);
                        SecurityContextHolder.setContext(securityContext);

                        // 保存到会话中
                        session.setAttribute(SPRING_SECURITY_CONTEXT, securityContext);
                        log.debug("Created and saved authentication to session for user: {}", loginUser.getUserId());
                    }
                }

                // 记录认证状态
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null) {
                    log.debug("User is authenticated: {} (type: {})",
                            authentication.getName(),
                            authentication.getClass().getSimpleName());
                }
            } catch (Exception e) {
                log.error("Error restoring user context from session", e);
            }
        }

        return true;
    }

    /**
     * 保存用户信息到会话
     */
    public static void saveUserToSession(HttpServletRequest request, LoginUser loginUser) {
        if (loginUser != null) {
            HttpSession session = request.getSession(true);
            session.setAttribute(CURRENT_USER_KEY, loginUser);
            log.debug("Saved LoginUser {} to session {}", loginUser.getUserId(), session.getId());
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) {
        // 请求处理后的逻辑（如果需要）
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        // 清理ThreadLocal，防止内存泄漏
        LoginUserContextHolder.clearContext();
        SecurityContextHolder.clearContext();
    }
}