package com.czb.hn.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Unified Cache Configuration
 * 统一缓存配置管理器，包含告警配置和计费系统的缓存
 */
@Configuration
@EnableCaching
@EnableAsync
public class AlertConfigurationCacheConfig {

    /**
     * 统一缓存管理器
     * 使用ConcurrentMapCacheManager提供高性能的内存缓存
     * 包含告警配置和计费系统的所有缓存
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 预定义缓存名称，提高性能
        cacheManager.setCacheNames(java.util.Arrays.asList(
                // 告警配置相关缓存
                "activeConfigurations", // 活跃配置缓存
                "singleConfiguration", // 单个配置缓存

                // 计费系统相关缓存
                "enterpriseAccess", // 企业访问权限缓存
                "subscriptionData" // 订阅数据缓存
        ));

        // 允许运行时创建新缓存
        cacheManager.setAllowNullValues(false);

        return cacheManager;
    }
}
