package com.czb.hn.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 应用配置类
 */
@Configuration
@EnableScheduling
public class JsonConfig {

    /**
     * 配置Jackson ObjectMapper
     * 支持Java 8日期时间类型序列化，统一使用 yyyy-MM-dd HH:mm:ss 格式
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册Java时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        objectMapper.registerModule(javaTimeModule);

        // 禁用时间戳格式，使用字符串格式
        objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 设置统一的日期时间格式
        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 设置时区为系统默认时区，避免时区转换
        dateFormat.setTimeZone(java.util.TimeZone.getDefault());
        objectMapper.setDateFormat(dateFormat);

        // 设置时区为系统默认时区
        objectMapper.setTimeZone(java.util.TimeZone.getDefault());

        // Configure Jackson to ignore unknown properties during deserialization
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }
}