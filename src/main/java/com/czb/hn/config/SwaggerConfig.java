package com.czb.hn.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;

/**
 * Swagger配置类
 * 提供OpenAPI文档和Swagger UI配置
 */
@Configuration
public class SwaggerConfig {

        @Value("${server.servlet.context-path:}")
        private String contextPath;

        /**
         * 配置OpenAPI对象，提供API基本信息
         */
        @Bean
        public OpenAPI customOpenAPI() {
                return new OpenAPI()
                                .addServersItem(new Server()
                                                .url(contextPath)
                                                .description("Default Server"))
                                .info(new Info()
                                                .title("SecuRadar API")
                                                .description("SecuRadar Application API Documentation")
                                                .version("v1.0")
                                                .contact(new Contact()
                                                                .name("SecuRadar Team")
                                                                .email("<EMAIL>")
                                                                .url("https://securadar.example.com"))
                                                .license(new License()
                                                                .name("Private License")
                                                                .url("https://www.example.com/licenses")))
                                .components(new Components()
                                                .addSecuritySchemes("onepass", new SecurityScheme()
                                                                .type(SecurityScheme.Type.HTTP)
                                                                .scheme("bearer")
                                                                .bearerFormat("JWT")
                                                                .description("使用OnePass认证系统登录获取的JWT令牌")));
        }

        /**
         * 创建默认API组，确保所有API都被包含
         */
        @Bean
        public GroupedOpenApi defaultApi() {
                return GroupedOpenApi.builder()
                                .group("default")
                                .pathsToMatch("/**")
                                .build();
        }

}