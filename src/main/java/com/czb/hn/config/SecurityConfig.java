package com.czb.hn.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.SessionManagementConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.session.RegisterSessionAuthenticationStrategy;
import org.springframework.security.web.authentication.session.SessionAuthenticationStrategy;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import com.czb.hn.util.ResponseUtils;

import cn.com.ycfin.onepass.client.OnePassRedirect;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * 配置安全过滤链
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 使用简化的配置
        http
                // 禁用CSRF
                .csrf(AbstractHttpConfigurer::disable)

                // 配置授权
                .authorizeHttpRequests((requests) -> requests
                        // Swagger UI相关路径 - 使用更简洁的通配符
                        .requestMatchers(
                                "/swagger-ui.html",
                                "/swagger-ui/**",
                                "/v3/api-docs/**",
                                "/webjars/**")
                        .permitAll()

                        // 公开路径
                        .requestMatchers(
                                "/onepass/login",
                                "/onepass/logout",
                                "/onepass/login/**",
                                "/onepass/refresh-groups-cache",
                                "/groups/refresh",
                                "/",
                                "/login",
                                "/error",
                                "/test/**",
                                "/alert-results/**",
                                "/workbench/**",
                                "/**",
                                "/share/**")
                        .permitAll()

                        // 所有其他请求需要认证
                        .anyRequest().authenticated())
                .exceptionHandling(exceptionHandling -> exceptionHandling
                        .authenticationEntryPoint((request, response, e) -> {
                            response.setStatus(HttpStatus.UNAUTHORIZED.value());
                            ResponseUtils.writeHttpResponseJson(response, new OnePassRedirect("/api/onepass/login"));
                        }))
                // 禁用表单登录和HTTP Basic
                .formLogin(AbstractHttpConfigurer::disable)
                .httpBasic(AbstractHttpConfigurer::disable)
                // 会话管理配置
                .sessionManagement(sessionManagement -> {
                    sessionManagement
                            // 使用IF_REQUIRED确保只在需要时创建会话
                            .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                            // 防止会话固定攻击
                            .sessionFixation(SessionManagementConfigurer.SessionFixationConfigurer::newSession)
                            // 启用会话认证策略
                            .sessionAuthenticationStrategy(sessionAuthenticationStrategy())
                            // 允许同一用户最多10个会话
                            .maximumSessions(10)
                            // 超过最大会话数时不阻止新登录，而是使最旧的会话失效
                            .maxSessionsPreventsLogin(false)
                            // 使用会话注册表跟踪会话
                            .sessionRegistry(sessionRegistry())
                            // 会话过期处理
                            .expiredUrl("/onepass/login");
                })
                // 配置SecurityContext存储库
                .securityContext(securityContext -> securityContext
                        .securityContextRepository(securityContextRepository())
                        .requireExplicitSave(true));

        return http.build();
    }

    /**
     * 会话认证策略 - 登录成功时注册会话
     */
    @Bean
    public SessionAuthenticationStrategy sessionAuthenticationStrategy() {
        return new RegisterSessionAuthenticationStrategy(sessionRegistry());
    }

    /**
     * 会话注册表 - 跟踪活动会话
     */
    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    /**
     * HTTP会话事件发布器 - 确保会话事件能被Spring Security捕获
     */
    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }

    /**
     * SecurityContext存储库 - 负责在HTTP会话中存储和检索认证信息
     */
    @Bean
    public SecurityContextRepository securityContextRepository() {
        return new HttpSessionSecurityContextRepository();
    }
}