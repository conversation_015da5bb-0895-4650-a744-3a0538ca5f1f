package com.czb.hn.config;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionEvent;
import jakarta.servlet.http.HttpSessionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 会话监听器，用于监控会话的创建和销毁
 */
@Component
public class SessionListener implements HttpSessionListener {
    
    private static final Logger log = LoggerFactory.getLogger(SessionListener.class);
    
    @Override
    public void sessionCreated(HttpSessionEvent event) {
        HttpSession session = event.getSession();
        log.debug("Session created: {}, max inactive interval: {} seconds", 
                 session.getId(), session.getMaxInactiveInterval());
    }
    
    @Override
    public void sessionDestroyed(HttpSessionEvent event) {
        HttpSession session = event.getSession();
        log.debug("Session destroyed: {}", session.getId());
    }
} 