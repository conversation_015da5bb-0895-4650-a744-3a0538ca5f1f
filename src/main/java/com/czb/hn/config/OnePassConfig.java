package com.czb.hn.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.com.ycfin.onepass.client.OnePassClient;

@Configuration
@ConfigurationProperties(prefix = "onepass")
public class OnePassConfig {
    private String issuer;
    private String clientId;
    private String clientSecret;
    private String redirectUri;

    @Bean
    public OnePassClient onePassClient() {
        return new OnePassClient(issuer, clientId, clientSecret, redirectUri);
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}
