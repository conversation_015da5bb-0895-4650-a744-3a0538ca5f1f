package com.czb.hn.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务配置类
 * 支持分环境配置和开关控制
 */
@Configuration
@ConfigurationProperties(prefix = "schedule")
@Data
public class ScheduleConfig {

    /**
     * 数据收集器配置
     */
    private Collector collector = new Collector();

    /**
     * 数据清洗器配置
     */
    private Cleaner cleaner = new Cleaner();

    /**
     * 数据聚合器配置
     */
    private Aggregator aggregator = new Aggregator();

    /**
     * Elasticsearch同步配置
     */
    private ElasticsearchSync elasticsearchSync = new ElasticsearchSync();

    /**
     * 计费系统配置
     */
    private Billing billing = new Billing();

    @Data
    public static class Collector {
        /**
         * 是否启用数据收集定时任务
         */
        private boolean enabled = true;

        /**
         * 定时任务执行的cron表达式
         */
        private String cron = "0 */10 * * * *";

        /**
         * 环境配置 - 可以根据不同环境设置不同的行为
         */
        private String environment = "default";

        /**
         * 批处理大小
         */
        private int batchSize = 500;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long retryInterval = 5000;
    }

    @Data
    public static class Cleaner {
        /**
         * 是否启用数据清洗定时任务
         */
        private boolean enabled = true;

        /**
         * 定时任务执行的cron表达式
         */
        private String cron = "0 */15 * * * *";

        /**
         * 环境配置
         */
        private String environment = "default";

        /**
         * 批处理大小
         */
        private int batchSize = 100;
    }

    @Data
    public static class Aggregator {
        /**
         * 是否启用数据聚合定时任务
         */
        private boolean enabled = true;

        /**
         * 定时任务执行的cron表达式
         */
        private String cron = "0 0 1 * * *";

        /**
         * 环境配置
         */
        private String environment = "default";
    }

    @Data
    public static class ElasticsearchSync {
        /**
         * 是否启用Elasticsearch同步定时任务
         */
        private boolean enabled = true;

        /**
         * 定时任务执行的cron表达式
         */
        private String cron = "0 */5 * * * *";

        /**
         * 环境配置
         */
        private String environment = "default";

        /**
         * 批处理大小
         */
        private int batchSize = 100;
    }

    @Data
    public static class Billing {
        /**
         * 是否启用计费系统定时任务
         */
        private boolean enabled = true;

        /**
         * 过期检查定时任务的cron表达式
         */
        private String expirationCheckCron = "0 0 2 * * ?";

        /**
         * 过期警告定时任务的cron表达式
         */
        private String expirationWarningCron = "0 0 9 * * ?";

        /**
         * 环境配置
         */
        private String environment = "default";

        /**
         * 过期警告提前天数
         */
        private int warningDays = 7;
    }
}
