package com.czb.hn.config;

import com.czb.hn.dto.ShareLinkDto;
import com.czb.hn.service.share.ShareLinkService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 分享码校验过滤器，支持通配符URL配置
 */
@Component
public class ShareCodeFilter extends OncePerRequestFilter {

    @Value("${share.protected-urls:/pdf/**,/share/**}")
    private String protectedUrls;

    private final ShareLinkService shareLinkService;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public ShareCodeFilter(ShareLinkService shareLinkService) {
        this.shareLinkService = shareLinkService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String path = request.getRequestURI();
        List<String> urlPatterns = Arrays.asList(protectedUrls.split(","));

        boolean needAuth = urlPatterns.stream().anyMatch(pattern -> pathMatcher.match(pattern.trim(), path));
        if (needAuth) {
            String shareCode = request.getParameter("shareCode");
            if (shareCode == null || shareCode.isBlank()) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("缺少分享码");
                return;
            }
            try {
                ShareLinkDto dto = shareLinkService.validateShareCode(shareCode);
                // 可将dto放入request attribute，供后续业务使用
                request.setAttribute("shareLink", dto);
            } catch (Exception e) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("无效或已过期的分享码");
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
}