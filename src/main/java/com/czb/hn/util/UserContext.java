package com.czb.hn.util;

import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * User context utility class
 * Provides unified access to current user information
 */
@Slf4j
public class UserContext {

    private static final String CURRENT_USER_KEY = "CURRENT_USER";

    /**
     * Get current user from ThreadLocal context
     * 
     * @return current user or null if not found
     */
    public static LoginUser getCurrentUser() {
        try {
            return LoginUserContextHolder.getUser();
        } catch (Exception e) {
            log.debug("Could not get user from ThreadLocal context: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Get current user ID
     * 
     * @return current user ID or null if not found
     */
    public static String getCurrentUserId() {
        LoginUser user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }

    /**
     * Get current user's primary group ID (enterprise ID)
     * 
     * @return primary group ID or null if not found
     */
    public static String getCurrentEnterpriseId() {
        LoginUser user = getCurrentUser();
        return user != null ? user.getPrimaryGroupId() : null;
    }


    public static String getCurrentEnterpriseCode() {
        LoginUser user = getCurrentUser();
        return user != null ? user.getPrimaryGroupCode() : null;
    }

    /**
     * Get current user from HTTP session
     * 
     * @param request HTTP request
     * @return current user or null if not found
     */
    public static LoginUser getCurrentUserFromSession(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                return (LoginUser) session.getAttribute(CURRENT_USER_KEY);
            }
        } catch (Exception e) {
            log.debug("Could not get user from session: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Get current user ID from HTTP session
     * 
     * @param request HTTP request
     * @return user ID or null if not found
     */
    public static String getCurrentUserIdFromSession(HttpServletRequest request) {
        LoginUser user = getCurrentUserFromSession(request);
        return user != null ? user.getUserId() : null;
    }

    /**
     * Get current enterprise ID from HTTP session
     * 
     * @param request HTTP request
     * @return enterprise ID or null if not found
     */
    public static String getCurrentEnterpriseIdFromSession(HttpServletRequest request) {
        LoginUser user = getCurrentUserFromSession(request);
        return user != null ? user.getPrimaryGroupId() : null;
    }

    /**
     * Get current user with fallback to session
     * First tries ThreadLocal, then falls back to session
     * 
     * @param request HTTP request (optional, can be null)
     * @return current user or null if not found
     */
    public static LoginUser getCurrentUserWithFallback(HttpServletRequest request) {
        // First try ThreadLocal
        LoginUser user = getCurrentUser();
        if (user != null) {
            return user;
        }

        // Fallback to session if request is provided
        if (request != null) {
            user = getCurrentUserFromSession(request);
            if (user != null) {
                log.debug("Retrieved user from session fallback: {}", user.getUserId());
                return user;
            }
        }

        return null;
    }

    /**
     * Get current enterprise ID with fallback to session
     * 
     * @param request HTTP request (optional, can be null)
     * @return enterprise ID or null if not found
     */
    public static String getCurrentEnterpriseIdWithFallback(HttpServletRequest request) {
        LoginUser user = getCurrentUserWithFallback(request);
        return user != null ? user.getPrimaryGroupId() : null;
    }

    public static String getCurrentEnterpriseCodeWithFallback(HttpServletRequest request) {
        LoginUser user = getCurrentUserWithFallback(request);
        return user != null ? user.getPrimaryGroupCode() : null;
    }

    /**
     * Check if user is authenticated
     * 
     * @return true if user is authenticated, false otherwise
     */
    public static boolean isAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && authentication.isAuthenticated() &&
                    !"anonymousUser".equals(authentication.getName());
        } catch (Exception e) {
            log.debug("Error checking authentication status: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Check if user is authenticated with fallback to session
     * 
     * @param request HTTP request (optional, can be null)
     * @return true if user is authenticated, false otherwise
     */
    public static boolean isAuthenticatedWithFallback(HttpServletRequest request) {
        // First check Spring Security context
        if (isAuthenticated()) {
            return true;
        }

        // Fallback to session check
        if (request != null) {
            LoginUser user = getCurrentUserFromSession(request);
            return user != null && user.getUserId() != null;
        }

        return false;
    }
}
