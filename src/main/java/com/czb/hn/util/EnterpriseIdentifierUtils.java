package com.czb.hn.util;

import java.util.regex.Pattern;

/**
 * Enterprise Identifier Utilities
 * Provides utilities for handling enterprise identification
 * Supports both enterprise ID and unified social credit code
 */
public class EnterpriseIdentifierUtils {

    /**
     * Pattern for Chinese unified social credit code (统一社会信用代码)
     * 18 characters: first digit + 17 alphanumeric characters
     */
    private static final Pattern CREDIT_CODE_PATTERN = Pattern.compile("^[0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}$");

    /**
     * Check if the given identifier is a valid unified social credit code
     * 
     * @param identifier the identifier to check
     * @return true if it's a valid credit code format, false otherwise
     */
    public static boolean isValidCreditCode(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = identifier.trim().toUpperCase();
        return trimmed.length() == 18 && CREDIT_CODE_PATTERN.matcher(trimmed).matches();
    }

    /**
     * Check if the given identifier appears to be an enterprise ID
     * (not a credit code)
     * 
     * @param identifier the identifier to check
     * @return true if it appears to be an enterprise ID, false otherwise
     */
    public static boolean isEnterpriseId(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        
        return !isValidCreditCode(identifier);
    }

    /**
     * Normalize the identifier by trimming and converting to uppercase if it's a credit code
     * 
     * @param identifier the identifier to normalize
     * @return normalized identifier
     */
    public static String normalizeIdentifier(String identifier) {
        if (identifier == null) {
            return null;
        }
        
        String trimmed = identifier.trim();
        if (isValidCreditCode(trimmed)) {
            return trimmed.toUpperCase();
        }
        
        return trimmed;
    }

    /**
     * Get the type of identifier
     * 
     * @param identifier the identifier to check
     * @return "CREDIT_CODE" if it's a credit code, "ENTERPRISE_ID" if it's an enterprise ID, "UNKNOWN" if invalid
     */
    public static String getIdentifierType(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return "UNKNOWN";
        }
        
        if (isValidCreditCode(identifier)) {
            return "CREDIT_CODE";
        } else {
            return "ENTERPRISE_ID";
        }
    }

    /**
     * Validate unified social credit code using check digit algorithm
     * This is a simplified validation - full validation would require the complete algorithm
     * 
     * @param creditCode the credit code to validate
     * @return true if the format is valid, false otherwise
     */
    public static boolean validateCreditCodeChecksum(String creditCode) {
        if (!isValidCreditCode(creditCode)) {
            return false;
        }
        
        // TODO: Implement full checksum validation algorithm if needed
        // For now, just return format validation result
        return true;
    }

    /**
     * Format credit code for display (add spaces for readability)
     * Example: 91110000000000001X -> 91 1100 0000 0000 001X
     * 
     * @param creditCode the credit code to format
     * @return formatted credit code or original string if not a valid credit code
     */
    public static String formatCreditCodeForDisplay(String creditCode) {
        if (!isValidCreditCode(creditCode)) {
            return creditCode;
        }
        
        String normalized = normalizeIdentifier(creditCode);
        return String.format("%s %s %s %s %s",
                normalized.substring(0, 2),   // 前2位
                normalized.substring(2, 6),   // 3-6位
                normalized.substring(6, 10),  // 7-10位
                normalized.substring(10, 14), // 11-14位
                normalized.substring(14, 18)  // 15-18位
        );
    }
}
