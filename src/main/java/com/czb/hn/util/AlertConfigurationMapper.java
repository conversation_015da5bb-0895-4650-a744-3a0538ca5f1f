package com.czb.hn.util;

import com.czb.hn.dto.alert.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertConfigurationSnapshot;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Utility class for mapping between AlertConfiguration entities and DTOs
 * Handles JSON serialization/deserialization for configuration settings
 */
@Component
public class AlertConfigurationMapper {

    private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Convert CreateDto to Entity
     */
    public AlertConfiguration toEntity(AlertConfigurationCreateDto createDto) {
        try {
            AlertConfiguration entity = new AlertConfiguration();
            entity.setName(createDto.name());
            entity.setDescription(createDto.description());
            entity.setPlanId(createDto.planId());
            entity.setEnterpriseId(createDto.enterpriseId());
            entity.setEnabled(createDto.enabled() != null ? createDto.enabled() : true);
            entity.setCreatedBy(createDto.createdBy());

            // Convert DTOs to JSON strings
            entity.setAlertKeywords(objectMapper.writeValueAsString(createDto.alertKeywords()));
            entity.setContentSettings(objectMapper.writeValueAsString(createDto.contentSettings()));
            entity.setThresholdSettings(objectMapper.writeValueAsString(createDto.thresholdSettings()));
            entity.setLevelSettings(objectMapper.writeValueAsString(createDto.levelSettings()));
            entity.setReceptionSettings(objectMapper.writeValueAsString(createDto.receptionSettings()));

            return entity;
        } catch (Exception e) {
            logger.error("Error converting CreateDto to Entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert CreateDto to Entity", e);
        }
    }

    /**
     * Update Entity from UpdateDto
     */
    public void updateEntityFromDto(AlertConfigurationUpdateDto updateDto, AlertConfiguration entity) {
        try {
            if (updateDto.name() != null) {
                entity.setName(updateDto.name());
            }
            if (updateDto.description() != null) {
                entity.setDescription(updateDto.description());
            }
            if (updateDto.planId() != null) {
                entity.setPlanId(updateDto.planId());
            }
            if (updateDto.enabled() != null) {
                entity.setEnabled(updateDto.enabled());
            }
            if (updateDto.updatedBy() != null) {
                entity.setUpdatedBy(updateDto.updatedBy());
            }

            // Update JSON fields if provided
            if (updateDto.alertKeywords() != null) {
                entity.setAlertKeywords(objectMapper.writeValueAsString(updateDto.alertKeywords()));
            }
            if (updateDto.contentSettings() != null) {
                entity.setContentSettings(objectMapper.writeValueAsString(updateDto.contentSettings()));
            }
            if (updateDto.thresholdSettings() != null) {
                entity.setThresholdSettings(objectMapper.writeValueAsString(updateDto.thresholdSettings()));
            }
            if (updateDto.levelSettings() != null) {
                entity.setLevelSettings(objectMapper.writeValueAsString(updateDto.levelSettings()));
            }
            if (updateDto.receptionSettings() != null) {
                entity.setReceptionSettings(objectMapper.writeValueAsString(updateDto.receptionSettings()));
            }

        } catch (Exception e) {
            logger.error("Error updating Entity from UpdateDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update Entity from UpdateDto", e);
        }
    }

    /**
     * Convert Entity to ResponseDto
     */
    public AlertConfigurationResponseDto toResponseDto(AlertConfiguration entity) {
        try {
            return new AlertConfigurationResponseDto(
                    entity.getId(),
                    entity.getName(),
                    entity.getDescription(),
                    entity.getPlanId(),
                    entity.getEnterpriseId(),
                    entity.getEnabled(),
                    parseAlertKeywords(entity.getAlertKeywords()),
                    parseContentSettings(entity.getContentSettings()),
                    parseThresholdSettings(entity.getThresholdSettings()),
                    parseLevelSettings(entity.getLevelSettings()),
                    parseReceptionSettings(entity.getReceptionSettings()),
                    entity.getCreatedAt(),
                    entity.getUpdatedAt(),
                    entity.getCreatedBy(),
                    entity.getUpdatedBy(),
                    entity.getCurrentVersion(),
                    entity.getIsActive(),
                    entity.getLastSnapshotAt()
            );
        } catch (Exception e) {
            logger.error("Error converting Entity to ResponseDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to ResponseDto", e);
        }
    }

    /**
     * Convert Snapshot Entity to SnapshotDto
     */
    public AlertConfigurationSnapshotDto toSnapshotDto(AlertConfigurationSnapshot snapshot) {
        return new AlertConfigurationSnapshotDto(
                snapshot.getId(),
                snapshot.getConfigurationId(),
                snapshot.getVersionNumber(),
                snapshot.getSnapshotData(),
                snapshot.getCreatedAt(),
                snapshot.getCreatedBy(),
                snapshot.getChangeReason(),
                snapshot.getIsActive(),
                snapshot.getOperationType(),
                snapshot.getDataSize(),
                snapshot.getChecksum()
        );
    }

    /**
     * Convert Entity to JSON for snapshot storage
     */
    public String toSnapshotJson(AlertConfiguration entity) {
        try {
            AlertConfigurationResponseDto responseDto = toResponseDto(entity);
            return objectMapper.writeValueAsString(responseDto);
        } catch (Exception e) {
            logger.error("Error converting Entity to snapshot JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to snapshot JSON", e);
        }
    }

    /**
     * Restore Entity from snapshot JSON
     */
    public AlertConfiguration fromSnapshotJson(String snapshotJson) {
        try {
            AlertConfigurationResponseDto responseDto = objectMapper.readValue(snapshotJson, AlertConfigurationResponseDto.class);
            
            AlertConfiguration entity = new AlertConfiguration();
            entity.setId(responseDto.id());
            entity.setName(responseDto.name());
            entity.setDescription(responseDto.description());
            entity.setPlanId(responseDto.planId());
            entity.setEnterpriseId(responseDto.enterpriseId());
            entity.setEnabled(responseDto.enabled());
            entity.setCreatedBy(responseDto.createdBy());
            entity.setUpdatedBy(responseDto.updatedBy());
            entity.setCreatedAt(responseDto.createdAt());
            entity.setUpdatedAt(responseDto.updatedAt());
            entity.setCurrentVersion(responseDto.currentVersion());
            entity.setIsActive(responseDto.isActive());
            entity.setLastSnapshotAt(responseDto.lastSnapshotAt());

            // Convert DTOs back to JSON strings
            entity.setAlertKeywords(objectMapper.writeValueAsString(responseDto.alertKeywords()));
            entity.setContentSettings(objectMapper.writeValueAsString(responseDto.contentSettings()));
            entity.setThresholdSettings(objectMapper.writeValueAsString(responseDto.thresholdSettings()));
            entity.setLevelSettings(objectMapper.writeValueAsString(responseDto.levelSettings()));
            entity.setReceptionSettings(objectMapper.writeValueAsString(responseDto.receptionSettings()));

            return entity;
        } catch (Exception e) {
            logger.error("Error restoring Entity from snapshot JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to restore Entity from snapshot JSON", e);
        }
    }

    /**
     * Copy configuration data from source to target entity
     */
    public void copyConfigurationData(AlertConfiguration source, AlertConfiguration target) {
        target.setName(source.getName());
        target.setDescription(source.getDescription());
        target.setPlanId(source.getPlanId());
        target.setEnabled(source.getEnabled());
        target.setAlertKeywords(source.getAlertKeywords());
        target.setContentSettings(source.getContentSettings());
        target.setThresholdSettings(source.getThresholdSettings());
        target.setLevelSettings(source.getLevelSettings());
        target.setReceptionSettings(source.getReceptionSettings());
    }

    // Helper methods for parsing JSON strings to DTOs
    private com.czb.hn.dto.alert.config.AlertKeywordsDto parseAlertKeywords(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.AlertKeywordsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing alert keywords JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.ContentSettingsDto parseContentSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.ContentSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing content settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.ThresholdSettingsDto parseThresholdSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.ThresholdSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing threshold settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.LevelSettingsDto parseLevelSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.LevelSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing level settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.ReceptionSettingsDto parseReceptionSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.ReceptionSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing reception settings JSON: {}", e.getMessage());
            return null;
        }
    }
}
