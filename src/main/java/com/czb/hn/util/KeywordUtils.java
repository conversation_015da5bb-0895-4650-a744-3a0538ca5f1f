package com.czb.hn.util;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 关键词处理工具类
 * 提供关键词解析、验证、标准化等功能
 * 支持逻辑运算符：+ (AND), | (OR), @@ (转义+)
 */
public class KeywordUtils {

    /**
     * 关键词解析结果
     */
    public static class KeywordParseResult {
        private final MonitorKeywordGroup monitorKeywords;
        private final ExcludeKeywordGroup excludeKeywords;

        public KeywordParseResult(MonitorKeywordGroup monitorKeywords, ExcludeKeywordGroup excludeKeywords) {
            this.monitorKeywords = monitorKeywords;
            this.excludeKeywords = excludeKeywords;
        }

        public MonitorKeywordGroup getMonitorKeywords() {
            return monitorKeywords;
        }

        public ExcludeKeywordGroup getExcludeKeywords() {
            return excludeKeywords;
        }

        @Override
        public String toString() {
            return "KeywordParseResult{" +
                    "monitorKeywords=" + monitorKeywords +
                    ", excludeKeywords=" + excludeKeywords +
                    '}';
        }
    }

    /**
     * 监控关键词组（支持AND和OR逻辑）
     */
    public static class MonitorKeywordGroup {
        private final List<List<String>> andGroups; // AND组合：每个内部List是一个AND组
        private final List<String> orKeywords; // OR关键词列表

        public MonitorKeywordGroup(List<List<String>> andGroups, List<String> orKeywords) {
            this.andGroups = andGroups != null ? andGroups : new ArrayList<>();
            this.orKeywords = orKeywords != null ? orKeywords : new ArrayList<>();
        }

        public List<List<String>> getAndGroups() {
            return andGroups;
        }

        public List<String> getOrKeywords() {
            return orKeywords;
        }

        /**
         * 获取所有关键词的扁平列表（用于高亮等场景）
         */
        public List<String> getAllKeywords() {
            Set<String> allKeywords = new HashSet<>();
            andGroups.forEach(allKeywords::addAll);
            allKeywords.addAll(orKeywords);
            return new ArrayList<>(allKeywords);
        }

        public boolean isEmpty() {
            return andGroups.isEmpty() && orKeywords.isEmpty();
        }

        @Override
        public String toString() {
            return "MonitorKeywordGroup{" +
                    "andGroups=" + andGroups +
                    ", orKeywords=" + orKeywords +
                    '}';
        }
    }

    /**
     * 排除关键词组（OR逻辑）
     */
    public static class ExcludeKeywordGroup {
        private final List<String> keywords; // 排除关键词列表（逻辑关系为OR）

        public ExcludeKeywordGroup(List<String> keywords) {
            this.keywords = keywords != null ? keywords : new ArrayList<>();
        }

        public List<String> getKeywords() {
            return keywords;
        }

        public boolean isEmpty() {
            return keywords.isEmpty();
        }

        @Override
        public String toString() {
            return "ExcludeKeywordGroup{" +
                    "keywords=" + keywords +
                    '}';
        }
    }

    /**
     * 关键词最大长度
     */
    private static final int MAX_KEYWORD_LENGTH = 50;

    /**
     * 关键词最小长度
     */
    private static final int MIN_KEYWORD_LENGTH = 1;

    /**
     * 单个方案最大关键词数量
     */
    private static final int MAX_KEYWORDS_COUNT = 100;

    /**
     * 逻辑运算符
     */
    private static final String AND_OPERATOR = "+";
    private static final String OR_OPERATOR = "|";
    private static final String ESCAPE_PLUS = "@@";

    /**
     * 无效字符正则表达式（只允许中文、英文、数字、空格、连字符、下划线、逻辑运算符）
     */
    private static final Pattern INVALID_CHARS_PATTERN = Pattern.compile("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_+|@]");

    /**
     * 解析带逻辑运算符的关键词字符串
     *
     * @param monitorKeywords 监控关键词字符串（支持+和|运算符）
     * @param excludeKeywords 排除关键词字符串（使用|分割）
     * @return 解析结果对象
     */
    public static KeywordParseResult parseKeywordsWithLogic(String monitorKeywords, String excludeKeywords) {
        MonitorKeywordGroup monitorGroup = parseMonitorKeywords(monitorKeywords);
        ExcludeKeywordGroup excludeGroup = parseExcludeKeywords(excludeKeywords);
        return new KeywordParseResult(monitorGroup, excludeGroup);
    }

    /**
     * 解析监控关键词（支持AND和OR逻辑）
     *
     * @param keywords 监控关键词字符串
     * @return 监控关键词组
     */
    public static MonitorKeywordGroup parseMonitorKeywords(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return new MonitorKeywordGroup(new ArrayList<>(), new ArrayList<>());
        }

        List<List<String>> andGroups = new ArrayList<>();
        List<String> orKeywords = new ArrayList<>();

        // 先按|分割得到OR组
        String[] orParts = keywords.split("\\|");

        for (String orPart : orParts) {
            orPart = orPart.trim();
            if (orPart.isEmpty())
                continue;

            // 检查是否包含+（AND运算符）
            if (orPart.contains(AND_OPERATOR)) {
                // 按+分割得到AND组
                List<String> andGroup = Arrays.stream(orPart.split("\\+"))
                        .map(String::trim)
                        .filter(keyword -> !keyword.isEmpty())
                        .map(KeywordUtils::unescapeKeyword)
                        .filter(KeywordUtils::validateSingleKeyword)
                        .distinct()
                        .collect(Collectors.toList());

                if (!andGroup.isEmpty()) {
                    andGroups.add(andGroup);
                }
            } else {
                // 单个关键词，加入OR列表
                String keyword = unescapeKeyword(orPart);
                if (validateSingleKeyword(keyword)) {
                    orKeywords.add(keyword);
                }
            }
        }

        return new MonitorKeywordGroup(andGroups, orKeywords);
    }

    /**
     * 解析排除关键词（OR逻辑）
     *
     * @param keywords 排除关键词字符串
     * @return 排除关键词组
     */
    public static ExcludeKeywordGroup parseExcludeKeywords(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return new ExcludeKeywordGroup(new ArrayList<>());
        }

        List<String> excludeList = Arrays.stream(keywords.split("\\|"))
                .map(String::trim)
                .filter(keyword -> !keyword.isEmpty())
                .map(KeywordUtils::unescapeKeyword)
                .filter(KeywordUtils::validateSingleKeyword)
                .distinct()
                .collect(Collectors.toList());

        return new ExcludeKeywordGroup(excludeList);
    }

    /**
     * 处理转义字符，将@@替换为+
     *
     * @param keyword 原始关键词
     * @return 处理转义后的关键词
     */
    public static String unescapeKeyword(String keyword) {
        if (keyword == null) {
            return null;
        }
        return keyword.replace(ESCAPE_PLUS, "+");
    }

    /**
     * 验证监控关键词格式（支持逻辑运算符）
     *
     * @param keywords 待验证的监控关键词字符串
     * @return 验证结果
     */
    public static boolean validateMonitorKeywords(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return false;
        }

        try {
            MonitorKeywordGroup group = parseMonitorKeywords(keywords);

            // 检查总关键词数量
            int totalCount = group.getAllKeywords().size();
            if (totalCount > MAX_KEYWORDS_COUNT) {
                return false;
            }

            // 检查是否为空
            if (group.isEmpty()) {
                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证排除关键词格式
     *
     * @param keywords 待验证的排除关键词字符串
     * @return 验证结果
     */
    public static boolean validateExcludeKeywords(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return true; // 排除关键词可以为空
        }

        try {
            ExcludeKeywordGroup group = parseExcludeKeywords(keywords);

            // 检查关键词数量
            if (group.getKeywords().size() > MAX_KEYWORDS_COUNT) {
                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证单个关键词
     * 
     * @param keyword 待验证的关键词
     * @return 验证结果
     */
    public static boolean validateSingleKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return false;
        }

        String trimmedKeyword = keyword.trim();

        // 检查长度
        if (trimmedKeyword.length() < MIN_KEYWORD_LENGTH ||
                trimmedKeyword.length() > MAX_KEYWORD_LENGTH) {
            return false;
        }

        // 检查是否包含无效字符
        if (containsInvalidCharacters(trimmedKeyword)) {
            return false;
        }

        return true;
    }

    /**
     * 标准化关键词列表
     * 
     * @param keywords 原始关键词列表
     * @return 标准化后的关键词列表
     */
    public static List<String> normalizeKeywords(List<String> keywords) {
        if (keywords == null || keywords.isEmpty()) {
            return new ArrayList<>();
        }

        return keywords.stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(keyword -> !keyword.isEmpty())
                .filter(KeywordUtils::validateSingleKeyword)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检查关键词是否包含无效字符
     *
     * @param keyword 待检查的关键词
     * @return 是否包含无效字符
     */
    public static boolean containsInvalidCharacters(String keyword) {
        if (keyword == null) {
            return true;
        }

        // 检查其他无效字符（允许中文、英文、数字、空格、连字符、下划线、@符号）
        Pattern singleKeywordPattern = Pattern.compile("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_@]");
        return singleKeywordPattern.matcher(keyword).find();
    }

    /**
     * 获取监控关键词验证错误信息（支持逻辑运算符）
     *
     * @param keywords 待验证的监控关键词字符串
     * @return 错误信息，如果验证通过则返回null
     */
    public static String getMonitorKeywordsValidationErrorMessage(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return "监控关键词不能为空";
        }

        try {
            MonitorKeywordGroup group = parseMonitorKeywords(keywords);

            if (group.isEmpty()) {
                return "监控关键词不能为空";
            }

            List<String> allKeywords = group.getAllKeywords();
            if (allKeywords.size() > MAX_KEYWORDS_COUNT) {
                return "关键词数量不能超过" + MAX_KEYWORDS_COUNT + "个";
            }

            // 验证每个关键词
            for (String keyword : allKeywords) {
                String error = validateSingleKeywordWithMessage(keyword);
                if (error != null) {
                    return error;
                }
            }

            return null;
        } catch (Exception e) {
            return "关键词格式错误：" + e.getMessage();
        }
    }

    /**
     * 获取排除关键词验证错误信息
     *
     * @param keywords 待验证的排除关键词字符串
     * @return 错误信息，如果验证通过则返回null
     */
    public static String getExcludeKeywordsValidationErrorMessage(String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return null; // 排除关键词可以为空
        }

        try {
            ExcludeKeywordGroup group = parseExcludeKeywords(keywords);

            if (group.getKeywords().size() > MAX_KEYWORDS_COUNT) {
                return "排除关键词数量不能超过" + MAX_KEYWORDS_COUNT + "个";
            }

            // 验证每个关键词
            for (String keyword : group.getKeywords()) {
                String error = validateSingleKeywordWithMessage(keyword);
                if (error != null) {
                    return error;
                }
            }

            return null;
        } catch (Exception e) {
            return "排除关键词格式错误：" + e.getMessage();
        }
    }

    /**
     * 验证单个关键词并返回错误信息
     *
     * @param keyword 待验证的关键词
     * @return 错误信息，如果验证通过则返回null
     */
    private static String validateSingleKeywordWithMessage(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return "关键词不能为空";
        }

        String trimmedKeyword = keyword.trim();

        if (trimmedKeyword.length() < MIN_KEYWORD_LENGTH) {
            return "关键词长度不能少于" + MIN_KEYWORD_LENGTH + "个字符";
        }

        if (trimmedKeyword.length() > MAX_KEYWORD_LENGTH) {
            return "关键词'" + trimmedKeyword + "'长度不能超过" + MAX_KEYWORD_LENGTH + "个字符";
        }

        if (containsInvalidCharacters(trimmedKeyword)) {
            return "关键词'" + trimmedKeyword + "'包含无效字符，只允许中文、英文、数字、空格、连字符、下划线和转义符@@";
        }

        return null;
    }

}
