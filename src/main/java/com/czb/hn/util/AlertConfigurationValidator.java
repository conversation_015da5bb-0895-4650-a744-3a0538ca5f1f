package com.czb.hn.util;

import com.czb.hn.dto.alert.config.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Utility class for validating alert configuration data
 * Implements comprehensive validation and business rules enforcement
 */
@Component
public class AlertConfigurationValidator {

    private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationValidator.class);

    // Validation patterns
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern TIME_PATTERN = Pattern.compile("^([01]?[0-9]|2[0-3]):[0-5][0-9]$|^24:00$");

    /**
     * Validate complete alert configuration
     */
    public ValidationResult validateConfiguration(
            AlertKeywordsDto alertKeywords,
            ContentSettingsDto contentSettings,
            ThresholdSettingsDto thresholdSettings,
            LevelSettingsDto levelSettings,
            ReceptionSettingsDto receptionSettings) {

        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // Validate each section
        validateAlertKeywords(alertKeywords, errors, warnings);
        validateContentSettings(contentSettings, errors, warnings);
        validateThresholdSettings(thresholdSettings, errors, warnings);
        validateLevelSettings(levelSettings, errors, warnings);
        validateReceptionSettings(receptionSettings, errors, warnings);

        // Cross-validation between sections
        validateCrossReferences(alertKeywords, contentSettings, thresholdSettings, levelSettings, receptionSettings,
                errors, warnings);

        return new ValidationResult(errors.isEmpty(), errors, warnings);
    }

    /**
     * Validate alert keywords configuration
     */
    public void validateAlertKeywords(AlertKeywordsDto alertKeywords, List<String> errors, List<String> warnings) {
        if (alertKeywords == null) {
            errors.add("Alert keywords configuration is required");
            return;
        }

        if (alertKeywords.keywords() == null || alertKeywords.keywords().isEmpty()) {
            errors.add("At least one alert keyword is required");
            return;
        }

        // Validate individual keywords
        for (String keyword : alertKeywords.keywords()) {
            if (keyword == null || keyword.trim().isEmpty()) {
                errors.add("Keywords cannot be empty");
                continue;
            }

            if (keyword.length() > 100) {
                errors.add("Keyword '" + keyword + "' exceeds maximum length of 100 characters");
            }

            if (keyword.length() < 2) {
                warnings.add("Keyword '" + keyword + "' is very short and may cause too many matches");
            }

            // Check for special characters that might cause issues
            if (keyword.matches(".*[<>\"'&].*")) {
                warnings.add("Keyword '" + keyword + "' contains special characters that may affect matching");
            }
        }

        // Check for duplicates
        long uniqueCount = alertKeywords.keywords().stream().distinct().count();
        if (uniqueCount != alertKeywords.keywords().size()) {
            warnings.add("Duplicate keywords detected and will be ignored");
        }

        // Warn about too many keywords
        if (alertKeywords.keywords().size() > 20) {
            warnings.add("Large number of keywords (" + alertKeywords.keywords().size() + ") may impact performance");
        }
    }

    /**
     * Validate content settings configuration
     */
    public void validateContentSettings(ContentSettingsDto contentSettings, List<String> errors,
            List<String> warnings) {
        if (contentSettings == null) {
            errors.add("Content settings configuration is required");
            return;
        }

        // Validate source types
        if (contentSettings.sourceTypes() != null && !contentSettings.sourceTypes().isEmpty()) {
            List<String> validSourceTypes = List.of("ALL", "WEBSITE", "FORUM", "WEIBO", "WECHAT", "CLIENT", "VIDEO",
                    "DIGITAL_PAPER");
            for (String sourceType : contentSettings.sourceTypes()) {
                if (!validSourceTypes.contains(sourceType)) {
                    errors.add("Invalid source type: " + sourceType);
                }
            }
        }

        // Validate content types
        if (contentSettings.contentTypes() != null && !contentSettings.contentTypes().isEmpty()) {
            List<String> validContentTypes = List.of("1", "2", "3", "4");
            for (String contentType : contentSettings.contentTypes()) {
                if (!validContentTypes.contains(contentType)) {
                    errors.add("Invalid content type: " + contentType);
                }
            }
        }

        // Business rule: Check for overly restrictive settings
        if (isContentSettingsTooRestrictive(contentSettings)) {
            warnings.add("Content settings appear to be very restrictive and may result in few or no matches");
        }
    }

    /**
     * Validate threshold settings configuration
     */
    public void validateThresholdSettings(ThresholdSettingsDto thresholdSettings, List<String> errors,
            List<String> warnings) {
        if (thresholdSettings == null) {
            errors.add("Threshold settings configuration is required");
            return;
        }

        // Validate that at least one condition is enabled
        boolean hasEnabledCondition = false;

        if (thresholdSettings.interactionCount() != null
                && Boolean.TRUE.equals(thresholdSettings.interactionCount().enabled())) {
            hasEnabledCondition = true;
            validateThresholdCondition("Interaction count", thresholdSettings.interactionCount(), errors, warnings);
        }

        if (thresholdSettings.fansCount() != null && Boolean.TRUE.equals(thresholdSettings.fansCount().enabled())) {
            hasEnabledCondition = true;
            validateThresholdCondition("Fans count", thresholdSettings.fansCount(), errors, warnings);
        }

        if (thresholdSettings.readCount() != null && Boolean.TRUE.equals(thresholdSettings.readCount().enabled())) {
            hasEnabledCondition = true;
            validateThresholdCondition("Read count", thresholdSettings.readCount(), errors, warnings);
        }

        if (thresholdSettings.similarArticleCount() != null
                && Boolean.TRUE.equals(thresholdSettings.similarArticleCount().enabled())) {
            hasEnabledCondition = true;
            validateThresholdCondition("Similar article count", thresholdSettings.similarArticleCount(), errors,
                    warnings);
        }

        if (thresholdSettings.keywordFrequency() != null && !thresholdSettings.keywordFrequency().isEmpty()) {
            hasEnabledCondition = true;
            validateKeywordFrequency(thresholdSettings.keywordFrequency(), errors, warnings);
        }

        if (!hasEnabledCondition) {
            errors.add("At least one threshold condition must be enabled");
        }

        // Business rule: Warn about very high thresholds
        validateThresholdReasonableness(thresholdSettings, warnings);
    }

    /**
     * Validate level settings configuration
     */
    public void validateLevelSettings(LevelSettingsDto levelSettings, List<String> errors, List<String> warnings) {
        if (levelSettings == null) {
            errors.add("Level settings configuration is required");
            return;
        }

        // Validate threshold ranges
        if (levelSettings.interactionThresholds() != null) {
            validateLevelThresholds("Interaction", levelSettings.interactionThresholds(), errors, warnings);
        }
        if (levelSettings.fansThresholds() != null) {
            validateLevelThresholds("Fans", levelSettings.fansThresholds(), errors, warnings);
        }
        if (levelSettings.readThresholds() != null) {
            validateLevelThresholds("Read", levelSettings.readThresholds(), errors, warnings);
        }
        if (levelSettings.similarArticleThresholds() != null) {
            validateLevelThresholds("Similar article", levelSettings.similarArticleThresholds(), errors, warnings);
        }
    }

    /**
     * Validate reception settings configuration
     */
    public void validateReceptionSettings(ReceptionSettingsDto receptionSettings, List<String> errors,
            List<String> warnings) {
        if (receptionSettings == null) {
            errors.add("Reception settings configuration is required");
            return;
        }

        // Validate alert interval
        if (receptionSettings.alertInterval() != null && receptionSettings.alertInterval() < 30) {
            errors.add("Alert interval must be at least 30 minutes");
        }

        // Validate time period
        if (receptionSettings.receptionPeriod() != null) {
            validateTimePeriod(receptionSettings.receptionPeriod(), errors, warnings);
        }

        // Validate reception methods
        if (receptionSettings.receptionMethods() != null) {
            validateReceptionMethods(receptionSettings.receptionMethods(), errors, warnings);
        }

        // Business rule: Warn about very frequent alerts
        if (receptionSettings.alertInterval() != null && receptionSettings.alertInterval() < 60) {
            warnings.add("Alert interval is less than 1 hour, which may result in frequent notifications");
        }
    }

    /**
     * Cross-validation between different configuration sections
     */
    private void validateCrossReferences(
            AlertKeywordsDto alertKeywords,
            ContentSettingsDto contentSettings,
            ThresholdSettingsDto thresholdSettings,
            LevelSettingsDto levelSettings,
            ReceptionSettingsDto receptionSettings,
            List<String> errors,
            List<String> warnings) {

        // Check if keyword frequency thresholds reference existing alert keywords
        if (thresholdSettings.keywordFrequency() != null && alertKeywords.keywords() != null) {
            for (var keywordFreq : thresholdSettings.keywordFrequency()) {
                if (!alertKeywords.keywords().contains(keywordFreq.keyword())) {
                    warnings.add("Keyword frequency threshold references keyword '" + keywordFreq.keyword() +
                            "' which is not in the alert keywords list");
                }
            }
        }

        // Validate consistency between threshold and level settings
        validateThresholdLevelConsistency(thresholdSettings, levelSettings, warnings);
    }

    // Helper validation methods
    private void validateThresholdCondition(String name, ThresholdSettingsDto.ThresholdConditionDto condition,
            List<String> errors, List<String> warnings) {
        if (condition.threshold() == null || condition.threshold() < 0) {
            errors.add(name + " threshold must be non-negative");
        }
        if (condition.threshold() != null && condition.threshold() > 1000000) {
            warnings.add(name + " threshold is very high (" + condition.threshold() + ") and may never trigger");
        }
    }

    private void validateKeywordFrequency(List<ThresholdSettingsDto.KeywordFrequencyDto> keywordFrequencies,
            List<String> errors, List<String> warnings) {
        if (keywordFrequencies.size() > 3) {
            errors.add("Maximum 3 keyword frequency conditions are allowed");
        }

        for (var keywordFreq : keywordFrequencies) {
            if (keywordFreq.frequency() > 50) {
                warnings.add("Keyword frequency for '" + keywordFreq.keyword() +
                        "' is very high (" + keywordFreq.frequency() + ") and may rarely trigger");
            }
        }
    }

    private void validateLevelThresholds(String type, LevelSettingsDto.LevelThresholdsDto thresholds,
            List<String> errors, List<String> warnings) {
        // Validate that thresholds are in ascending order
        if (thresholds.general() != null && thresholds.moderate() != null) {
            if (thresholds.general().max() != null && thresholds.moderate().min() != null) {
                if (thresholds.general().max() >= thresholds.moderate().min()) {
                    errors.add(type + " thresholds: general max must be less than moderate min");
                }
            }
        }
    }

    private void validateTimePeriod(ReceptionSettingsDto.TimePeriodDto timePeriod, List<String> errors,
            List<String> warnings) {
        if (timePeriod.start() != null && !TIME_PATTERN.matcher(timePeriod.start()).matches()) {
            errors.add("Invalid start time format: " + timePeriod.start());
        }
        if (timePeriod.end() != null && !TIME_PATTERN.matcher(timePeriod.end()).matches()) {
            errors.add("Invalid end time format: " + timePeriod.end());
        }
    }

    private void validateReceptionMethods(ReceptionSettingsDto.ReceptionMethodsDto methods, List<String> errors,
            List<String> warnings) {
        boolean hasEnabledMethod = false;

        if (methods.email() != null && Boolean.TRUE.equals(methods.email().enabled())) {
            hasEnabledMethod = true;
            validateEmailConfig(methods.email(), errors, warnings);
        }

        if (methods.sms() != null && Boolean.TRUE.equals(methods.sms().enabled())) {
            hasEnabledMethod = true;
            validateSmsConfig(methods.sms(), errors, warnings);
        }

        if (!hasEnabledMethod) {
            errors.add("At least one reception method (email or SMS) must be enabled");
        }
    }

    private void validateEmailConfig(ReceptionSettingsDto.EmailConfigDto emailConfig, List<String> errors,
            List<String> warnings) {
        if (emailConfig.recipients() == null || emailConfig.recipients().isEmpty()) {
            errors.add("Email recipients cannot be empty when email is enabled");
            return;
        }

        for (var recipient : emailConfig.recipients()) {
            if (!EMAIL_PATTERN.matcher(recipient.email()).matches()) {
                errors.add("Invalid email address: " + recipient.email());
            }
        }
    }

    private void validateSmsConfig(ReceptionSettingsDto.SmsConfigDto smsConfig, List<String> errors,
            List<String> warnings) {
        if (smsConfig.recipients() == null || smsConfig.recipients().isEmpty()) {
            errors.add("SMS recipients cannot be empty when SMS is enabled");
            return;
        }

        for (var recipient : smsConfig.recipients()) {
            if (!PHONE_PATTERN.matcher(recipient.phone()).matches()) {
                errors.add("Invalid phone number: " + recipient.phone());
            }
        }
    }

    private boolean isContentSettingsTooRestrictive(ContentSettingsDto contentSettings) {
        int restrictionCount = 0;

        if (contentSettings.sourceTypes() != null && contentSettings.sourceTypes().size() == 1 &&
                !contentSettings.sourceTypes().contains("ALL")) {
            restrictionCount++;
        }

        if (contentSettings.contentTypes() != null && contentSettings.contentTypes().size() == 1 &&
                !contentSettings.contentTypes().contains("ALL")) {
            restrictionCount++;
        }

        if (!"ALL".equals(contentSettings.sensitivityType())) {
            restrictionCount++;
        }

        return restrictionCount >= 3;
    }

    private void validateThresholdReasonableness(ThresholdSettingsDto thresholdSettings, List<String> warnings) {
        // Add warnings for unreasonably high thresholds that may never trigger
        if (thresholdSettings.fansCount() != null && Boolean.TRUE.equals(thresholdSettings.fansCount().enabled()) &&
                thresholdSettings.fansCount().threshold() != null
                && thresholdSettings.fansCount().threshold() > 100000) {
            warnings.add("Fans count threshold is very high and may rarely trigger alerts");
        }
    }

    private void validateThresholdLevelConsistency(ThresholdSettingsDto thresholdSettings,
            LevelSettingsDto levelSettings, List<String> warnings) {
        // Check if threshold settings are consistent with level settings
        // This is a simplified check - could be expanded based on business requirements
        if (thresholdSettings.interactionCount() != null && levelSettings.interactionThresholds() != null) {
            // Add consistency checks here if needed
        }
    }

    /**
     * Validation result container
     */
    public record ValidationResult(
            boolean isValid,
            List<String> errors,
            List<String> warnings) {
    }
}
