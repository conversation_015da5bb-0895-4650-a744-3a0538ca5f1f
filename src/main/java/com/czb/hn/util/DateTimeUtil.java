package com.czb.hn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;

/**
 * 时间处理工具类
 * 统一管理系统中所有时间格式的解析和转换
 * 
 * 系统标准时间格式：yyyy-MM-dd HH:mm:ss
 * 支持解析的输入格式：
 * 1. yyyy-MM-dd HH:mm:ss (标准格式)
 * 2. yyyy-MM-ddTHH:mm:ss (ISO 8601格式)
 * 3. yyyy-MM-ddTHH:mm:ss.SSS (ISO 8601带毫秒)
 * 4. 其他ISO标准格式
 */
public class DateTimeUtil {

    private static final Logger logger = LoggerFactory.getLogger(DateTimeUtil.class);

    /**
     * 系统标准时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String STANDARD_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 标准时间格式化器
     */
    public static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern(STANDARD_DATETIME_PATTERN);

    /**
     * ISO 8601时间格式化器（不带毫秒）
     */
    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    /**
     * ISO 8601时间格式化器（带毫秒）
     */
    private static final DateTimeFormatter ISO_WITH_MILLIS_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");

    /**
     * 解析时间字符串，支持多种输入格式，统一返回LocalDateTime对象
     * 
     * @param timeStr 时间字符串
     * @return 解析后的LocalDateTime，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String timeStr) {
        return parseDateTime(timeStr, null);
    }

    /**
     * 解析时间字符串，支持多种输入格式，统一返回LocalDateTime对象
     * 
     * @param timeStr     时间字符串
     * @param contextInfo 上下文信息（用于日志记录，如contentId等）
     * @return 解析后的LocalDateTime，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String timeStr, String contextInfo) {
        if (timeStr == null || timeStr.isBlank()) {
            return null;
        }

        // 去除首尾空格
        timeStr = timeStr.trim();

        try {
            // 1. 首先尝试标准格式 yyyy-MM-dd HH:mm:ss
            return LocalDateTime.parse(timeStr, STANDARD_FORMATTER);
        } catch (DateTimeParseException e1) {
            try {
                // 2. 尝试ISO 8601格式 yyyy-MM-ddTHH:mm:ss
                return LocalDateTime.parse(timeStr, ISO_FORMATTER);
            } catch (DateTimeParseException e2) {
                try {
                    // 3. 尝试ISO 8601格式带毫秒 yyyy-MM-ddTHH:mm:ss.SSS
                    // 解析后截断毫秒部分，保持与系统标准格式一致
                    LocalDateTime parsed = LocalDateTime.parse(timeStr, ISO_WITH_MILLIS_FORMATTER);
                    return parsed.truncatedTo(ChronoUnit.SECONDS);
                } catch (DateTimeParseException e3) {
                    try {
                        // 4. 尝试使用默认的ISO格式解析器（支持更多ISO变体）
                        return LocalDateTime.parse(timeStr);
                    } catch (DateTimeParseException e4) {
                        String logMessage = contextInfo != null
                                ? "Failed to parse datetime '{}' for context '{}': {}"
                                : "Failed to parse datetime '{}': {}";

                        if (contextInfo != null) {
                            logger.warn(logMessage, timeStr, contextInfo, e4.getMessage());
                        } else {
                            logger.warn(logMessage, timeStr, e4.getMessage());
                        }
                        return null;
                    }
                }
            }
        }
    }

    /**
     * 将LocalDateTime格式化为系统标准格式字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的时间字符串，格式为 yyyy-MM-dd HH:mm:ss
     */
    public static String formatToStandardString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(STANDARD_FORMATTER);
    }

    /**
     * 获取当前时间的标准格式字符串
     * 
     * @return 当前时间的标准格式字符串
     */
    public static String getCurrentTimeAsStandardString() {
        return formatToStandardString(LocalDateTime.now());
    }

    /**
     * 验证时间字符串是否符合标准格式
     * 
     * @param timeStr 时间字符串
     * @return true如果符合标准格式，false否则
     */
    public static boolean isStandardFormat(String timeStr) {
        if (timeStr == null || timeStr.isBlank()) {
            return false;
        }

        try {
            LocalDateTime.parse(timeStr.trim(), STANDARD_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 将任意支持的时间格式转换为标准格式字符串
     * 
     * @param timeStr 输入的时间字符串
     * @return 标准格式的时间字符串，解析失败返回null
     */
    public static String convertToStandardFormat(String timeStr) {
        LocalDateTime dateTime = parseDateTime(timeStr);
        return formatToStandardString(dateTime);
    }

    /**
     * 将任意支持的时间格式转换为标准格式字符串
     * 
     * @param timeStr     输入的时间字符串
     * @param contextInfo 上下文信息（用于日志记录）
     * @return 标准格式的时间字符串，解析失败返回null
     */
    public static String convertToStandardFormat(String timeStr, String contextInfo) {
        LocalDateTime dateTime = parseDateTime(timeStr, contextInfo);
        return formatToStandardString(dateTime);
    }

    /**
     * 检查两个时间字符串是否表示同一时刻
     * 支持不同格式的时间字符串比较
     * 
     * @param timeStr1 第一个时间字符串
     * @param timeStr2 第二个时间字符串
     * @return true如果表示同一时刻，false否则
     */
    public static boolean isSameTime(String timeStr1, String timeStr2) {
        LocalDateTime time1 = parseDateTime(timeStr1);
        LocalDateTime time2 = parseDateTime(timeStr2);

        if (time1 == null || time2 == null) {
            return false;
        }

        return time1.equals(time2);
    }

    /**
     * 私有构造函数，防止实例化
     */
    private DateTimeUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}
