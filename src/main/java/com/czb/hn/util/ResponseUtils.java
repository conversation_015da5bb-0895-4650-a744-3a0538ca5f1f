package com.czb.hn.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ResponseUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static void writeHttpResponseJson(HttpServletResponse response, Object data) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(OBJECT_MAPPER.writeValueAsString(data));
    }
} 