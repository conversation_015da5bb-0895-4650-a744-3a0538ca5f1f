package com.czb.hn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 令牌加密工具类
 * 使用AES-GCM算法对令牌进行加密和解密
 * 提供高安全性的令牌存储保护
 */
@Component
public class TokenEncryptionUtil {

    private static final Logger logger = LoggerFactory.getLogger(TokenEncryptionUtil.class);

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // 96 bits
    private static final int GCM_TAG_LENGTH = 16; // 128 bits

    @Value("${sina.token.encryption.key:SecuRadar2024TokenEncryptionKey32}")
    private String encryptionKey;

    /**
     * 加密令牌
     * 
     * @param plainText 明文令牌
     * @return 加密后的Base64编码字符串
     */
    public String encrypt(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return null;
        }

        try {
            // 生成密钥
            SecretKey secretKey = getSecretKey();

            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom.getInstanceStrong().nextBytes(iv);

            // 初始化加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);

            // 执行加密
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 将IV和加密数据合并
            byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.length);

            // 返回Base64编码的结果
            return Base64.getEncoder().encodeToString(encryptedWithIv);

        } catch (Exception e) {
            logger.error("Failed to encrypt token: {}", e.getMessage(), e);
            throw new RuntimeException("Token encryption failed", e);
        }
    }

    /**
     * 解密令牌
     * 
     * @param encryptedText 加密的Base64编码字符串
     * @return 解密后的明文令牌
     */
    public String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return null;
        }

        try {
            // 生成密钥
            SecretKey secretKey = getSecretKey();

            // 解码Base64
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);

            // 提取IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);

            // 初始化解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmParameterSpec);

            // 执行解密
            byte[] decryptedData = cipher.doFinal(encryptedData);

            return new String(decryptedData, StandardCharsets.UTF_8);

        } catch (Exception e) {
            logger.error("Failed to decrypt token: {}", e.getMessage(), e);
            throw new RuntimeException("Token decryption failed", e);
        }
    }

    /**
     * 生成密钥
     * 
     * @return 密钥对象
     */
    private SecretKey getSecretKey() {
        try {
            // 确保密钥长度为32字节（256位）
            String key = encryptionKey;
            if (key.length() < 32) {
                // 如果密钥长度不足，用0填充
                key = String.format("%-32s", key).replace(' ', '0');
            } else if (key.length() > 32) {
                // 如果密钥长度超过，截取前32字节
                key = key.substring(0, 32);
            }

            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            return new SecretKeySpec(keyBytes, ALGORITHM);

        } catch (Exception e) {
            logger.error("Failed to generate secret key: {}", e.getMessage(), e);
            throw new RuntimeException("Secret key generation failed", e);
        }
    }

    /**
     * 生成随机加密密钥（用于初始化配置）
     * 
     * @return Base64编码的随机密钥
     */
    public static String generateRandomKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(256); // 256位密钥
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate random key", e);
        }
    }

    /**
     * 验证加密和解密功能
     * 
     * @param testText 测试文本
     * @return 是否验证成功
     */
    public boolean validateEncryption(String testText) {
        try {
            String encrypted = encrypt(testText);
            String decrypted = decrypt(encrypted);
            return testText.equals(decrypted);
        } catch (Exception e) {
            logger.error("Encryption validation failed: {}", e.getMessage(), e);
            return false;
        }
    }
}
