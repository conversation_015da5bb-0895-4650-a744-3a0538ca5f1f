package com.czb.hn.util;

import com.czb.hn.dto.briefing.BriefingConfigurationCreateDto;
import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.BriefingConfigurationUpdateDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.BriefingConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BriefingConfigurationMapper {

    private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Convert CreateDTO to Entity
     */
    public BriefingConfiguration toEntity(BriefingConfigurationCreateDto createDto){
        try {
            BriefingConfiguration entity = new BriefingConfiguration();
            entity.setName(createDto.name());
            entity.setPlanId(createDto.planId());
            entity.setIsActive(createDto.isActive());
            entity.setContentSettings(objectMapper.writeValueAsString(createDto.contentSettings()));
            entity.setThresholdSettings(objectMapper.writeValueAsString(createDto.thresholdSettings()));
            entity.setLevelSettings(objectMapper.writeValueAsString(createDto.levelSettings()));
            entity.setDailyBriefingIsActive(createDto.dailyBriefingIsActive());
            entity.setDailyReceptionSettings(objectMapper.writeValueAsString(createDto.dailyReceptionSettings()));
            entity.setWeeklyBriefingIsActive(createDto.weeklyBriefingIsActive());
            entity.setWeeklyReceptionSettings(objectMapper.writeValueAsString(createDto.weeklyReceptionSettings()));
            entity.setMonthlyBriefingIsActive(createDto.monthlyBriefingIsActive());
            entity.setMonthlyReceptionSettings(objectMapper.writeValueAsString(createDto.monthlyReceptionSettings()));

            return entity;
        } catch (Exception e){
            logger.error("Error converting CreateDto to Entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert CreateDto to Entity", e);
        }
    }

    /**
     * Update entity from Dto
     */
    public void updateEntityFromDto(BriefingConfigurationUpdateDto updateDto, BriefingConfiguration entity) {
        try {
            if (updateDto.name() != null){
                entity.setName(updateDto.name());
            }
            if (updateDto.isActive() != null){
                entity.setIsActive(updateDto.isActive());
            }
            if (updateDto.contentSettings() != null) {
                entity.setContentSettings(objectMapper.writeValueAsString(updateDto.contentSettings()));
            }
            if (updateDto.thresholdSettings() != null) {
                entity.setThresholdSettings(objectMapper.writeValueAsString(updateDto.thresholdSettings()));
            }
            if (updateDto.levelSettings() != null) {
                entity.setLevelSettings(objectMapper.writeValueAsString(updateDto.levelSettings()));
            }
            if (updateDto.dailyBriefingIsActive() != null) {
                entity.setDailyBriefingIsActive(updateDto.dailyBriefingIsActive());
            }
            if (updateDto.dailyReceptionSettings() != null) {
                entity.setDailyReceptionSettings(objectMapper.writeValueAsString(updateDto.dailyReceptionSettings()));
            }
            if (updateDto.weeklyBriefingIsActive() != null) {
                entity.setWeeklyBriefingIsActive(updateDto.weeklyBriefingIsActive());
            }
            if (updateDto.weeklyReceptionSettings() != null) {
                entity.setWeeklyReceptionSettings(objectMapper.writeValueAsString(updateDto.weeklyReceptionSettings()));
            }
            if (updateDto.monthlyBriefingIsActive() != null) {
                entity.setMonthlyBriefingIsActive(updateDto.monthlyBriefingIsActive());
            }
            if (updateDto.monthlyReceptionSettings() != null) {
                entity.setMonthlyReceptionSettings(objectMapper.writeValueAsString(updateDto.monthlyReceptionSettings()));
            }
        } catch (Exception e) {
            logger.error("Error updating Entity from UpdateDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update Entity from UpdateDto", e);
        }
    }

    public BriefingConfigurationResponseDto toResponseDto(BriefingConfiguration entity){
        try {
            return new BriefingConfigurationResponseDto(
                    entity.getId(),
                    entity.getName(),
                    entity.getPlanId(),
                    entity.getIsActive(),
                    parseContentSettings(entity.getContentSettings()),
                    parseThresholdSettings(entity.getThresholdSettings()),
                    parseLevelSettings(entity.getLevelSettings()),
                    entity.getDailyBriefingIsActive(),
                    parseReceptionSettings(entity.getDailyReceptionSettings()),
                    entity.getWeeklyBriefingIsActive(),
                    parseReceptionSettings(entity.getWeeklyReceptionSettings()),
                    entity.getMonthlyBriefingIsActive(),
                    parseReceptionSettings(entity.getMonthlyReceptionSettings())
            );
        } catch (Exception e) {
            logger.error("Error converting Entity to ResponseDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to ResponseDto", e);
        }
    }

    private com.czb.hn.dto.alert.config.ContentSettingsDto parseContentSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.ContentSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing content settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.ThresholdSettingsDto parseThresholdSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.ThresholdSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing threshold settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private com.czb.hn.dto.alert.config.LevelSettingsDto parseLevelSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, com.czb.hn.dto.alert.config.LevelSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing level settings JSON: {}", e.getMessage());
            return null;
        }
    }

    private BriefingReceptionSettingsDto parseReceptionSettings(String json) {
        try {
            return json != null ? objectMapper.readValue(json, BriefingReceptionSettingsDto.class) : null;
        } catch (Exception e) {
            logger.warn("Error parsing reception settings JSON: {}", e.getMessage());
            return null;
        }
    }
}
