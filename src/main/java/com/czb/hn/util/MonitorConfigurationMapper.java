package com.czb.hn.util;


import com.czb.hn.dto.monitor.MonitorConfigurationCreateDto;
import com.czb.hn.dto.monitor.MonitorConfigurationResponseDto;
import com.czb.hn.dto.monitor.MonitorConfigurationUpdateDto;
import com.czb.hn.jpa.securadar.entity.MonitorConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MonitorConfigurationMapper {

    private static final Logger logger = LoggerFactory.getLogger(MonitorConfigurationMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    public MonitorConfiguration toEntity(MonitorConfigurationCreateDto createDto) {
        try {
            MonitorConfiguration entity = new MonitorConfiguration();
            entity.setName(createDto.name());
            entity.setPlanId(createDto.planId());
            entity.setTime(createDto.time());
            entity.setStartTime(createDto.startTime());
            entity.setEndTime(createDto.endTime());
            entity.setSortRule(createDto.sortRule());
            entity.setSensitivityType(createDto.sensitivityType());
            entity.setSimilarityDisplayRule(createDto.similarityDisplayRule());
            entity.setMatchMethod(createDto.matchMethod());
            entity.setMediaType(createDto.MediaType());
            entity.setMediaTypeSecond(createDto.MediaTypeSecond());
            entity.setContentType(createDto.ContentType());
            entity.setIsOriginal(createDto.isOriginal());
            entity.setImageTextMode(createDto.imageTextMode());
            entity.setSecondTrades(createDto.SecondTrades());
            entity.setAuthorFollowersCountMin(createDto.authorFollowersCountMin());
            entity.setAuthorFollowersCountMax(createDto.authorFollowersCountMax());
            entity.setMediaLevel(createDto.MediaLevel());

            return entity;
        } catch (Exception e){
            logger.error("Error converting CreateDto to Entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert CreateDto to Entity", e);
        }
    }

    public MonitorConfigurationResponseDto toResponseDto(MonitorConfiguration entity) {
        try {
            return new MonitorConfigurationResponseDto(
                    entity.getId(),
                    entity.getName(),
                    entity.getPlanId(),
                    entity.getTime(),
                    entity.getStartTime(),
                    entity.getEndTime(),
                    entity.getSortRule(),
                    entity.getSensitivityType(),
                    entity.getSimilarityDisplayRule(),
                    entity.getMatchMethod(),
                    entity.getMediaType(),
                    entity.getMediaTypeSecond(),
                    entity.getContentType(),
                    entity.getIsOriginal(),
                    entity.getImageTextMode(),
                    entity.getSecondTrades(),
                    entity.getAuthorFollowersCountMin(),
                    entity.getAuthorFollowersCountMax(),
                    entity.getMediaLevel()
            );
        } catch (Exception e) {
            logger.error("Error converting Entity to ResponseDto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert Entity to ResponseDto", e);
        }
    }

    public void updateEntityFromDto(MonitorConfigurationUpdateDto updateDto, MonitorConfiguration entity) {
        try {
            if (updateDto.name() != null){
                entity.setName(updateDto.name());
            }
            if (updateDto.planId() != null){
                entity.setPlanId(updateDto.planId());
            }
            if (updateDto.time() != null){
                entity.setTime(updateDto.time());
            }
            if (updateDto.startTime() != null){
                entity.setStartTime(updateDto.startTime());
            }
            if (updateDto.endTime() != null){
                entity.setEndTime(updateDto.endTime());
            }
            if (updateDto.sortRule() != null){
                entity.setSortRule(updateDto.sortRule());
            }
            if (updateDto.sensitivityType() != null){
                entity.setSensitivityType(updateDto.sensitivityType());
            }
            if (updateDto.SimilarityDisplayRule() != null){
                entity.setSimilarityDisplayRule(updateDto.SimilarityDisplayRule());
            }
            if (updateDto.MatchMethod() != null){
                entity.setMatchMethod(updateDto.MatchMethod());
            }
            if (updateDto.MediaType() != null){
                entity.setMediaType(updateDto.MediaType());
            }
            if (updateDto.MediaTypeSecond() != null){
                entity.setMediaTypeSecond(updateDto.MediaTypeSecond());
            }
            if (updateDto.ContentType() != null){
                entity.setContentType(updateDto.ContentType());
            }
            if (updateDto.isOriginal() != null){
                entity.setIsOriginal(updateDto.isOriginal());
            }
            if (updateDto.imageTextMode() != null){
                entity.setImageTextMode(updateDto.imageTextMode());
            }
            if (updateDto.SecondTrades() != null){
                entity.setSecondTrades(updateDto.SecondTrades());
            }
            if (updateDto.authorFollowersCountMin() != null){
                entity.setAuthorFollowersCountMin(updateDto.authorFollowersCountMin());
            }
            if (updateDto.authorFollowersCountMax() != null){
                entity.setAuthorFollowersCountMax(updateDto.authorFollowersCountMax());
            }
            if (updateDto.MediaLevel() != null){
                entity.setMediaLevel(updateDto.MediaLevel());
            }
        } catch (Exception e) {
            logger.error("Error updating Entity from Dto: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update Entity from Dto", e);
        }
    }
}
