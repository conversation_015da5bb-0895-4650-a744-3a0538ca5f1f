package com.czb.hn.service.scheduled;

import com.czb.hn.config.ScheduleConfig;
import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.enums.SubscriptionStatus;
import com.czb.hn.jpa.securadar.repository.EnterpriseSubscriptionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Subscription expiration service
 * Handles automatic expiration checking and status updates
 */
@Service
@Slf4j
public class SubscriptionExpirationService {

    @Autowired
    private EnterpriseSubscriptionRepository subscriptionRepository;

    @Autowired
    private ScheduleConfig scheduleConfig;

    /**
     * Check and update expired subscriptions
     * Runs based on configuration (default: every day at 2:00 AM)
     */
    @Scheduled(cron = "#{@scheduleConfig.billing.enabled ? @scheduleConfig.billing.expirationCheckCron : '-'}")
    @Transactional
    @CacheEvict(value = "enterpriseAccess", allEntries = true)
    public void checkAndUpdateExpiredSubscriptions() {
        // 检查是否启用计费定时任务
        if (!scheduleConfig.getBilling().isEnabled()) {
            log.debug("Billing scheduled task is disabled, skipping expiration check");
            return;
        }

        log.info("Starting scheduled check for expired subscriptions");

        try {
            LocalDate today = LocalDate.now();
            List<EnterpriseSubscription> expiredSubscriptions = subscriptionRepository.findExpiredButStillActive();

            int updatedCount = 0;
            for (EnterpriseSubscription subscription : expiredSubscriptions) {
                // 检查是否真的过期了
                if (subscription.getEndDate().isBefore(today)) {
                    subscription.setStatus(SubscriptionStatus.EXPIRED);
                    subscription.setUpdatedAt(LocalDateTime.now());
                    subscriptionRepository.save(subscription);
                    updatedCount++;

                    log.info("Updated subscription {} for enterprise {} to EXPIRED status. End date: {}",
                            subscription.getId(),
                            subscription.getEnterpriseId(),
                            subscription.getEndDate());
                }
            }

            log.info("Scheduled expiration check completed. Updated {} subscriptions to EXPIRED status", updatedCount);

        } catch (Exception e) {
            log.error("Error during scheduled expiration check", e);
        }
    }

    /**
     * Send expiration warnings for subscriptions expiring soon
     * Runs based on configuration (default: every day at 9:00 AM)
     */
    @Scheduled(cron = "#{@scheduleConfig.billing.enabled ? @scheduleConfig.billing.expirationWarningCron : '-'}")
    @Transactional(readOnly = true)
    public void sendExpirationWarnings() {
        // 检查是否启用计费定时任务
        if (!scheduleConfig.getBilling().isEnabled()) {
            log.debug("Billing scheduled task is disabled, skipping expiration warning");
            return;
        }

        log.info("Starting scheduled check for expiring subscriptions");

        try {
            // 检查7天内即将过期的订阅
            LocalDate today = LocalDate.now();
            LocalDate endDate = today.plusDays(scheduleConfig.getBilling().getWarningDays());
            List<EnterpriseSubscription> expiringSoon = subscriptionRepository.findExpiringWithinDays(endDate);

            for (EnterpriseSubscription subscription : expiringSoon) {
                long daysUntilExpiration = subscription.getDaysUntilExpiration();

                if (daysUntilExpiration >= 0 && daysUntilExpiration <= 7) {
                    log.warn("Subscription expiring soon - Enterprise: {}, Days until expiration: {}, End date: {}",
                            subscription.getEnterpriseId(),
                            daysUntilExpiration,
                            subscription.getEndDate());

                    // TODO: 这里可以集成邮件或短信通知服务
                    // sendExpirationNotification(subscription);
                }
            }

            log.info("Expiration warning check completed. Found {} subscriptions expiring within 7 days",
                    expiringSoon.size());

        } catch (Exception e) {
            log.error("Error during expiration warning check", e);
        }
    }

    /**
     * Manual trigger for expiration check (for testing or manual execution)
     */
    @Transactional
    @CacheEvict(value = "enterpriseAccess", allEntries = true)
    public int manualExpirationCheck() {
        log.info("Manual expiration check triggered");

        LocalDate today = LocalDate.now();
        List<EnterpriseSubscription> expiredSubscriptions = subscriptionRepository.findExpiredButStillActive();

        int updatedCount = 0;
        for (EnterpriseSubscription subscription : expiredSubscriptions) {
            if (subscription.getEndDate().isBefore(today)) {
                subscription.setStatus(SubscriptionStatus.EXPIRED);
                subscription.setUpdatedAt(LocalDateTime.now());
                subscriptionRepository.save(subscription);
                updatedCount++;

                log.info("Manually updated subscription {} for enterprise {} to EXPIRED status",
                        subscription.getId(),
                        subscription.getEnterpriseId());
            }
        }

        log.info("Manual expiration check completed. Updated {} subscriptions", updatedCount);
        return updatedCount;
    }

    /**
     * Get subscription expiration statistics
     */
    @Transactional(readOnly = true)
    public ExpirationStatistics getExpirationStatistics() {
        LocalDate today = LocalDate.now();

        // 今天过期的订阅
        List<EnterpriseSubscription> expiringToday = subscriptionRepository.findByEndDateBetween(today, today);

        // 7天内过期的订阅
        LocalDate sevenDaysLater = today.plusDays(7);
        List<EnterpriseSubscription> expiringSoon = subscriptionRepository.findExpiringWithinDays(sevenDaysLater);

        // 已过期但状态仍为ACTIVE的订阅
        List<EnterpriseSubscription> expiredButActive = subscriptionRepository.findExpiredButStillActive();

        return new ExpirationStatistics(
                expiringToday.size(),
                expiringSoon.size(),
                expiredButActive.size());
    }

    /**
     * Statistics record for expiration data
     */
    public record ExpirationStatistics(
            int expiringToday,
            int expiringSoon,
            int expiredButActive) {
    }

    // TODO: 实现通知服务
    // private void sendExpirationNotification(EnterpriseSubscription subscription)
    // {
    // // 发送邮件或短信通知
    // }
}
