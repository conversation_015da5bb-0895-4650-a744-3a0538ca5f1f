package com.czb.hn.service.business;

import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.jpa.securadar.entity.Plan;
import com.czb.hn.jpa.securadar.repository.PlanRepository;
import com.czb.hn.util.KeywordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 基于方案的搜索服务
 * 将方案中的关键词转换为搜索服务所需的格式，并执行搜索
 */
@Service
public class PlanBasedSearchService {

    @Autowired
    private PlanRepository planRepository;

    @Autowired
    private ElasticsearchSearchService searchService;

    /**
     * 根据方案ID执行舆情监控搜索
     *
     * @param planId          方案ID
     * @param mediaTypes      媒体类型列表
     * @param sensitivityType 敏感度类型
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param sortRule        排序规则
     * @param pageSize        页面大小
     * @param pageNum         页码
     * @return 搜索结果列表
     */

    public List<SinaNewsSearchResponseDto> searchByPlan(
            Long planId,
            String startTime,
            String endTime,
            Integer sortRule,
            Integer sensitivityType,
            Boolean similarityDisplayRule,
            List<String> mediaTypes,
            Integer pageSize,
            Integer pageNum) {

        // 获取方案信息
        Plan plan = planRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + planId));

        // 解析监控关键词（支持逻辑运算符）
        KeywordUtils.MonitorKeywordGroup monitorGroup = KeywordUtils.parseMonitorKeywords(plan.getMonitorKeywords());

        // 将逻辑关键词转换为搜索服务需要的格式
        List<String> includeKeywords = monitorGroup.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorGroup.getAndGroups();

        // 解析排除关键词
        List<String> excludeKeywords = null;
        if (plan.getExcludeKeywords() != null && !plan.getExcludeKeywords().trim().isEmpty()) {
            KeywordUtils.ExcludeKeywordGroup excludeGroup = KeywordUtils
                    .parseExcludeKeywords(plan.getExcludeKeywords());
            excludeKeywords = excludeGroup.getKeywords();
        }

        SearchRequestDto requestDto = new SearchRequestDto();
        requestDto.setPlanId(planId);
        requestDto.setStartTime(startTime);
        requestDto.setEndTime(endTime);
        requestDto.setSortRule(sortRule);
        requestDto.setSensitivityType(sensitivityType);
        requestDto.setSimilarityDisplayRule(similarityDisplayRule);
        requestDto.setMediaTypes(mediaTypes);
        requestDto.setMatchMethod(0);
        requestDto.setSimilarityDisplayRule(false);
        // 执行搜索
        return searchService.SinaNewsMonitor(
                requestDto,
                pageSize,
                pageNum);
    }

    /**
     * 根据方案ID和自定义关键词执行搜索
     * 将方案关键词与自定义关键词合并
     *
     * @param planId             方案ID
     * @param additionalKeywords 额外的关键词字符串
     * @param mediaTypes         媒体类型列表
     * @param sensitivityType    敏感度类型
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param sortRule           排序规则
     * @param pageSize           页面大小
     * @param pageNum            页码
     * @return 搜索结果列表
     */
    public List<SinaNewsSearchResponseDto> searchByPlanWithAdditionalKeywords(
            Long planId,
            String additionalKeywords,
            String startTime,
            String endTime,
            Integer sortRule,
            Integer sensitivityType,
            Boolean similarityDisplayRule,
            List<String> mediaTypes,
            Integer pageSize,
            Integer pageNum) {

        SearchRequestDto requestDto = new SearchRequestDto();
        requestDto.setPlanId(planId);
        requestDto.setStartTime(startTime);
        requestDto.setEndTime(endTime);
        requestDto.setSortRule(sortRule);
        requestDto.setSensitivityType(sensitivityType);
        requestDto.setSimilarityDisplayRule(similarityDisplayRule);
        requestDto.setMediaTypes(mediaTypes);
        requestDto.setMatchMethod(0);
        requestDto.setSimilarityDisplayRule(false);
        // 执行搜索
        return searchService.SinaNewsMonitor(
                requestDto,
                pageSize,
                pageNum);
    }


    /**
     * 验证方案关键词是否有效
     *
     * @param planId 方案ID
     * @return 验证结果信息，如果有效返回null
     */
    public String validatePlanKeywords(Long planId) {
        Plan plan = planRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + planId));

        // 验证监控关键词（支持逻辑运算符）
        String monitorKeywordsError = KeywordUtils.getMonitorKeywordsValidationErrorMessage(plan.getMonitorKeywords());
        if (monitorKeywordsError != null) {
            return "Monitor keywords validation failed: " + monitorKeywordsError;
        }

        // 验证排除关键词（如果存在）
        if (plan.getExcludeKeywords() != null && !plan.getExcludeKeywords().trim().isEmpty()) {
            String excludeKeywordsError = KeywordUtils
                    .getExcludeKeywordsValidationErrorMessage(plan.getExcludeKeywords());
            if (excludeKeywordsError != null) {
                return "Exclude keywords validation failed: " + excludeKeywordsError;
            }
        }

        return null; // 验证通过
    }

    /**
     * 获取方案的所有关键词信息
     *
     * @param planId 方案ID
     * @return 关键词信息对象
     */
    public PlanKeywordInfo getPlanKeywordInfo(Long planId) {
        Plan plan = planRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + planId));

        // 解析监控关键词（支持逻辑运算符）
        KeywordUtils.MonitorKeywordGroup monitorGroup = KeywordUtils.parseMonitorKeywords(plan.getMonitorKeywords());
        List<String> monitorKeywords = monitorGroup.getAllKeywords();

        // 解析排除关键词
        List<String> excludeKeywords = null;
        if (plan.getExcludeKeywords() != null && !plan.getExcludeKeywords().trim().isEmpty()) {
            KeywordUtils.ExcludeKeywordGroup excludeGroup = KeywordUtils
                    .parseExcludeKeywords(plan.getExcludeKeywords());
            excludeKeywords = excludeGroup.getKeywords();
        }

        return new PlanKeywordInfo(
                planId,
                plan.getName(),
                monitorKeywords,
                excludeKeywords,
                plan.getMonitorKeywords(),
                plan.getExcludeKeywords());
    }

    /**
     * 方案关键词信息类
     */
    public static class PlanKeywordInfo {
        private final Long planId;
        private final String planName;
        private final List<String> monitorKeywordsList;
        private final List<String> excludeKeywordsList;
        private final String monitorKeywordsString;
        private final String excludeKeywordsString;

        public PlanKeywordInfo(Long planId, String planName,
                List<String> monitorKeywordsList, List<String> excludeKeywordsList,
                String monitorKeywordsString, String excludeKeywordsString) {
            this.planId = planId;
            this.planName = planName;
            this.monitorKeywordsList = monitorKeywordsList;
            this.excludeKeywordsList = excludeKeywordsList;
            this.monitorKeywordsString = monitorKeywordsString;
            this.excludeKeywordsString = excludeKeywordsString;
        }

        public Long getPlanId() {
            return planId;
        }

        public String getPlanName() {
            return planName;
        }

        public List<String> getMonitorKeywordsList() {
            return monitorKeywordsList;
        }

        public List<String> getExcludeKeywordsList() {
            return excludeKeywordsList;
        }

        public String getMonitorKeywordsString() {
            return monitorKeywordsString;
        }

        public String getExcludeKeywordsString() {
            return excludeKeywordsString;
        }

        @Override
        public String toString() {
            return "PlanKeywordInfo{" +
                    "planId=" + planId +
                    ", planName='" + planName + '\'' +
                    ", monitorKeywords=" + monitorKeywordsList +
                    ", excludeKeywords=" + excludeKeywordsList +
                    '}';
        }
    }
}
