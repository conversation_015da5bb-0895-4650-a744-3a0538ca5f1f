package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.config.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.MediaLevel;

import java.util.ArrayList;
import java.util.List;

import com.czb.hn.enums.InformationSensitivityType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Alert Rule Engine
 * Evaluates JSON-based alert rules against Elasticsearch documents
 * Determines warning levels and processes complex rule conditions
 */
@Component
@Slf4j
public class AlertRuleEngine {

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Evaluate alert rule against Elasticsearch document
     * 
     * @param config   Alert configuration with JSON rules
     * @param document Elasticsearch document (SinaNewsDocument)
     * @return true if alert should be triggered
     */
    public boolean evaluateRule(AlertConfiguration config, SinaNewsDocument document) {
        try {
            // Validate input parameters
            if (config == null || document == null) {
                log.warn("Cannot evaluate rule: config or document is null");
                return false;
            }

            // Parse JSON configurations
            AlertKeywordsDto keywords = parseAlertKeywords(config.getAlertKeywords());
            ContentSettingsDto contentSettings = parseContentSettings(config.getContentSettings());
            ThresholdSettingsDto thresholdSettings = parseThresholdSettings(config.getThresholdSettings());

            // Check if any required configuration failed to parse
            if (config.getAlertKeywords() != null && !config.getAlertKeywords().trim().isEmpty() && keywords == null) {
                log.warn("Alert keywords configuration is invalid for config {}", config.getId());
                return false;
            }

            // Evaluate each rule component with null safety
            boolean keywordMatch = evaluateKeywordRules(keywords, document);
            boolean contentMatch = evaluateContentRules(contentSettings, document);
            boolean thresholdMatch = evaluateThresholdRules(thresholdSettings, document);

            // Combine results based on configuration logic (AND operation)
            boolean result = keywordMatch && contentMatch && thresholdMatch;

            if (result) {
                log.debug("Alert rule triggered for document {} with configuration {}",
                        document.getContentId(), config.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("Error evaluating rule for configuration {} against document {}: {}",
                    config != null ? config.getId() : "null",
                    document != null ? document.getContentId() : "null",
                    e.getMessage(), e);
            return false;
        }
    }

    /**
     * Determine warning level based on document content and level settings
     * If levelSettings is null, returns GENERAL as default level
     *
     * @param levelSettings Level configuration settings (can be null)
     * @param document      Elasticsearch document
     * @return Determined warning level
     */
    public AlertResult.WarningLevel determineWarningLevel(
            LevelSettingsDto levelSettings, SinaNewsDocument document) {

        // If no level settings configured, return default level
        if (levelSettings == null) {
            log.debug("No level settings configured, using default GENERAL level for document {}",
                    document.getContentId());
            return AlertResult.WarningLevel.GENERAL;
        }

        try {
            int score = calculateAlertScore(levelSettings, document);

            // Use threshold logic matching LevelSettingsDto terminology
            if (score >= 75) {
                return AlertResult.WarningLevel.SEVERE;
            } else if (score >= 50) {
                return AlertResult.WarningLevel.MODERATE;
            } else {
                return AlertResult.WarningLevel.GENERAL;
            }

        } catch (Exception e) {
            log.error("Error determining warning level for document {}: {}",
                    document.getContentId(), e.getMessage(), e);
            return AlertResult.WarningLevel.GENERAL; // Default to GENERAL on error
        }
    }

    /**
     * Evaluate keyword rules against document content
     * Uses simple OR logic - if ANY keyword matches, the rule triggers
     */
    private boolean evaluateKeywordRules(AlertKeywordsDto keywords, SinaNewsDocument document) {
        // If no keywords configured, return true (no keyword filtering)
        if (keywords == null || keywords.keywords() == null || keywords.keywords().isEmpty()) {
            log.debug("No keywords configured, allowing all content for document {}",
                    document.getContentId());
            return true;
        }

        String content = buildSearchableContent(document);
        log.debug("Evaluating keywords {} against content: {}", keywords.keywords(), content);

        // Simple OR logic: if ANY keyword from the list matches the content, return
        // true
        for (String keyword : keywords.keywords()) {
            if (keyword != null && !keyword.trim().isEmpty()) {
                String trimmedKeyword = keyword.trim();
                if (content.toLowerCase().contains(trimmedKeyword.toLowerCase())) {
                    log.debug("Keyword '{}' matched in content for document {}",
                            trimmedKeyword, document.getContentId());
                    return true;
                }
            }
        }

        log.debug("No keywords matched for document {}", document.getContentId());
        return false;
    }

    /**
     * Evaluate content rules against document properties
     * If contentSettings is null, returns true (no filtering applied)
     */
    private boolean evaluateContentRules(ContentSettingsDto contentSettings, SinaNewsDocument document) {
        // If no content settings configured, allow all content
        if (contentSettings == null) {
            log.debug("No content settings configured, allowing all content for document {}",
                    document.getContentId());
            return true;
        }

        // Check sensitivity type
        if (contentSettings.sensitivityType() != null &&
                !"1".equals(contentSettings.sensitivityType())) {
            // Map document sensitivity to string for comparison
            if (!contentSettings.sensitivityType().equals(document.getSensitivityType())) {
                return false;
            }
        }

        // Check source types
        if (contentSettings.sourceTypes() != null &&
                !contentSettings.sourceTypes().isEmpty() &&
                !contentSettings.sourceTypes().contains("hdlt")) {
            String docSourceType = mapSourceType(document.getSource());
            if (!contentSettings.sourceTypes().contains(docSourceType)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate threshold rules against document metrics
     * If thresholdSettings is null, returns true (no threshold filtering applied)
     */
    private boolean evaluateThresholdRules(ThresholdSettingsDto thresholdSettings, SinaNewsDocument document) {
        // If no threshold settings configured, allow all documents
        if (thresholdSettings == null) {
            log.debug("No threshold settings configured, allowing all documents for document {}",
                    document.getContentId());
            return true;
        }

        List<Boolean> conditionResults = new ArrayList<>();

        // Check interaction count threshold
        if (thresholdSettings.interactionCount() != null && thresholdSettings.interactionCount().enabled()) {
            long totalInteractions = calculateTotalInteractions(document);
            boolean meets = totalInteractions >= thresholdSettings.interactionCount().threshold();
            conditionResults.add(meets);
        }

        // Check fans count threshold (using authorFollowersCount)
        if (thresholdSettings.fansCount() != null && thresholdSettings.fansCount().enabled()) {
            boolean meets = document.getAuthorFollowersCount() != null &&
                    document.getAuthorFollowersCount() >= thresholdSettings.fansCount().threshold();
            conditionResults.add(meets);
        }

        // Check read count threshold (using lookingCount)
        if (thresholdSettings.readCount() != null && thresholdSettings.readCount().enabled()) {
            boolean meets = document.getLookingCount() != null &&
                    document.getLookingCount() >= thresholdSettings.readCount().threshold();
            conditionResults.add(meets);
        }

        // Check similar article count threshold
        if (thresholdSettings.similarArticleCount() != null && thresholdSettings.similarArticleCount().enabled()) {
            boolean meets = document.getSimilarityNum() != null &&
                    document.getSimilarityNum() >= thresholdSettings.similarArticleCount().threshold();
            conditionResults.add(meets);
        }

        // If no conditions are set, return true
        if (conditionResults.isEmpty()) {
            return true;
        }

        // Apply condition relation (AND/OR)
        if ("AND".equals(thresholdSettings.conditionRelation())) {
            return conditionResults.stream().allMatch(Boolean::booleanValue);
        } else {
            return conditionResults.stream().anyMatch(Boolean::booleanValue);
        }
    }

    /**
     * Calculate alert score based on various factors
     * If levelSettings is null, uses default scoring logic
     */
    private int calculateAlertScore(LevelSettingsDto levelSettings, SinaNewsDocument document) {
        int score = 0;

        // Base score from sensitivity
        if (document.getSensitivityType() != null) {
            InformationSensitivityType sensitivityType = InformationSensitivityType
                    .fromInteger(document.getSensitivityType());
            if (sensitivityType.isSensitive()) {
                score += 30;
            }
        }

        // Score from interaction metrics
        long totalInteractions = calculateTotalInteractions(document);
        if (levelSettings != null && levelSettings.interactionThresholds() != null) {
            // Use configured thresholds if available
            score += calculateThresholdScore(levelSettings.interactionThresholds(), totalInteractions);
        } else {
            // Use default thresholds
            if (totalInteractions > 1000)
                score += 20;
            else if (totalInteractions > 100)
                score += 10;
        }

        // Score from fans count (using authorFollowersCount)
        if (document.getAuthorFollowersCount() != null) {
            if (levelSettings != null && levelSettings.fansThresholds() != null) {
                // Use configured thresholds if available
                score += calculateThresholdScore(levelSettings.fansThresholds(), document.getAuthorFollowersCount());
            } else {
                // Use default thresholds
                if (document.getAuthorFollowersCount() > 10000)
                    score += 20;
                else if (document.getAuthorFollowersCount() > 1000)
                    score += 10;
            }
        }

        // Score from media level
        if (levelSettings != null && levelSettings.sourceLevel() != null) {
            // Use configured source level mappings if available
            String mediaLevelStr = document.getMediaLevel();
            if (mediaLevelStr != null) {
                MediaLevel mediaLevel = MediaLevel.fromChineseValue(mediaLevelStr);
                String mediaLevelKey = mediaLevel.getDescription();
                if (levelSettings.sourceLevel().containsKey(mediaLevelKey)) {
                    String configuredLevel = levelSettings.sourceLevel().get(mediaLevelKey);
                    score += getScoreForLevel(configuredLevel);
                }
            }
        } else {
            // Use default media level scoring
            String mediaLevelStr = document.getMediaLevel();
            if (mediaLevelStr != null) {
                MediaLevel mediaLevel = MediaLevel.fromChineseValue(mediaLevelStr);
                if (mediaLevel == MediaLevel.NATIONAL) {
                    score += 25;
                } else if (mediaLevel == MediaLevel.PROVINCIAL) {
                    score += 15;
                }
            }
        }

        return score;
    }

    /**
     * Calculate score based on threshold configuration
     */
    private int calculateThresholdScore(LevelSettingsDto.LevelThresholdsDto thresholds, long value) {
        if (thresholds == null) {
            return 0;
        }

        // Check severe threshold first
        if (thresholds.severe() != null && isInRange(thresholds.severe(), value)) {
            return 20;
        }
        // Check moderate threshold
        if (thresholds.moderate() != null && isInRange(thresholds.moderate(), value)) {
            return 10;
        }
        // Check general threshold
        if (thresholds.general() != null && isInRange(thresholds.general(), value)) {
            return 5;
        }

        return 0;
    }

    /**
     * Check if value is within threshold range
     */
    private boolean isInRange(LevelSettingsDto.ThresholdRangeDto range, long value) {
        if (range == null) {
            return false;
        }

        boolean aboveMin = range.min() == null || value >= range.min();
        boolean belowMax = range.max() == null || value < range.max();

        return aboveMin && belowMax;
    }

    /**
     * Get score for configured level
     */
    private int getScoreForLevel(String level) {
        if (level == null) {
            return 0;
        }

        return switch (level.toUpperCase()) {
            case "SEVERE" -> 25;
            case "MODERATE" -> 15;
            case "GENERAL" -> 5;
            default -> 0;
        };
    }

    /**
     * Build searchable content from document
     */
    private String buildSearchableContent(SinaNewsDocument document) {
        StringBuilder content = new StringBuilder();

        if (document.getTitle() != null) {
            content.append(document.getTitle()).append(" ");
        }
        if (document.getContent() != null) {
            content.append(document.getContent()).append(" ");
        }
        if (document.getSummary() != null) {
            content.append(document.getSummary()).append(" ");
        }

        return content.toString();
    }

    /**
     * Determine source type (original vs forwarded)
     */
    private String determineSourceType(SinaNewsDocument document) {
        // Logic to determine if content is original or forwarded
        // Based on document properties like wbForwardType, rootContentId, etc.
        if (document.getRootContentId() != null && !document.getRootContentId().isEmpty()) {
            return "FORWARDED";
        }
        return "ORIGINAL";
    }

    /**
     * Calculate total interactions from document metrics
     */
    private long calculateTotalInteractions(SinaNewsDocument document) {
        long total = 0;
        if (document.getForwardCount() != null)
            total += document.getForwardCount();
        if (document.getCommentCount() != null)
            total += document.getCommentCount();
        if (document.getPraiseCount() != null)
            total += document.getPraiseCount();
        if (document.getLookingCount() != null)
            total += document.getLookingCount();
        return total;
    }

    // JSON parsing helper methods
    private AlertKeywordsDto parseAlertKeywords(String json) {
        // If JSON is null or empty, return null (no keyword filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Alert keywords JSON is null or empty, returning null");
            return null;
        }

        try {
            AlertKeywordsDto result = objectMapper.readValue(json, AlertKeywordsDto.class);
            log.debug("Parsed alert keywords: {}", result);
            return result;
        } catch (Exception e) {
            log.error("Error parsing alert keywords JSON: {}, JSON content: {}", e.getMessage(), json);
            // Return null instead of empty keywords to indicate no keyword filtering
            return null;
        }
    }

    private ContentSettingsDto parseContentSettings(String json) {
        // If JSON is null or empty, return null (no content filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Content settings JSON is null or empty, returning null");
            return null;
        }

        try {
            return objectMapper.readValue(json, ContentSettingsDto.class);
        } catch (Exception e) {
            log.error("Error parsing content settings JSON: {}", e.getMessage());
            // Return default ContentSettingsDto with minimal required fields
            return new ContentSettingsDto(null, null, null, null, null, null, null);
        }
    }

    private ThresholdSettingsDto parseThresholdSettings(String json) {
        // If JSON is null or empty, return null (no threshold filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Threshold settings JSON is null or empty, returning null");
            return null;
        }

        try {
            return objectMapper.readValue(json, ThresholdSettingsDto.class);
        } catch (Exception e) {
            log.error("Error parsing threshold settings JSON: {}", e.getMessage());
            // Return default ThresholdSettingsDto with minimal configuration
            return new ThresholdSettingsDto("OR", null, null, null, null, null);
        }
    }

    private LevelSettingsDto parseLevelSettings(String json) {
        // If JSON is null or empty, return null (use default level logic)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Level settings JSON is null or empty, returning null");
            return null;
        }

        try {
            return objectMapper.readValue(json, LevelSettingsDto.class);
        } catch (Exception e) {
            log.error("Error parsing level settings JSON: {}", e.getMessage());
            return new LevelSettingsDto(null, null, null, null, null);
        }
    }

    /**
     * Map document source to source type
     */
    private String mapSourceType(String source) {
        if (source == null) {
            return "OTHER";
        }

        String lowerSource = source.toLowerCase();
        return lowerSource;
    }
}
