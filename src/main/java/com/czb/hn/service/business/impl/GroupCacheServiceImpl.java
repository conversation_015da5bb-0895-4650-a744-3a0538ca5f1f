package com.czb.hn.service.business.impl;

import cn.com.ycfin.onepass.client.OnePassClient;
import cn.com.ycfin.onepass.client.model.Group;
import cn.com.ycfin.onepass.client.response.GetGroupsResponse;

import com.czb.hn.dto.user.GroupInfo;
import com.czb.hn.service.business.GroupCacheService;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 部门/组织信息缓存服务实现类
 * 负责从OnePass获取、缓存和管理部门/组织信息
 */
@Service
public class GroupCacheServiceImpl implements GroupCacheService {

    private static final Logger logger = LoggerFactory.getLogger(GroupCacheServiceImpl.class);
    
    // 缓存最大生存时间（默认1小时）
    @Value("${onepass.group.cache.ttl:3600000}")
    private long cacheTtl;
    
    // 缓存刷新间隔（默认30分钟）
    @Value("${onepass.group.cache.refresh:1800000}")
    private long cacheRefreshInterval;
    
    @Autowired
    private OnePassClient onePassClient;
    
    // 组织信息缓存
    private final Map<String, GroupInfo> groupByIdCache = new ConcurrentHashMap<>();
    private final Map<String, String> groupIdToCodeMap = new ConcurrentHashMap<>();
    private final Map<String, GroupInfo> groupByCodeCache = new ConcurrentHashMap<>();
    
    // 缓存读写锁
    private final ReentrantReadWriteLock cacheLock = new ReentrantReadWriteLock();
    
    // 缓存最后更新时间
    private volatile long lastRefreshTime = 0L;
    
    // 上次刷新是否成功
    private volatile boolean lastRefreshSuccessful = false;
    
    @PostConstruct
    public void init() {
        // 初始化时加载缓存
        refreshCache();
    }
    
    /**
     * 定时刷新缓存
     */
    @Scheduled(fixedDelayString = "${onepass.group.cache.refresh:1800000}")
    public void scheduledRefresh() {
        logger.debug("执行定时刷新组织缓存");
        refreshCache();
    }
    
    @Override
    public void refreshCache() {
        try {
            cacheLock.writeLock().lock();
            
            logger.info("开始刷新组织缓存数据");
            
            // 清除现有缓存
            groupByIdCache.clear();
            groupByCodeCache.clear();
            groupIdToCodeMap.clear();
            
            // 从OnePass获取组织信息
            List<GroupInfo> groups = fetchAllGroupsFromOnePass();
            
            // 更新缓存
            for (GroupInfo group : groups) {
                processGroup(group);
            }
            
            lastRefreshTime = System.currentTimeMillis();
            lastRefreshSuccessful = true;
            
            logger.info("组织缓存刷新成功，共加载 {} 个组织", groupByIdCache.size());
        } catch (Exception e) {
            lastRefreshSuccessful = false;
            logger.error("刷新组织缓存失败", e);
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 处理单个组织及其子组织
     */
    private void processGroup(GroupInfo group) {
        if (group == null || group.groupId() == null) {
            return;
        }
        
        // 添加到ID缓存
        groupByIdCache.put(group.groupId(), group);
        
        // 如果组织代码存在，添加到代码缓存
        if (group.groupCode() != null && !group.groupCode().isEmpty()) {
            groupByCodeCache.put(group.groupCode(), group);
            groupIdToCodeMap.put(group.groupId(), group.groupCode());
        }
        
        // 递归处理子组织
        if (group.children() != null) {
            for (GroupInfo child : group.children()) {
                processGroup(child);
            }
        }
    }
    
    /**
     * 从OnePass获取所有组织信息并转换为GroupInfo结构
     */
    private List<GroupInfo> fetchAllGroupsFromOnePass() {
        try {
            // 调用OnePass客户端获取组织结构
            GetGroupsResponse groupsResponse = onePassClient.getGroups();
            
            if (groupsResponse == null || groupsResponse.getGroups() == null) {
                logger.warn("从OnePass获取的组织信息为空");
                return Collections.emptyList();
            }

            List<Group> groups = groupsResponse.getGroups();
            
            // 临时存储所有组织
            Map<String, GroupInfo> groupMap = new HashMap<>();
            // 存储子组织与父组织的关系
            Map<String, String> childToParentMap = new HashMap<>();
            
            // 第一遍遍历：创建所有GroupInfo对象并记录父子关系
            for (Group group : groups) {
                String id = group.getId();
                String parentId = group.getParentId();
                
                GroupInfo groupInfo = new GroupInfo(
                    id,
                    group.getName(),
                    group.getCode(),
                    parentId,
                    0, // 初始层级设为0，后面会计算实际层级
                    new ArrayList<>()
                );
                
                groupMap.put(id, groupInfo);
                if (parentId != null && !parentId.isEmpty()) {
                    childToParentMap.put(id, parentId);
                }
            }
            
            // 第二遍遍历：构建层级关系
            for (String childId : childToParentMap.keySet()) {
                String parentId = childToParentMap.get(childId);
                GroupInfo child = groupMap.get(childId);
                GroupInfo parent = groupMap.get(parentId);
                
                if (parent != null && child != null) {
                    // 获取父节点现有的子节点列表
                    List<GroupInfo> children = new ArrayList<>(parent.children());
                    children.add(child);
                    
                    // 创建带有更新后的子节点列表的新父节点（因为GroupInfo是不可变的）
                    GroupInfo updatedParent = new GroupInfo(
                        parent.groupId(), 
                        parent.groupName(),
                        parent.groupCode(),
                        parent.parentId(),
                        parent.level(),
                        children
                    );
                    
                    // 更新父节点
                    groupMap.put(parentId, updatedParent);
                }
            }
            
            // 计算每个节点的层级
            calculateGroupLevels(groupMap);
            
            // 找出根节点（没有父节点的节点）
            List<GroupInfo> rootGroups = new ArrayList<>();
            for (GroupInfo group : groupMap.values()) {
                if (group.parentId() == null || group.parentId().isEmpty() || !groupMap.containsKey(group.parentId())) {
                    rootGroups.add(group);
                }
            }
            
            return new ArrayList<>(groupMap.values());
        } catch (Exception e) {
            logger.error("从OnePass获取组织信息失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 计算每个组织的层级
     */
    private void calculateGroupLevels(Map<String, GroupInfo> groupMap) {
        // 找出所有根节点
        List<String> rootIds = new ArrayList<>();
        for (GroupInfo group : groupMap.values()) {
            if (group.parentId() == null || group.parentId().isEmpty() || !groupMap.containsKey(group.parentId())) {
                rootIds.add(group.groupId());
            }
        }
        
        // 从每个根节点开始，递归设置层级
        for (String rootId : rootIds) {
            setGroupLevel(groupMap, rootId, 0);
        }
    }
    
    /**
     * 递归设置组织层级
     */
    private void setGroupLevel(Map<String, GroupInfo> groupMap, String groupId, int level) {
        GroupInfo group = groupMap.get(groupId);
        if (group == null) {
            return;
        }
        
        // 创建具有正确层级的新GroupInfo
        GroupInfo updatedGroup = new GroupInfo(
            group.groupId(),
            group.groupName(),
            group.groupCode(),
            group.parentId(),
            level,
            group.children()
        );
        
        // 更新组织Map
        groupMap.put(groupId, updatedGroup);
        
        // 递归处理子组织
        for (GroupInfo child : group.children()) {
            setGroupLevel(groupMap, child.groupId(), level + 1);
        }
    }
    
    @Override
    public List<GroupInfo> getAllGroups() {
        checkAndRefreshCacheIfExpired();
        
        try {
            cacheLock.readLock().lock();
            return new ArrayList<>(groupByIdCache.values());
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    @Override
    public Optional<GroupInfo> getGroupById(String groupId) {
        if (groupId == null) {
            return Optional.empty();
        }
        
        checkAndRefreshCacheIfExpired();
        
        try {
            cacheLock.readLock().lock();
            return Optional.ofNullable(groupByIdCache.get(groupId));
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    @Override
    public Optional<GroupInfo> getGroupByCode(String groupCode) {
        if (groupCode == null) {
            return Optional.empty();
        }
        
        checkAndRefreshCacheIfExpired();
        
        try {
            cacheLock.readLock().lock();
            return Optional.ofNullable(groupByCodeCache.get(groupCode));
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    @Override
    public Map<String, String> getGroupIdToCodeMapping() {
        checkAndRefreshCacheIfExpired();
        
        try {
            cacheLock.readLock().lock();
            return new HashMap<>(groupIdToCodeMap);
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    /**
     * 检查缓存是否过期，如果过期则刷新
     */
    private void checkAndRefreshCacheIfExpired() {
        long currentTime = System.currentTimeMillis();
        // 如果缓存为空或者过期
        if (groupByIdCache.isEmpty() || (currentTime - lastRefreshTime > cacheTtl)) {
            logger.debug("缓存已过期或为空，执行刷新");
            refreshCache();
        }
    }
} 