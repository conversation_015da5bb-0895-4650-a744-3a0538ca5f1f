package com.czb.hn.service.business;

import com.czb.hn.dto.alert.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for alert configuration management
 * Provides business logic for CRUD operations and snapshot management
 */
public interface AlertConfigurationService {

        /**
         * Create a new alert configuration
         * Automatically creates the first snapshot
         *
         * @param createDto Configuration creation data
         * @return Created configuration response
         */
        AlertConfigurationResponseDto createConfiguration(AlertConfigurationCreateDto createDto);

        /**
         * Update an existing alert configuration
         * Automatically creates a new snapshot with incremented version
         *
         * @param id        Configuration ID
         * @param updateDto Configuration update data
         * @return Updated configuration response
         */
        AlertConfigurationResponseDto updateConfiguration(Long id, AlertConfigurationUpdateDto updateDto);

        /**
         * Get alert configuration by ID
         *
         * @param id Configuration ID
         * @return Configuration response
         */
        AlertConfigurationResponseDto getConfigurationById(Long id);

        /**
         * Get all active alert configurations with pagination
         *
         * @param pageable Pagination parameters
         * @return Page of configuration responses
         */
        Page<AlertConfigurationResponseDto> getAllConfigurations(Pageable pageable);

        /**
         * Get alert configurations by enterprise ID
         *
         * @param enterpriseId Enterprise ID
         * @return List of configuration responses
         */
        List<AlertConfigurationResponseDto> getConfigurationsByEnterpriseId(String enterpriseId);

        /**
         * Get alert configurations by plan ID
         *
         * @param planId Plan ID
         * @return List of configuration responses
         */
        List<AlertConfigurationResponseDto> getConfigurationsByPlanId(Long planId);

        /**
         * Get enabled alert configurations by enterprise ID
         *
         * @param enterpriseId Enterprise ID
         * @return List of enabled configuration responses
         */
        List<AlertConfigurationResponseDto> getEnabledConfigurationsByEnterpriseId(String enterpriseId);

        /**
         * Get enabled alert configurations by plan ID
         *
         * @param planId Plan ID
         * @return List of enabled configuration responses
         */
        List<AlertConfigurationResponseDto> getEnabledConfigurationsByPlanId(Long planId);

        /**
         * Search configurations by name pattern
         *
         * @param namePattern Name pattern to search
         * @param pageable    Pagination parameters
         * @return Page of matching configuration responses
         */
        Page<AlertConfigurationResponseDto> searchConfigurationsByName(String namePattern, Pageable pageable);

        /**
         * Get configurations created within date range
         *
         * @param startDate Start date
         * @param endDate   End date
         * @param pageable  Pagination parameters
         * @return Page of configuration responses
         */
        Page<AlertConfigurationResponseDto> getConfigurationsByCreatedDateRange(LocalDateTime startDate,
                        LocalDateTime endDate, Pageable pageable);

        /**
         * Enable or disable a configuration
         *
         * @param id           Configuration ID
         * @param enabled      Whether to enable or disable
         * @param updatedBy    User performing the operation
         * @param changeReason Reason for the change
         * @return Updated configuration response
         */
        AlertConfigurationResponseDto toggleConfiguration(Long id, Boolean enabled, String updatedBy,
                        String changeReason);

        /**
         * Soft delete a configuration (set isActive to false)
         *
         * @param id           Configuration ID
         * @param deletedBy    User performing the deletion
         * @param deleteReason Reason for deletion
         */
        void deleteConfiguration(Long id, String deletedBy, String deleteReason);

        /**
         * Get all snapshots for a configuration
         *
         * @param configurationId Configuration ID
         * @param pageable        Pagination parameters
         * @return Page of snapshot responses
         */
        Page<AlertConfigurationSnapshotDto> getConfigurationSnapshots(Long configurationId, Pageable pageable);

        /**
         * Get a specific snapshot by configuration ID and version
         *
         * @param configurationId Configuration ID
         * @param version         Version number
         * @return Snapshot response
         */
        AlertConfigurationSnapshotDto getConfigurationSnapshot(Long configurationId, Integer version);

        /**
         * Get the currently active snapshot for a configuration
         *
         * @param configurationId Configuration ID
         * @return Active snapshot response
         */
        AlertConfigurationSnapshotDto getActiveSnapshot(Long configurationId);

        /**
         * Rollback configuration to a specific version
         * Creates a new snapshot with rollback operation type
         *
         * @param configurationId Configuration ID
         * @param targetVersion   Target version to rollback to
         * @param rolledBackBy    User performing the rollback
         * @param rollbackReason  Reason for rollback
         * @return Updated configuration response
         */
        AlertConfigurationResponseDto rollbackToVersion(Long configurationId, Integer targetVersion,
                        String rolledBackBy,
                        String rollbackReason);

        /**
         * Create a manual snapshot of current configuration state
         *
         * @param configurationId Configuration ID
         * @param createdBy       User creating the snapshot
         * @param reason          Reason for creating the snapshot
         * @return Created snapshot response
         */
        AlertConfigurationSnapshotDto createManualSnapshot(Long configurationId, String createdBy, String reason);

        /**
         * Validate configuration name uniqueness within enterprise
         *
         * @param name         Configuration name
         * @param enterpriseId Enterprise ID
         * @param excludeId    Configuration ID to exclude from check (for updates)
         * @return True if name is available
         */
        Boolean isConfigurationNameAvailable(String name, String enterpriseId, Long excludeId);

        /**
         * Get configuration statistics for an enterprise
         *
         * @param enterpriseId Enterprise ID
         * @return Configuration statistics
         */
        ConfigurationStatisticsDto getConfigurationStatistics(String enterpriseId);

        /**
         * DTO for configuration statistics
         */
        record ConfigurationStatisticsDto(
                        Long totalConfigurations,
                        Long enabledConfigurations,
                        Long disabledConfigurations,
                        Long totalSnapshots,
                        Long averageSnapshotsPerConfiguration) {
        }
}
