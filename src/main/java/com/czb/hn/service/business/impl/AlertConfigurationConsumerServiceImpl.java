package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.AlertKeywordsDto;
import com.czb.hn.dto.alert.config.ContentSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.util.AlertConfigurationMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Implementation of AlertConfigurationConsumerService
 * Provides cached access to alert configurations for optimal performance
 */
@Service
@Transactional(readOnly = true)
public class AlertConfigurationConsumerServiceImpl implements AlertConfigurationConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationConsumerServiceImpl.class);

    @Autowired
    private AlertConfigurationRepository alertConfigurationRepository;

    @Autowired
    private AlertConfigurationMapper configurationMapper;

    // Cache statistics
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private volatile long lastRefreshTime = System.currentTimeMillis();

    @Override
    @Cacheable(value = "activeConfigurations", key = "'all'")
    public List<AlertConfigurationResponseDto> getAllActiveConfigurations() {
        logger.debug("Loading all active configurations from database");
        cacheMisses.incrementAndGet();
        
        List<AlertConfiguration> configurations = alertConfigurationRepository.findAllActiveConfigurations();
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Cacheable(value = "activeConfigurations", key = "'enterprise:' + #enterpriseId")
    public List<AlertConfigurationResponseDto> getActiveConfigurationsByEnterprise(String enterpriseId) {
        logger.debug("Loading active configurations for enterprise: {}", enterpriseId);
        cacheMisses.incrementAndGet();
        
        List<AlertConfiguration> configurations = alertConfigurationRepository.findEnabledByEnterpriseId(enterpriseId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Cacheable(value = "activeConfigurations", key = "'plan:' + #planId")
    public List<AlertConfigurationResponseDto> getActiveConfigurationsByPlan(Long planId) {
        logger.debug("Loading active configurations for plan: {}", planId);
        cacheMisses.incrementAndGet();
        
        List<AlertConfiguration> configurations = alertConfigurationRepository.findEnabledByPlanId(planId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Cacheable(value = "singleConfiguration", key = "#configurationId")
    public Optional<AlertConfigurationResponseDto> getActiveConfigurationById(Long configurationId) {
        logger.debug("Loading configuration by ID: {}", configurationId);
        cacheMisses.incrementAndGet();
        
        Optional<AlertConfiguration> configuration = alertConfigurationRepository.findActiveById(configurationId);
        return configuration.map(configurationMapper::toResponseDto);
    }

    @Override
    public Boolean isConfigurationActiveAndEnabled(Long configurationId) {
        // Check cache first
        Optional<AlertConfigurationResponseDto> cached = getActiveConfigurationById(configurationId);
        if (cached.isPresent()) {
            cacheHits.incrementAndGet();
            return cached.get().enabled();
        }
        
        cacheMisses.incrementAndGet();
        return false;
    }

    @Override
    public List<AlertConfigurationResponseDto> getMatchingConfigurations(
            List<String> contentKeywords,
            String sourceType,
            String sensitivityType) {
        
        logger.debug("Finding matching configurations for keywords: {}, sourceType: {}, sensitivityType: {}", 
                contentKeywords, sourceType, sensitivityType);
        
        // Get all active configurations and filter in memory for complex matching
        List<AlertConfigurationResponseDto> allConfigurations = getAllActiveConfigurations();
        
        return allConfigurations.stream()
                .filter(config -> matchesKeywords(config, contentKeywords))
                .filter(config -> matchesSourceType(config, sourceType))
                .filter(config -> matchesSensitivityType(config, sensitivityType))
                .toList();
    }

    @Override
    public List<AlertConfigurationResponseDto> getConfigurationsByKeywords(List<String> keywords) {
        logger.debug("Finding configurations by keywords: {}", keywords);
        
        List<AlertConfigurationResponseDto> allConfigurations = getAllActiveConfigurations();
        
        return allConfigurations.stream()
                .filter(config -> matchesKeywords(config, keywords))
                .toList();
    }

    @Override
    @CacheEvict(value = {"activeConfigurations", "singleConfiguration"}, allEntries = true)
    public void refreshCache() {
        logger.info("Refreshing alert configuration cache");
        lastRefreshTime = System.currentTimeMillis();
        
        // Preload frequently accessed data
        preloadCache();
    }

    @Override
    @CacheEvict(value = {"activeConfigurations", "singleConfiguration"}, allEntries = true)
    public void clearCache() {
        logger.info("Clearing alert configuration cache");
        lastRefreshTime = System.currentTimeMillis();
    }

    @Override
    public CacheStatisticsDto getCacheStatistics() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRatio = total > 0 ? (double) hits / total : 0.0;
        
        // Estimate cache size (this is a simplified approach)
        long cacheSize = getAllActiveConfigurations().size();
        
        return new CacheStatisticsDto(
                cacheSize,
                hits,
                misses,
                hitRatio,
                lastRefreshTime,
                cacheSize
        );
    }

    @Override
    public void preloadCache() {
        logger.info("Preloading alert configuration cache");
        
        // Load all active configurations to warm up the cache
        getAllActiveConfigurations();
        
        // Load configurations for common enterprises (if any patterns exist)
        // This could be enhanced based on usage patterns
    }

    /**
     * Scheduled cache refresh every 5 minutes
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void scheduledCacheRefresh() {
        logger.debug("Performing scheduled cache refresh");
        refreshCache();
    }

    /**
     * Check if configuration matches any of the provided keywords
     */
    private boolean matchesKeywords(AlertConfigurationResponseDto config, List<String> contentKeywords) {
        if (contentKeywords == null || contentKeywords.isEmpty()) {
            return true;
        }
        
        AlertKeywordsDto alertKeywords = config.alertKeywords();
        if (alertKeywords == null || alertKeywords.keywords() == null) {
            return false;
        }
        
        // Check if any content keyword matches any alert keyword
        return contentKeywords.stream()
                .anyMatch(contentKeyword -> 
                    alertKeywords.keywords().stream()
                        .anyMatch(alertKeyword -> 
                            contentKeyword.toLowerCase().contains(alertKeyword.toLowerCase()) ||
                            alertKeyword.toLowerCase().contains(contentKeyword.toLowerCase())
                        )
                );
    }

    /**
     * Check if configuration matches the source type
     */
    private boolean matchesSourceType(AlertConfigurationResponseDto config, String sourceType) {
        if (sourceType == null) {
            return true;
        }
        
        ContentSettingsDto contentSettings = config.contentSettings();
        if (contentSettings == null || contentSettings.sourceTypes() == null) {
            return true; // Default to match if no restrictions
        }
        
        List<String> allowedSourceTypes = contentSettings.sourceTypes();
        return allowedSourceTypes.contains("ALL") || allowedSourceTypes.contains(sourceType);
    }

    /**
     * Check if configuration matches the sensitivity type
     */
    private boolean matchesSensitivityType(AlertConfigurationResponseDto config, String sensitivityType) {
        if (sensitivityType == null) {
            return true;
        }
        
        ContentSettingsDto contentSettings = config.contentSettings();
        if (contentSettings == null) {
            return true; // Default to match if no restrictions
        }
        
        String allowedSensitivityType = contentSettings.sensitivityType();
        return "ALL".equals(allowedSensitivityType) || sensitivityType.equals(allowedSensitivityType);
    }
}
