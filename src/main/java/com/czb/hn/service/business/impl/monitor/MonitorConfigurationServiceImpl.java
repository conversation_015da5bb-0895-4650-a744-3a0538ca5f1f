package com.czb.hn.service.business.impl.monitor;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.monitor.MonitorConfigurationCreateDto;
import com.czb.hn.dto.monitor.MonitorConfigurationResponseDto;
import com.czb.hn.dto.monitor.MonitorConfigurationUpdateDto;
import com.czb.hn.jpa.securadar.entity.MonitorConfiguration;
import com.czb.hn.jpa.securadar.repository.MonitorConfigurationRepository;
import com.czb.hn.service.business.MonitorConfigurationService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.util.MonitorConfigurationMapper;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.slf4j.Logger;

@Service
@Transactional
public class MonitorConfigurationServiceImpl implements MonitorConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(MonitorConfigurationServiceImpl.class.getName());

    @Autowired
    private MonitorConfigurationRepository monitorConfigurationRepository;

    @Autowired
    private MonitorConfigurationMapper monitorConfigurationMapper;

    @Autowired
    private PlanService planService;

    @Override
    public MonitorConfigurationResponseDto getConfigurationsByPlanId(Long planId) {
        try {
            logger.info("Retrieving monitor configuration by plan ID: {}", planId);

            if (monitorConfigurationRepository.existsByPlanId(planId)) {
                MonitorConfiguration configuration = monitorConfigurationRepository.findByPlanId(planId);
                logger.info("Successfully retrieved monitor configuration for plan ID: {}", planId);
                return monitorConfigurationMapper.toResponseDto(configuration);
            } else {
                logger.info("Monitor configuration not found for plan ID: {}. Create default configuration", planId);
                PlanDTO planDto = planService.getPlanById(planId);
                String planName = planDto.name();
                MonitorConfigurationCreateDto createDto = MonitorConfigurationCreateDto.withDefaults(planId, planName);
                MonitorConfiguration configuration = monitorConfigurationMapper.toEntity(createDto);
                MonitorConfiguration savedConfiguration = monitorConfigurationRepository.save(configuration);
                return monitorConfigurationMapper.toResponseDto(savedConfiguration);
            }
        } catch (Exception e) {
            logger.error("Error retrieving monitor configuration by plan ID {}: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve monitor configuration: " + e.getMessage(), e);
        }
    }

    @Override
    public MonitorConfigurationResponseDto updateConfiguration(Long planId, MonitorConfigurationUpdateDto updateDto) {
        try {
            logger.info("Updating monitor configuration for plan ID: {}", planId);

            if (!monitorConfigurationRepository.existsByPlanId(planId)) {
                logger.warn("Monitor configuration not found for plan ID: {}. Create it before update", planId);
                throw new RuntimeException("Monitor configuration not found for plan ID: " + planId);
            } else {
                MonitorConfiguration configuration = monitorConfigurationRepository.findByPlanId(planId);
                monitorConfigurationMapper.updateEntityFromDto(updateDto, configuration);
                MonitorConfiguration updatedConfiguration = monitorConfigurationRepository.save(configuration);
                return monitorConfigurationMapper.toResponseDto(updatedConfiguration);
            }
        } catch (Exception e) {
            logger.error("Error updating monitor configuration for plan ID {}: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to update monitor configuration: " + e.getMessage(), e);
        }
    }
}
