package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.AlertPushDetailRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Alert Push Service Implementation
 * Manages alert notification push operations with multi-tenant support
 */
@Service
@Slf4j
@Transactional
public class AlertPushServiceImpl implements AlertPushService {

    @Value("${alert.message.sms.template.id}")
    private Long smsTemplateId;

    @Autowired
    private AlertPushDetailRepository alertPushDetailRepository;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private com.czb.hn.util.SmsUtil smsUtil;

    @Autowired
    private com.czb.hn.util.EmailUtil emailUtil;

    @Override
    @Transactional(readOnly = true)
    public List<AlertPushDetailResponseDto> getPushDetailsByAlertId(Long alertId) {
        log.debug("Getting push details for alert ID: {}", alertId);

        try {
            // 通过 AlertResult 的关联查询 AlertPushDetail
            AlertResult alertResult = alertResultRepository.findById(alertId)
                    .orElseThrow(() -> new IllegalArgumentException("Alert not found with ID: " + alertId));

            List<AlertPushDetail> pushDetails = new ArrayList<>();

            // 如果该预警关联了推送详情，则查询
            if (alertResult.getAlertPushDetailId() != null) {
                alertPushDetailRepository.findById(alertResult.getAlertPushDetailId())
                        .ifPresent(pushDetails::add);
            }

            return pushDetails.stream()
                    .map(this::mapToResponseDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get push details for alert ID: {}", alertId, e);
            throw new RuntimeException("Failed to get push details: " + e.getMessage(), e);
        }
    }

    /**
     * Map AlertPushDetail entity to response DTO
     */
    private AlertPushDetailResponseDto mapToResponseDto(AlertPushDetail pushDetail) {
        return new AlertPushDetailResponseDto(
                pushDetail.getId(),
                pushDetail.getEnterpriseId(),
                pushDetail.getAccountInfo(),
                pushDetail.getPushType(),
                pushDetail.getPushStatus(),
                pushDetail.getPushTime(),
                pushDetail.getErrorMessage(),
                pushDetail.getPushDetails(),
                pushDetail.getRetryCount(),
                pushDetail.getLastRetryTime(),
                pushDetail.getCreatedAt(),
                pushDetail.getUpdatedAt(),
                pushDetail.getCreatedBy());
    }

    @Override
    public CompletableFuture<Boolean> sendEmailNotification(String email, String subject, String content) {
        log.info("Sending email notification to: {}", email);

        return CompletableFuture.supplyAsync(() -> {
            try {
                emailUtil.sendEmail(email, subject, content);
                // This is a placeholder implementation
                log.info("Email notification sent successfully to: {}", email);
                return true;
            } catch (Exception e) {
                log.error("Failed to send email to: {}", email, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> sendSmsNotification(String phoneNumber, String message) {
        log.info("Sending SMS notification to: {}", phoneNumber);

        return CompletableFuture.supplyAsync(() -> {
            try {
                HashMap<String, String> messageMap = new HashMap<>();
                messageMap.put("message", message);
                smsUtil.sendSms(phoneNumber, smsTemplateId, messageMap);
                // This is a placeholder implementation
                log.info("SMS notification sent successfully to: {}", phoneNumber);
                // For now, always return success
                return true;
            } catch (Exception e) {
                log.error("Failed to send SMS to: {}", phoneNumber, e);
                return false;
            }
        });
    }

    @Override
    public void createPushRecordsForAlert(AlertResult alert, Object recipient) {
        log.debug("Creating push records for alert {} and recipient", alert != null ? alert.getId() : "null");

        try {
            // Cast recipient to RecipientInfo
            if (!(recipient instanceof com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo)) {
                log.error("Invalid recipient type: {}", recipient.getClass().getName());
                throw new IllegalArgumentException("Recipient must be of type RecipientInfo");
            }

            // Validate alert
            if (alert == null) {
                log.error("Invalid alert: alert is null");
                throw new IllegalArgumentException("Alert cannot be null");
            }
            if (alert.getId() == null) {
                log.error("Invalid alert: alert ID is null");
                throw new IllegalArgumentException("Alert ID cannot be null");
            }

            // Validate recipient has enabled methods
            if (!recipientInfo.hasEnabledMethods()) {
                log.debug("Recipient {} has no enabled notification methods, skipping push record creation",
                        recipientInfo.name());
                return;
            }

            LocalDateTime pushTime = LocalDateTime.now();
            String createdBy = "SYSTEM"; // System-generated push records

            // Create push record for email if enabled
            if (recipientInfo.emailEnabled() && recipientInfo.email() != null && !recipientInfo.email().isBlank()) {
                createPushDetailRecord(alert, recipientInfo, PushType.EMAIL, recipientInfo.email(), pushTime,
                        createdBy);
            }

            // Create push record for SMS if enabled
            if (recipientInfo.smsEnabled() && recipientInfo.phone() != null && !recipientInfo.phone().isBlank()) {
                createPushDetailRecord(alert, recipientInfo, PushType.SMS, recipientInfo.phone(), pushTime, createdBy);
            }

            log.debug("Successfully created push records for alert {} and recipient {}",
                    alert.getId(), recipientInfo.name());

        } catch (Exception e) {
            log.error("Failed to create push records for alert {} and recipient: {}",
                    alert != null ? alert.getId() : "null", e.getMessage(), e);
            throw new RuntimeException("Failed to create push records: " + e.getMessage(), e);
        }
    }

    /**
     * Create a single push detail record
     */
    private void createPushDetailRecord(AlertResult alert,
            com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo,
            PushType pushType,
            String accountInfo,
            LocalDateTime pushTime,
            String createdBy) {
        try {
            AlertPushDetail pushDetail = AlertPushDetail.builder()
                    .planId(alert.getPlanId())
                    .alertConfigSnapshotId(alert.getConfigurationId()) // Use configurationId as snapshot ID
                    .enterpriseId(alert.getEnterpriseId())
                    .accountInfo(accountInfo)
                    .pushType(pushType)
                    .pushStatus(PushStatus.SUCCESS) // Assume batch push was successful
                    .pushTime(pushTime)
                    .errorMessage(null)
                    .pushDetails(String.format("批量预警推送记录 - %s: %s",
                            pushType.getDescription(), recipientInfo.name()))
                    .retryCount(0)
                    .createdBy(createdBy)
                    .build();

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);

            // Update AlertResult with the push detail ID (for the last created record)
            // Note: This will only store the last push detail ID if multiple push types are
            // enabled
            alert.setAlertPushDetailId(saved.getId());
            alertResultRepository.save(alert);

            log.debug("Created push detail record {} for alert {} with type {} to {}",
                    saved.getId(), alert.getId(), pushType, accountInfo);

        } catch (Exception e) {
            log.error("Failed to create push detail record for alert {} with type {} to {}: {}",
                    alert.getId(), pushType, accountInfo, e.getMessage(), e);
            throw new RuntimeException("Failed to create push detail record: " + e.getMessage(), e);
        }
    }

    @Override
    public void createNoAlertPushRecord(Object notification, Object recipient) {
        log.debug("Creating push record for no-alert notification");

        try {
            // Cast notification to AlertNotificationQueue
            if (!(notification instanceof com.czb.hn.jpa.securadar.entity.AlertNotificationQueue notificationQueue)) {
                log.error("Invalid notification type: {}", notification.getClass().getName());
                throw new IllegalArgumentException("Notification must be of type AlertNotificationQueue");
            }

            // Cast recipient to RecipientInfo
            if (!(recipient instanceof com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo)) {
                log.error("Invalid recipient type: {}", recipient.getClass().getName());
                throw new IllegalArgumentException("Recipient must be of type RecipientInfo");
            }

            // Validate recipient has enabled methods
            if (!recipientInfo.hasEnabledMethods()) {
                log.debug("Recipient {} has no enabled notification methods, skipping push record creation",
                        recipientInfo.name());
                return;
            }

            LocalDateTime pushTime = LocalDateTime.now();
            String createdBy = "SYSTEM"; // System-generated push records

            // Create push record for email if enabled
            if (recipientInfo.emailEnabled() && recipientInfo.email() != null && !recipientInfo.email().isBlank()) {
                createNoAlertPushDetailRecord(notificationQueue, recipientInfo, PushType.EMAIL,
                        recipientInfo.email(), pushTime, createdBy);
            }

            // Create push record for SMS if enabled
            if (recipientInfo.smsEnabled() && recipientInfo.phone() != null && !recipientInfo.phone().isBlank()) {
                createNoAlertPushDetailRecord(notificationQueue, recipientInfo, PushType.SMS,
                        recipientInfo.phone(), pushTime, createdBy);
            }

            log.debug("Successfully created no-alert push records for notification {} and recipient {}",
                    notificationQueue.getId(), recipientInfo.name());

        } catch (Exception e) {
            log.error("Failed to create no-alert push records: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create no-alert push records: " + e.getMessage(), e);
        }
    }

    /**
     * Create a single push detail record for no-alert notification
     */
    private void createNoAlertPushDetailRecord(
            com.czb.hn.jpa.securadar.entity.AlertNotificationQueue notificationQueue,
            com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo,
            PushType pushType,
            String accountInfo,
            LocalDateTime pushTime,
            String createdBy) {
        try {
            AlertPushDetail pushDetail = AlertPushDetail.builder()
                    .planId(notificationQueue.getPlanId())
                    .alertConfigSnapshotId(notificationQueue.getConfigurationId())
                    .enterpriseId(notificationQueue.getEnterpriseId())
                    .accountInfo(accountInfo)
                    .pushType(pushType)
                    .pushStatus(PushStatus.SUCCESS) // Assume no-alert push was successful
                    .pushTime(pushTime)
                    .errorMessage(null)
                    .pushDetails(String.format("无预警通知推送记录 - %s: %s",
                            pushType.getDescription(), recipientInfo.name()))
                    .retryCount(0)
                    .createdBy(createdBy)
                    .build();

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);

            log.debug("Created no-alert push detail record {} for notification {} with type {} to {}",
                    saved.getId(), notificationQueue.getId(), pushType, accountInfo);

        } catch (Exception e) {
            log.error("Failed to create no-alert push detail record for notification {} with type {} to {}: {}",
                    notificationQueue.getId(), pushType, accountInfo, e.getMessage(), e);
            throw new RuntimeException("Failed to create no-alert push detail record: " + e.getMessage(), e);
        }
    }

}
