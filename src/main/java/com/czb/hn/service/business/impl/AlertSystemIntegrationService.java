package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertProcessingService;
import com.czb.hn.service.business.AlertSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Alert System Integration Service
 * 告警系统集成服务，确保告警配置系统与告警处理系统的无缝集成
 */
@Service
@Slf4j
public class AlertSystemIntegrationService {

        @Autowired
        private AlertConfigurationConsumerService alertConfigConsumerService;

        @Autowired
        private AlertProcessingService alertProcessingService;

        @Autowired
        private AlertSearchService alertSearchService;

        /**
         * 触发指定配置的告警处理
         */
        public AlertProcessingResult triggerConfigurationProcessing(Long configurationId) {
                log.info("Triggering alert processing for configuration: {}", configurationId);

                try {
                        // 验证配置是否存在且活跃
                        var configOpt = alertConfigConsumerService.getActiveConfigurationById(configurationId);
                        if (configOpt.isEmpty()) {
                                throw new IllegalArgumentException("配置不存在或未激活: " + configurationId);
                        }

                        AlertConfigurationResponseDto config = configOpt.get();

                        // 执行告警处理
                        List<AlertResultResponseDto> results = alertProcessingService
                                        .processConfigurationAlerts(configurationId);

                        return new AlertProcessingResult(
                                        configurationId,
                                        config.name(),
                                        config.enterpriseId(),
                                        results.size(),
                                        results,
                                        System.currentTimeMillis(),
                                        "SUCCESS");

                } catch (Exception e) {
                        log.error("Error processing alerts for configuration {}: {}", configurationId, e.getMessage(),
                                        e);
                        return new AlertProcessingResult(
                                        configurationId,
                                        "UNKNOWN",
                                        "UNKNOWN",
                                        0,
                                        List.of(),
                                        System.currentTimeMillis(),
                                        "ERROR: " + e.getMessage());
                }
        }

        /**
         * 触发企业所有配置的告警处理
         */
        public EnterpriseProcessingResult triggerEnterpriseProcessing(String enterpriseId) {
                log.info("Triggering alert processing for enterprise: {}", enterpriseId);

                try {
                        // 获取企业的所有活跃配置
                        List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                                        .getActiveConfigurationsByEnterprise(enterpriseId);

                        if (configs.isEmpty()) {
                                log.debug("No active configurations found for enterprise: {}", enterpriseId);
                                return new EnterpriseProcessingResult(
                                                enterpriseId, 0, 0, List.of(), System.currentTimeMillis(),
                                                "NO_CONFIGS");
                        }

                        // 执行企业告警处理
                        List<AlertResultResponseDto> allResults = alertProcessingService
                                        .processEnterpriseAlerts(enterpriseId);

                        // 按配置分组结果
                        Map<Long, List<AlertResultResponseDto>> resultsByConfig = allResults.stream()
                                        .collect(Collectors.groupingBy(AlertResultResponseDto::configurationId));

                        // 创建每个配置的处理结果
                        List<AlertProcessingResult> configResults = configs.stream()
                                        .map(config -> {
                                                List<AlertResultResponseDto> configAlerts = resultsByConfig
                                                                .getOrDefault(config.id(),
                                                                                List.of());
                                                return new AlertProcessingResult(
                                                                config.id(),
                                                                config.name(),
                                                                config.enterpriseId(),
                                                                configAlerts.size(),
                                                                configAlerts,
                                                                System.currentTimeMillis(),
                                                                "SUCCESS");
                                        })
                                        .toList();

                        return new EnterpriseProcessingResult(
                                        enterpriseId,
                                        configs.size(),
                                        allResults.size(),
                                        configResults,
                                        System.currentTimeMillis(),
                                        "SUCCESS");

                } catch (Exception e) {
                        log.error("Error processing alerts for enterprise {}: {}", enterpriseId, e.getMessage(), e);
                        return new EnterpriseProcessingResult(
                                        enterpriseId, 0, 0, List.of(), System.currentTimeMillis(),
                                        "ERROR: " + e.getMessage());
                }
        }

        public record AlertProcessingResult(
                        Long configurationId,
                        String configurationName,
                        String enterpriseId,
                        int alertsGenerated,
                        List<AlertResultResponseDto> alerts,
                        long timestamp,
                        String status) {
        }

        public record EnterpriseProcessingResult(
                        String enterpriseId,
                        int configurationsProcessed,
                        int totalAlertsGenerated,
                        List<AlertProcessingResult> configurationResults,
                        long timestamp,
                        String status) {
        }

        public record ConfigurationValidationResult(
                        Long configurationId,
                        boolean isValid,
                        String message,
                        List<String> validationIssues) {
        }

        public record ConfigurationUsageStatistics(
                        Long configurationId,
                        String configurationName,
                        String enterpriseId,
                        int periodDays,
                        long totalAlerts,
                        Map<String, Long> levelDistribution,
                        Map<String, Long> sourceDistribution,
                        long timestamp) {
        }
}
