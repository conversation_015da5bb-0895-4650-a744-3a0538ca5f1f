package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Alert Notification Scheduler Interface
 * Manages the scheduling and processing of alert notifications based on reception settings
 * Decouples alert generation from notification delivery
 */
public interface AlertNotificationScheduler {

    /**
     * Process pending notifications (scheduled task)
     * Checks for notifications ready to be sent and processes them
     */
    void processPendingNotifications();

    /**
     * Schedule no-alert notifications for configurations without recent alerts
     * 
     * @param configuration Alert configuration to check for no-alert notifications
     */
    void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration);

    /**
     * Process notifications ready for sending
     * 
     * @return Number of notifications processed
     */
    int processReadyNotifications();

    /**
     * Retry failed notifications
     * 
     * @return Number of notifications retried
     */
    int retryFailedNotifications();

}
