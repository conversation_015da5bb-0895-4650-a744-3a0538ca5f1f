package com.czb.hn.service.business;

import com.czb.hn.dto.workbench.InformationStatsDTO;

import java.time.LocalDateTime;

/**
 * 信息统计服务接口
 * 提供舆情信息、敏感信息和预警信息的统计功能
 */
public interface InformationStatsService {

    /**
     * 获取指定方案的信息统计
     *
     * @param planId    方案ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 信息统计结果
     */
    InformationStatsDTO getInformationStats(Long planId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取企业下所有方案的信息统计
     *
     * @param enterpriseId 企业ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 信息统计结果
     */
    InformationStatsDTO getEnterpriseInformationStats(String enterpriseId, LocalDateTime startTime, LocalDateTime endTime);
}
