package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Alert Configuration Sync Service
 * 告警配置同步服务，确保告警配置的变更能够及时同步到告警处理系统
 */
@Service
@Slf4j
public class AlertConfigurationSyncService {

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    // 同步统计信息
    private final AtomicLong syncCount = new AtomicLong(0);
    private final AtomicLong lastSyncTime = new AtomicLong(0);
    private final Map<String, Long> enterpriseSyncTimes = new ConcurrentHashMap<>();
    private final Map<Long, Long> configurationSyncTimes = new ConcurrentHashMap<>();

    /**
     * 同步企业的所有告警配置
     */
    public SyncResult syncEnterpriseConfigurations(String enterpriseId) {
        log.info("Syncing configurations for enterprise: {}", enterpriseId);

        long startTime = System.currentTimeMillis();

        try {
            // 获取企业的活跃配置
            List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                    .getActiveConfigurationsByEnterprise(enterpriseId);

            // 更新同步时间
            enterpriseSyncTimes.put(enterpriseId, System.currentTimeMillis());

            // 更新每个配置的同步时间
            configs.forEach(config -> configurationSyncTimes.put(config.id(), System.currentTimeMillis()));

            long duration = System.currentTimeMillis() - startTime;
            syncCount.incrementAndGet();
            lastSyncTime.set(System.currentTimeMillis());

            log.info("Successfully synced {} configurations for enterprise {} in {}ms",
                    configs.size(), enterpriseId, duration);

            return new SyncResult(
                    enterpriseId,
                    configs.size(),
                    duration,
                    "SUCCESS",
                    "同步成功",
                    System.currentTimeMillis());

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Error syncing configurations for enterprise {}: {}",
                    enterpriseId, e.getMessage(), e);

            return new SyncResult(
                    enterpriseId,
                    0,
                    duration,
                    "ERROR",
                    "同步失败: " + e.getMessage(),
                    System.currentTimeMillis());
        }
    }

    /**
     * 同步单个告警配置
     */
    public ConfigurationSyncResult syncConfiguration(Long configurationId) {
        log.info("Syncing configuration: {}", configurationId);

        long startTime = System.currentTimeMillis();

        try {
            // 获取配置信息
            var configOpt = alertConfigConsumerService.getActiveConfigurationById(configurationId);

            if (configOpt.isEmpty()) {
                return new ConfigurationSyncResult(
                        configurationId,
                        "NOT_FOUND",
                        "配置不存在或未激活",
                        System.currentTimeMillis() - startTime,
                        System.currentTimeMillis());
            }

            AlertConfigurationResponseDto config = configOpt.get();

            // 更新配置同步时间
            configurationSyncTimes.put(configurationId, System.currentTimeMillis());

            // 同时更新企业同步时间
            enterpriseSyncTimes.put(config.enterpriseId(), System.currentTimeMillis());

            long duration = System.currentTimeMillis() - startTime;
            syncCount.incrementAndGet();

            log.info("Successfully synced configuration {} for enterprise {} in {}ms",
                    configurationId, config.enterpriseId(), duration);

            return new ConfigurationSyncResult(
                    configurationId,
                    "SUCCESS",
                    "配置同步成功",
                    duration,
                    System.currentTimeMillis());

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Error syncing configuration {}: {}", configurationId, e.getMessage(), e);

            return new ConfigurationSyncResult(
                    configurationId,
                    "ERROR",
                    "同步失败: " + e.getMessage(),
                    duration,
                    System.currentTimeMillis());
        }
    }

    /**
     * 强制刷新所有缓存
     */
    @CacheEvict(value = { "activeConfigurations", "singleConfiguration" }, allEntries = true)
    public void forceRefreshCache() {
        log.info("Force refreshing all alert configuration caches");

        // 刷新消费者服务的缓存
        alertConfigConsumerService.refreshCache();

        // 更新全局同步时间
        lastSyncTime.set(System.currentTimeMillis());

        log.info("All caches refreshed successfully");
    }

    /**
     * 定时同步任务 - 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void scheduledSync() {
        log.debug("Running scheduled configuration sync");

        try {
            // 刷新缓存以获取最新配置
            forceRefreshCache();

            // 获取所有活跃配置
            List<AlertConfigurationResponseDto> allConfigs = alertConfigConsumerService.getAllActiveConfigurations();

            // 按企业分组并同步
            Map<String, List<AlertConfigurationResponseDto>> configsByEnterprise = allConfigs.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            AlertConfigurationResponseDto::enterpriseId));

            for (String enterpriseId : configsByEnterprise.keySet()) {
                syncEnterpriseConfigurations(enterpriseId);
            }

            log.info("Scheduled sync completed for {} enterprises", configsByEnterprise.size());

        } catch (Exception e) {
            log.error("Error during scheduled sync: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步处理配置变更事件
     */
    @Async
    @EventListener
    public void handleConfigurationChangeEvent(ConfigurationChangeEvent event) {
        log.info("Handling configuration change event: {}", event);

        try {
            switch (event.getEventType()) {
                case CREATED:
                case UPDATED:
                case ENABLED:
                    // 同步单个配置
                    syncConfiguration(event.getConfigurationId());
                    break;

                case DISABLED:
                case DELETED:
                    // 移除同步记录
                    configurationSyncTimes.remove(event.getConfigurationId());
                    break;

                case ENTERPRISE_UPDATED:
                    // 同步整个企业的配置
                    syncEnterpriseConfigurations(event.getEnterpriseId());
                    break;
            }

        } catch (Exception e) {
            log.error("Error handling configuration change event {}: {}", event, e.getMessage(), e);
        }
    }

    /**
     * 获取同步状态
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(
                syncCount.get(),
                lastSyncTime.get(),
                enterpriseSyncTimes.size(),
                configurationSyncTimes.size(),
                System.currentTimeMillis());
    }

    /**
     * 获取企业同步状态
     */
    public EnterpriseSyncStatus getEnterpriseSyncStatus(String enterpriseId) {
        Long lastSync = enterpriseSyncTimes.get(enterpriseId);

        // 获取企业配置数量
        List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                .getActiveConfigurationsByEnterprise(enterpriseId);

        return new EnterpriseSyncStatus(
                enterpriseId,
                configs.size(),
                lastSync != null ? lastSync : 0L,
                lastSync != null,
                System.currentTimeMillis());
    }

    /**
     * 检查配置是否需要同步
     */
    public boolean needsSync(Long configurationId, LocalDateTime lastModified) {
        Long lastSyncTime = configurationSyncTimes.get(configurationId);

        if (lastSyncTime == null) {
            return true; // 从未同步过
        }

        // 检查配置的最后修改时间是否晚于最后同步时间
        long lastModifiedMillis = lastModified.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        return lastModifiedMillis > lastSyncTime;
    }

    // ==================== Event Classes ====================

    public static class ConfigurationChangeEvent {
        private final Long configurationId;
        private final String enterpriseId;
        private final EventType eventType;
        private final long timestamp;

        public ConfigurationChangeEvent(Long configurationId, String enterpriseId, EventType eventType) {
            this.configurationId = configurationId;
            this.enterpriseId = enterpriseId;
            this.eventType = eventType;
            this.timestamp = System.currentTimeMillis();
        }

        public Long getConfigurationId() {
            return configurationId;
        }

        public String getEnterpriseId() {
            return enterpriseId;
        }

        public EventType getEventType() {
            return eventType;
        }

        public long getTimestamp() {
            return timestamp;
        }

        @Override
        public String toString() {
            return String.format("ConfigurationChangeEvent{configId=%d, enterpriseId='%s', type=%s, timestamp=%d}",
                    configurationId, enterpriseId, eventType, timestamp);
        }
    }

    public enum EventType {
        CREATED, UPDATED, DELETED, ENABLED, DISABLED, ENTERPRISE_UPDATED
    }

    // ==================== Result DTOs ====================

    public record SyncResult(
            String enterpriseId,
            int configurationCount,
            long durationMs,
            String status,
            String message,
            long timestamp) {
    }

    public record ConfigurationSyncResult(
            Long configurationId,
            String status,
            String message,
            long durationMs,
            long timestamp) {
    }

    public record SyncStatus(
            long totalSyncCount,
            long lastSyncTime,
            int enterpriseCount,
            int configurationCount,
            long timestamp) {
    }

    public record EnterpriseSyncStatus(
            String enterpriseId,
            int configurationCount,
            long lastSyncTime,
            boolean isSynced,
            long timestamp) {
    }
}
