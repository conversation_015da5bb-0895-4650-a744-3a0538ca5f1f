package com.czb.hn.service.business.impl;

import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.enums.SubscriptionStatus;
import com.czb.hn.exception.SubscriptionExpiredException;
import com.czb.hn.exception.SubscriptionNotFoundException;
import com.czb.hn.jpa.securadar.repository.EnterpriseSubscriptionRepository;
import com.czb.hn.service.business.BillingService;
import com.czb.hn.util.EnterpriseIdentifierUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Core billing service implementation
 * Provides enterprise subscription management and access control
 */
@Service
@Slf4j
@Transactional
public class BillingServiceImpl implements BillingService {

    @Autowired
    private EnterpriseSubscriptionRepository subscriptionRepository;

    // ==================== 核心权限检查方法 ====================

    @Override
    @Cacheable(value = "enterpriseAccess", key = "#enterpriseIdentifier")
    public boolean hasAccess(String enterpriseIdentifier) {
        log.debug("Checking access for enterprise: {}", enterpriseIdentifier);

        if (enterpriseIdentifier == null || enterpriseIdentifier.trim().isEmpty()) {
            log.warn("Empty enterprise identifier provided");
            return false;
        }

        try {
            String normalizedIdentifier = EnterpriseIdentifierUtils.normalizeIdentifier(enterpriseIdentifier);
            Optional<EnterpriseSubscription> subscription = subscriptionRepository
                    .findByEnterpriseIdOrCreditCode(normalizedIdentifier);

            if (subscription.isEmpty()) {
                log.warn("No subscription found for enterprise: {}", normalizedIdentifier);
                return false;
            }

            EnterpriseSubscription sub = subscription.get();
            boolean hasAccess = sub.isActiveAndValid();

            log.debug("Access check result for enterprise {}: {}, status: {}, end date: {}",
                    normalizedIdentifier, hasAccess, sub.getStatus(), sub.getEndDate());

            return hasAccess;

        } catch (Exception e) {
            log.error("Error checking access for enterprise: {}", enterpriseIdentifier, e);
            return false;
        }
    }

    @Override
    public boolean hasActiveSubscription(String enterpriseIdentifier) {
        if (enterpriseIdentifier == null || enterpriseIdentifier.trim().isEmpty()) {
            return false;
        }

        String normalizedIdentifier = EnterpriseIdentifierUtils.normalizeIdentifier(enterpriseIdentifier);
        return subscriptionRepository.hasActiveSubscriptionByIdentifier(normalizedIdentifier);
    }

    @Override
    public void validateAccess(String enterpriseIdentifier) {
        log.debug("Validating access for enterprise: {}", enterpriseIdentifier);

        if (enterpriseIdentifier == null || enterpriseIdentifier.trim().isEmpty()) {
            throw new SubscriptionNotFoundException(enterpriseIdentifier, "企业标识不能为空");
        }

        String normalizedIdentifier = EnterpriseIdentifierUtils.normalizeIdentifier(enterpriseIdentifier);
        Optional<EnterpriseSubscription> subscription = subscriptionRepository
                .findByEnterpriseIdOrCreditCode(normalizedIdentifier);

        if (subscription.isEmpty()) {
            throw new SubscriptionNotFoundException(normalizedIdentifier);
        }

        EnterpriseSubscription sub = subscription.get();
        if (!sub.isActiveAndValid()) {
            if (sub.getStatus() != SubscriptionStatus.ACTIVE) {
                throw new SubscriptionExpiredException(normalizedIdentifier, sub.getEndDate(),
                        String.format("企业订阅状态异常: %s", sub.getStatus().getDescription()));
            } else {
                throw new SubscriptionExpiredException(normalizedIdentifier, sub.getEndDate());
            }
        }
    }

    // ==================== 订阅查询方法 ====================

    @Override
    @Transactional(readOnly = true)
    public Optional<EnterpriseSubscription> getCurrentSubscription(String enterpriseIdentifier) {
        if (enterpriseIdentifier == null || enterpriseIdentifier.trim().isEmpty()) {
            return Optional.empty();
        }

        String normalizedIdentifier = EnterpriseIdentifierUtils.normalizeIdentifier(enterpriseIdentifier);
        return subscriptionRepository.findByEnterpriseIdOrCreditCode(normalizedIdentifier);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EnterpriseSubscription> getSubscriptionByEnterpriseId(String enterpriseId) {
        if (enterpriseId == null || enterpriseId.trim().isEmpty()) {
            return Optional.empty();
        }
        return subscriptionRepository.findByEnterpriseId(enterpriseId.trim());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EnterpriseSubscription> getSubscriptionByCreditCode(String creditCode) {
        if (creditCode == null || creditCode.trim().isEmpty()) {
            return Optional.empty();
        }

        String normalizedCode = EnterpriseIdentifierUtils.normalizeIdentifier(creditCode);
        if (!EnterpriseIdentifierUtils.isValidCreditCode(normalizedCode)) {
            log.warn("Invalid credit code format: {}", creditCode);
            return Optional.empty();
        }

        return subscriptionRepository.findByEnterpriseCreditCode(normalizedCode);
    }

    // ==================== 订阅管理方法 ====================

    @Override
    @CacheEvict(value = "enterpriseAccess", key = "#enterpriseId")
    public EnterpriseSubscription createSubscription(String enterpriseId, String creditCode,
            LocalDate startDate, LocalDate endDate) {
        log.info("Creating subscription for enterprise: {}, creditCode: {}", enterpriseId, creditCode);

        if (enterpriseId == null || enterpriseId.trim().isEmpty()) {
            throw new IllegalArgumentException("企业ID不能为空");
        }

        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("订阅开始和结束日期不能为空");
        }

        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        // 检查是否已存在订阅
        if (subscriptionRepository.existsByEnterpriseId(enterpriseId.trim())) {
            throw new IllegalArgumentException("企业已存在订阅记录: " + enterpriseId);
        }

        if (creditCode != null && !creditCode.trim().isEmpty()) {
            String normalizedCode = EnterpriseIdentifierUtils.normalizeIdentifier(creditCode);
            if (!EnterpriseIdentifierUtils.isValidCreditCode(normalizedCode)) {
                throw new IllegalArgumentException("无效的企业信用代码格式: " + creditCode);
            }
            if (subscriptionRepository.existsByEnterpriseCreditCode(normalizedCode)) {
                throw new IllegalArgumentException("企业信用代码已存在订阅记录: " + creditCode);
            }
        }

        EnterpriseSubscription subscription = EnterpriseSubscription.builder()
                .enterpriseId(enterpriseId.trim())
                .enterpriseCreditCode(
                        creditCode != null ? EnterpriseIdentifierUtils.normalizeIdentifier(creditCode) : null)
                .startDate(startDate)
                .endDate(endDate)
                .status(SubscriptionStatus.ACTIVE)
                .autoRenew(false)
                .build();

        EnterpriseSubscription saved = subscriptionRepository.save(subscription);
        log.info("Created subscription with ID: {} for enterprise: {}", saved.getId(), enterpriseId);

        return saved;
    }

    @Override
    @CacheEvict(value = "enterpriseAccess", key = "#enterpriseIdentifier")
    public EnterpriseSubscription extendSubscription(String enterpriseIdentifier, LocalDate newEndDate) {
        log.info("Extending subscription for enterprise: {} to {}", enterpriseIdentifier, newEndDate);

        if (newEndDate == null) {
            throw new IllegalArgumentException("新的结束日期不能为空");
        }

        EnterpriseSubscription subscription = getCurrentSubscription(enterpriseIdentifier)
                .orElseThrow(() -> new SubscriptionNotFoundException(enterpriseIdentifier));

        if (newEndDate.isBefore(subscription.getStartDate())) {
            throw new IllegalArgumentException("新的结束日期不能早于开始日期");
        }

        subscription.setEndDate(newEndDate);
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        subscription.setUpdatedAt(LocalDateTime.now());

        EnterpriseSubscription saved = subscriptionRepository.save(subscription);
        log.info("Extended subscription for enterprise: {} to {}", enterpriseIdentifier, newEndDate);

        return saved;
    }

    @Override
    @CacheEvict(value = "enterpriseAccess", key = "#enterpriseIdentifier")
    public EnterpriseSubscription cancelSubscription(String enterpriseIdentifier) {
        log.info("Cancelling subscription for enterprise: {}", enterpriseIdentifier);

        EnterpriseSubscription subscription = getCurrentSubscription(enterpriseIdentifier)
                .orElseThrow(() -> new SubscriptionNotFoundException(enterpriseIdentifier));

        subscription.setStatus(SubscriptionStatus.CANCELLED);
        subscription.setUpdatedAt(LocalDateTime.now());

        EnterpriseSubscription saved = subscriptionRepository.save(subscription);
        log.info("Cancelled subscription for enterprise: {}", enterpriseIdentifier);

        return saved;
    }

    @Override
    @CacheEvict(value = "enterpriseAccess", key = "#enterpriseIdentifier")
    public EnterpriseSubscription reactivateSubscription(String enterpriseIdentifier, LocalDate newEndDate) {
        log.info("Reactivating subscription for enterprise: {} until {}", enterpriseIdentifier, newEndDate);

        if (newEndDate == null) {
            throw new IllegalArgumentException("新的结束日期不能为空");
        }

        EnterpriseSubscription subscription = getCurrentSubscription(enterpriseIdentifier)
                .orElseThrow(() -> new SubscriptionNotFoundException(enterpriseIdentifier));

        LocalDate today = LocalDate.now();
        if (newEndDate.isBefore(today)) {
            throw new IllegalArgumentException("新的结束日期不能早于今天");
        }

        subscription.setStatus(SubscriptionStatus.ACTIVE);
        subscription.setEndDate(newEndDate);
        subscription.setUpdatedAt(LocalDateTime.now());

        EnterpriseSubscription saved = subscriptionRepository.save(subscription);
        log.info("Reactivated subscription for enterprise: {} until {}", enterpriseIdentifier, newEndDate);

        return saved;
    }

    // ==================== 批量操作和统计方法 ====================

    @Override
    @Transactional(readOnly = true)
    public List<EnterpriseSubscription> getExpiringSubscriptions(int days) {
        log.debug("Getting subscriptions expiring within {} days", days);
        LocalDate endDate = LocalDate.now().plusDays(days);
        return subscriptionRepository.findExpiringWithinDays(endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Long> getSubscriptionStatistics() {
        log.debug("Getting subscription statistics");

        Map<String, Long> stats = new HashMap<>();
        stats.put("total", subscriptionRepository.count());
        stats.put("active", subscriptionRepository.countActiveSubscriptions());
        stats.put("expired", subscriptionRepository.countExpiredSubscriptions());

        // 计算即将过期的订阅数量（7天内）
        LocalDate sevenDaysLater = LocalDate.now().plusDays(7);
        stats.put("expiring_soon", (long) subscriptionRepository.findExpiringWithinDays(sevenDaysLater).size());

        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public List<EnterpriseSubscription> getSubscriptionsByStatus(SubscriptionStatus status) {
        log.debug("Getting subscriptions by status: {}", status);
        return subscriptionRepository.findByStatus(status);
    }
}
