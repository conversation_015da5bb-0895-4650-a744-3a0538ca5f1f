package com.czb.hn.service.business;

import com.czb.hn.dto.user.GroupInfo;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 部门/组织信息缓存服务接口
 * 负责获取、缓存和管理OnePass的部门/组织信息
 */
public interface GroupCacheService {
    
    /**
     * 获取所有组织/部门信息
     * @return 所有组织/部门信息列表
     */
    List<GroupInfo> getAllGroups();
    
    /**
     * 根据组织ID获取组织/部门信息
     * @param groupId 组织ID
     * @return 组织/部门信息（可能为空）
     */
    Optional<GroupInfo> getGroupById(String groupId);
    
    /**
     * 根据组织代码获取组织/部门信息
     * @param groupCode 组织代码
     * @return 组织/部门信息（可能为空）
     */
    Optional<GroupInfo> getGroupByCode(String groupCode);
    
    /**
     * 获取组织ID与组织代码的映射关系
     * @return ID-代码映射
     */
    Map<String, String> getGroupIdToCodeMapping();
    
    /**
     * 强制刷新缓存
     */
    void refreshCache();
} 