package com.czb.hn.service.business.impl;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.service.business.KeywordTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Keyword Tracking Service Implementation
 * Extends SinaNewsMonitor patterns for alert processing
 * Works with Elasticsearch documents as the primary data source
 */
@Service
@Slf4j
public class KeywordTrackingServiceImpl implements KeywordTrackingService {

    @Override
    public List<String> extractInvolvedKeywords(AlertConfigurationResponseDto config, SinaNewsDocument document) {
        String content = buildSearchableContent(document);
        List<String> involvedKeywords = new ArrayList<>();

        // Simple OR logic: check each keyword from the alert configuration
        for (String keyword : config.alertKeywords().keywords()) {
            if (keyword != null && !keyword.trim().isEmpty()) {
                String trimmedKeyword = keyword.trim();
                if (content.toLowerCase().contains(trimmedKeyword.toLowerCase())) {
                    involvedKeywords.add(trimmedKeyword);
                }
            }
        }

        return involvedKeywords;
    }

    @Override
    public List<Integer> findKeywordPositions(String content, String keyword) {
        List<Integer> positions = new ArrayList<>();

        if (content == null || keyword == null || content.isEmpty() || keyword.isEmpty()) {
            return positions;
        }

        String lowerContent = content.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        int index = 0;
        while ((index = lowerContent.indexOf(lowerKeyword, index)) != -1) {
            positions.add(index);
            index += lowerKeyword.length();
        }

        return positions;
    }

    @Override
    public Set<String> buildTargetKeywords(AlertConfigurationResponseDto config) {
        Set<String> targetKeywords = new HashSet<>();

        try {
            // Simple approach: just add all keywords from the alert configuration
            for (String keyword : config.alertKeywords().keywords()) {
                if (keyword != null && !keyword.trim().isEmpty()) {
                    targetKeywords.add(keyword.trim());
                }
            }

        } catch (Exception e) {
            log.error("Error building target keywords for configuration {}: {}",
                    config.id(), e.getMessage(), e);
        }

        return targetKeywords;
    }

    @Override
    public Map<String, Integer> extractHighlightKeywords(String content, Set<String> targetKeywords) {
        Map<String, Integer> keywordCounts = new HashMap<>();

        if (content == null || targetKeywords == null || content.isEmpty() || targetKeywords.isEmpty()) {
            return keywordCounts;
        }

        String lowerContent = content.toLowerCase();

        for (String keyword : targetKeywords) {
            String lowerKeyword = keyword.toLowerCase();
            int count = 0;
            int index = 0;

            while ((index = lowerContent.indexOf(lowerKeyword, index)) != -1) {
                count++;
                index += lowerKeyword.length();
            }

            if (count > 0) {
                keywordCounts.put(keyword, count);
            }
        }

        return keywordCounts;
    }

    /**
     * Build searchable content from Elasticsearch document
     * Similar to existing SinaNewsMonitor patterns
     */
    private String buildSearchableContent(SinaNewsDocument document) {
        StringBuilder content = new StringBuilder();

        if (document.getTitle() != null) {
            content.append(document.getTitle()).append(" ");
        }
        if (document.getContent() != null) {
            content.append(document.getContent()).append(" ");
        }
        if (document.getSummary() != null) {
            content.append(document.getSummary()).append(" ");
        }

        return content.toString();
    }

    /**
     * Extract keywords with highlighting patterns
     * Adapts existing SinaNewsMonitor highlighting logic
     */
    public List<Map<String, Integer>> extractHighlightKeywordsWithPositions(
            String content, Set<String> targetKeywords) {

        List<Map<String, Integer>> result = new ArrayList<>();

        // Pattern to match highlighted content (if any)
        Pattern pattern = Pattern.compile("<strong>(.*?)</strong>");

        if (content != null) {
            Matcher matcher = pattern.matcher(content);
            Map<String, Integer> keywordCounts = new HashMap<>();

            while (matcher.find()) {
                String highlightedText = matcher.group(1);
                if (targetKeywords.contains(highlightedText)) {
                    keywordCounts.merge(highlightedText, 1, Integer::sum);
                }
            }

            // Convert to list of maps format (matching existing patterns)
            for (Map.Entry<String, Integer> entry : keywordCounts.entrySet()) {
                Map<String, Integer> keywordMap = new HashMap<>();
                keywordMap.put(entry.getKey(), entry.getValue());
                result.add(keywordMap);
            }
        }

        return result;
    }
}
