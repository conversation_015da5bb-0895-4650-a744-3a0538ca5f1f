package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for consuming alert configurations
 * Provides optimized access to alert configurations for other modules
 * with caching and performance optimizations
 */
public interface AlertConfigurationConsumerService {

    /**
     * Get all active and enabled alert configurations
     * This method is optimized for frequent access by alert processing modules
     *
     * @return List of active and enabled configurations
     */
    List<AlertConfigurationResponseDto> getAllActiveConfigurations();

    /**
     * Get active and enabled alert configurations for a specific enterprise
     *
     * @param enterpriseId Enterprise ID
     * @return List of active and enabled configurations for the enterprise
     */
    List<AlertConfigurationResponseDto> getActiveConfigurationsByEnterprise(String enterpriseId);

    /**
     * Get active and enabled alert configurations for a specific plan
     *
     * @param planId Plan ID
     * @return List of active and enabled configurations for the plan
     */
    List<AlertConfigurationResponseDto> getActiveConfigurationsByPlan(Long planId);

    /**
     * Get a specific active configuration by ID
     *
     * @param configurationId Configuration ID
     * @return Optional containing the configuration if found and active
     */
    Optional<AlertConfigurationResponseDto> getActiveConfigurationById(Long configurationId);

    /**
     * Check if a configuration is currently active and enabled
     *
     * @param configurationId Configuration ID
     * @return True if the configuration is active and enabled
     */
    Boolean isConfigurationActiveAndEnabled(Long configurationId);

    /**
     * Get configurations that match specific content criteria
     * Used by alert processing to find relevant configurations for content
     *
     * @param contentKeywords Keywords found in content
     * @param sourceType Source type of the content
     * @param sensitivityType Sensitivity type of the content
     * @return List of matching configurations
     */
    List<AlertConfigurationResponseDto> getMatchingConfigurations(
            List<String> contentKeywords,
            String sourceType,
            String sensitivityType
    );

    /**
     * Get configurations by alert keywords
     * Finds configurations that contain any of the specified keywords
     *
     * @param keywords Keywords to search for
     * @return List of configurations containing the keywords
     */
    List<AlertConfigurationResponseDto> getConfigurationsByKeywords(List<String> keywords);

    /**
     * Refresh the configuration cache
     * Should be called when configurations are updated
     */
    void refreshCache();

    /**
     * Clear the configuration cache
     * Forces reload of all configurations on next access
     */
    void clearCache();

    /**
     * Get cache statistics
     *
     * @return Cache statistics information
     */
    CacheStatisticsDto getCacheStatistics();

    /**
     * Preload configurations into cache
     * Useful for warming up the cache during application startup
     */
    void preloadCache();

    /**
     * DTO for cache statistics
     */
    record CacheStatisticsDto(
        Long totalCachedConfigurations,
        Long cacheHits,
        Long cacheMisses,
        Double hitRatio,
        Long lastRefreshTime,
        Long cacheSize
    ) {}
}
