package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.*;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertConfigurationSnapshot;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationRepository;
import com.czb.hn.jpa.securadar.repository.AlertConfigurationSnapshotRepository;
import com.czb.hn.service.business.AlertConfigurationService;
import com.czb.hn.util.AlertConfigurationMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Implementation of AlertConfigurationService
 * Provides business logic for alert configuration management with automatic
 * snapshot creation
 */
@Service
@Transactional
public class AlertConfigurationServiceImpl implements AlertConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(AlertConfigurationServiceImpl.class);

    @Autowired
    private AlertConfigurationRepository alertConfigurationRepository;

    @Autowired
    private AlertConfigurationSnapshotRepository snapshotRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AlertConfigurationMapper configurationMapper;

    @Override
    public AlertConfigurationResponseDto createConfiguration(AlertConfigurationCreateDto createDto) {
        try {
            logger.info("Creating new alert configuration: {}", createDto.name());

            // Validate name uniqueness
            if (!isConfigurationNameAvailable(createDto.name(), createDto.enterpriseId(), null)) {
                throw new IllegalArgumentException("Configuration name already exists for this enterprise");
            }

            // Create and save the configuration entity
            AlertConfiguration configuration = configurationMapper.toEntity(createDto);
            configuration.setCurrentVersion(1);
            configuration.setIsActive(true);
            configuration.setLastSnapshotAt(LocalDateTime.now());

            AlertConfiguration savedConfiguration = alertConfigurationRepository.save(configuration);

            // Create the initial snapshot
            createSnapshot(savedConfiguration, "CREATE", createDto.createdBy(), createDto.changeReason());

            logger.info("Successfully created alert configuration with ID: {}", savedConfiguration.getId());
            return configurationMapper.toResponseDto(savedConfiguration);

        } catch (Exception e) {
            logger.error("Error creating alert configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create alert configuration: " + e.getMessage(), e);
        }
    }

    @Override
    public AlertConfigurationResponseDto updateConfiguration(Long id, AlertConfigurationUpdateDto updateDto) {
        try {
            logger.info("Updating alert configuration with ID: {}", id);

            AlertConfiguration existingConfiguration = alertConfigurationRepository.findActiveById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Alert configuration not found with ID: " + id));

            // Validate name uniqueness if name is being updated
            if (updateDto.name() != null && !updateDto.name().equals(existingConfiguration.getName())) {
                if (!isConfigurationNameAvailable(updateDto.name(), existingConfiguration.getEnterpriseId(), id)) {
                    throw new IllegalArgumentException("Configuration name already exists for this enterprise");
                }
            }

            // Update the configuration
            configurationMapper.updateEntityFromDto(updateDto, existingConfiguration);
            existingConfiguration.setCurrentVersion(existingConfiguration.getCurrentVersion() + 1);
            existingConfiguration.setLastSnapshotAt(LocalDateTime.now());

            AlertConfiguration savedConfiguration = alertConfigurationRepository.save(existingConfiguration);

            // Create a new snapshot
            createSnapshot(savedConfiguration, "UPDATE", updateDto.updatedBy(), updateDto.changeReason());

            logger.info("Successfully updated alert configuration with ID: {}", id);
            return configurationMapper.toResponseDto(savedConfiguration);

        } catch (Exception e) {
            logger.error("Error updating alert configuration with ID {}: {}", id, e.getMessage(), e);
            throw new RuntimeException("Failed to update alert configuration: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AlertConfigurationResponseDto getConfigurationById(Long id) {
        AlertConfiguration configuration = alertConfigurationRepository.findActiveById(id)
                .orElseThrow(() -> new IllegalArgumentException("Alert configuration not found with ID: " + id));
        return configurationMapper.toResponseDto(configuration);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AlertConfigurationResponseDto> getAllConfigurations(Pageable pageable) {
        Page<AlertConfiguration> configurations = alertConfigurationRepository.findAll(pageable);
        return configurations.map(configurationMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AlertConfigurationResponseDto> getConfigurationsByEnterpriseId(String enterpriseId) {
        List<AlertConfiguration> configurations = alertConfigurationRepository.findByEnterpriseId(enterpriseId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AlertConfigurationResponseDto> getConfigurationsByPlanId(Long planId) {
        List<AlertConfiguration> configurations = alertConfigurationRepository.findByPlanId(planId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AlertConfigurationResponseDto> getEnabledConfigurationsByEnterpriseId(String enterpriseId) {
        List<AlertConfiguration> configurations = alertConfigurationRepository.findEnabledByEnterpriseId(enterpriseId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AlertConfigurationResponseDto> getEnabledConfigurationsByPlanId(Long planId) {
        List<AlertConfiguration> configurations = alertConfigurationRepository.findEnabledByPlanId(planId);
        return configurations.stream()
                .map(configurationMapper::toResponseDto)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AlertConfigurationResponseDto> searchConfigurationsByName(String namePattern, Pageable pageable) {
        Page<AlertConfiguration> configurations = alertConfigurationRepository.findByNameContaining(namePattern,
                pageable);
        return configurations.map(configurationMapper::toResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AlertConfigurationResponseDto> getConfigurationsByCreatedDateRange(LocalDateTime startDate,
            LocalDateTime endDate, Pageable pageable) {
        Page<AlertConfiguration> configurations = alertConfigurationRepository.findByCreatedAtBetween(startDate,
                endDate, pageable);
        return configurations.map(configurationMapper::toResponseDto);
    }

    @Override
    public AlertConfigurationResponseDto toggleConfiguration(Long id, Boolean enabled, String updatedBy,
            String changeReason) {
        try {
            logger.info("Toggling alert configuration {} to enabled: {}", id, enabled);

            AlertConfiguration configuration = alertConfigurationRepository.findActiveById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Alert configuration not found with ID: " + id));

            configuration.setEnabled(enabled);
            configuration.setUpdatedBy(updatedBy);
            configuration.setCurrentVersion(configuration.getCurrentVersion() + 1);
            configuration.setLastSnapshotAt(LocalDateTime.now());

            AlertConfiguration savedConfiguration = alertConfigurationRepository.save(configuration);

            // Create a snapshot for the toggle operation
            String operation = enabled ? "ENABLE" : "DISABLE";
            createSnapshot(savedConfiguration, operation, updatedBy, changeReason);

            logger.info("Successfully toggled alert configuration {} to enabled: {}", id, enabled);
            return configurationMapper.toResponseDto(savedConfiguration);

        } catch (Exception e) {
            logger.error("Error toggling alert configuration {}: {}", id, e.getMessage(), e);
            throw new RuntimeException("Failed to toggle alert configuration: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteConfiguration(Long id, String deletedBy, String deleteReason) {
        try {
            logger.info("Soft deleting alert configuration with ID: {}", id);

            AlertConfiguration configuration = alertConfigurationRepository.findActiveById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Alert configuration not found with ID: " + id));

            // Create a final snapshot before deletion
            createSnapshot(configuration, "DELETE", deletedBy, deleteReason);

            // Perform soft delete
            // todo 后期添加当前操作用户
            if (deletedBy == null) {
                deletedBy = "SYSTEM";
            }
            alertConfigurationRepository.softDeleteById(id, deletedBy);

            logger.info("Successfully soft deleted alert configuration with ID: {}", id);

        } catch (Exception e) {
            logger.error("Error deleting alert configuration {}: {}", id, e.getMessage(), e);
            throw new RuntimeException("Failed to delete alert configuration: " + e.getMessage(), e);
        }
    }

    /**
     * Create a snapshot of the current configuration state
     */
    private AlertConfigurationSnapshot createSnapshot(AlertConfiguration configuration, String operationType,
            String createdBy, String changeReason) {
        try {
            // Convert configuration to JSON
            String snapshotData = configurationMapper.toSnapshotJson(configuration);

            // Calculate data size and checksum
            byte[] dataBytes = snapshotData.getBytes(StandardCharsets.UTF_8);
            Long dataSize = (long) dataBytes.length;
            String checksum = calculateChecksum(dataBytes);

            // Deactivate existing snapshots
            snapshotRepository.deactivateAllByConfigurationId(configuration.getId());

            // Create new snapshot
            AlertConfigurationSnapshot snapshot = new AlertConfigurationSnapshot();
            snapshot.setConfigurationId(configuration.getId());
            snapshot.setVersionNumber(configuration.getCurrentVersion());
            snapshot.setSnapshotData(snapshotData);
            snapshot.setCreatedBy(createdBy);
            snapshot.setChangeReason(changeReason);
            snapshot.setIsActive(true);
            snapshot.setOperationType(operationType);
            snapshot.setDataSize(dataSize);
            snapshot.setChecksum(checksum);

            AlertConfigurationSnapshot savedSnapshot = snapshotRepository.save(snapshot);
            logger.debug("Created snapshot {} for configuration {}", savedSnapshot.getId(), configuration.getId());

            return savedSnapshot;

        } catch (Exception e) {
            logger.error("Error creating snapshot for configuration {}: {}", configuration.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to create configuration snapshot", e);
        }
    }

    /**
     * Calculate SHA-256 checksum for data integrity
     */
    private String calculateChecksum(byte[] data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            logger.warn("Error calculating checksum: {}", e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AlertConfigurationSnapshotDto> getConfigurationSnapshots(Long configurationId, Pageable pageable) {
        Page<AlertConfigurationSnapshot> snapshots = snapshotRepository.findByConfigurationId(configurationId,
                pageable);
        return snapshots.map(configurationMapper::toSnapshotDto);
    }

    @Override
    @Transactional(readOnly = true)
    public AlertConfigurationSnapshotDto getConfigurationSnapshot(Long configurationId, Integer version) {
        AlertConfigurationSnapshot snapshot = snapshotRepository
                .findByConfigurationIdAndVersion(configurationId, version)
                .orElseThrow(() -> new IllegalArgumentException(
                        "Snapshot not found for configuration " + configurationId + " version " + version));
        return configurationMapper.toSnapshotDto(snapshot);
    }

    @Override
    @Transactional(readOnly = true)
    public AlertConfigurationSnapshotDto getActiveSnapshot(Long configurationId) {
        AlertConfigurationSnapshot snapshot = snapshotRepository.findActiveByConfigurationId(configurationId)
                .orElseThrow(() -> new IllegalArgumentException(
                        "No active snapshot found for configuration " + configurationId));
        return configurationMapper.toSnapshotDto(snapshot);
    }

    @Override
    public AlertConfigurationResponseDto rollbackToVersion(Long configurationId, Integer targetVersion,
            String rolledBackBy, String rollbackReason) {
        try {
            logger.info("Rolling back configuration {} to version {}", configurationId, targetVersion);

            // Get current configuration
            AlertConfiguration currentConfiguration = alertConfigurationRepository.findActiveById(configurationId)
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Alert configuration not found with ID: " + configurationId));

            // Get target snapshot
            AlertConfigurationSnapshot targetSnapshot = snapshotRepository
                    .findByConfigurationIdAndVersion(configurationId, targetVersion)
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Target snapshot not found for version " + targetVersion));

            // Restore configuration from snapshot
            AlertConfiguration restoredConfiguration = configurationMapper
                    .fromSnapshotJson(targetSnapshot.getSnapshotData());

            // Update current configuration with restored data
            configurationMapper.copyConfigurationData(restoredConfiguration, currentConfiguration);
            currentConfiguration.setCurrentVersion(currentConfiguration.getCurrentVersion() + 1);
            currentConfiguration.setUpdatedBy(rolledBackBy);
            currentConfiguration.setLastSnapshotAt(LocalDateTime.now());

            AlertConfiguration savedConfiguration = alertConfigurationRepository.save(currentConfiguration);

            // Create rollback snapshot
            createSnapshot(savedConfiguration, "ROLLBACK", rolledBackBy,
                    "Rolled back to version " + targetVersion + ": " + rollbackReason);

            logger.info("Successfully rolled back configuration {} to version {}", configurationId, targetVersion);
            return configurationMapper.toResponseDto(savedConfiguration);

        } catch (Exception e) {
            logger.error("Error rolling back configuration {} to version {}: {}", configurationId, targetVersion,
                    e.getMessage(), e);
            throw new RuntimeException("Failed to rollback configuration: " + e.getMessage(), e);
        }
    }

    @Override
    public AlertConfigurationSnapshotDto createManualSnapshot(Long configurationId, String createdBy, String reason) {
        try {
            logger.info("Creating manual snapshot for configuration {}", configurationId);

            AlertConfiguration configuration = alertConfigurationRepository.findActiveById(configurationId)
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Alert configuration not found with ID: " + configurationId));

            AlertConfigurationSnapshot snapshot = createSnapshot(configuration, "MANUAL", createdBy, reason);

            logger.info("Successfully created manual snapshot {} for configuration {}", snapshot.getId(),
                    configurationId);
            return configurationMapper.toSnapshotDto(snapshot);

        } catch (Exception e) {
            logger.error("Error creating manual snapshot for configuration {}: {}", configurationId, e.getMessage(), e);
            throw new RuntimeException("Failed to create manual snapshot: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Boolean isConfigurationNameAvailable(String name, String enterpriseId, Long excludeId) {
        if (excludeId != null) {
            return !alertConfigurationRepository.existsByNameAndEnterpriseIdExcludingId(name, enterpriseId, excludeId);
        } else {
            return !alertConfigurationRepository.existsByNameAndEnterpriseId(name, enterpriseId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ConfigurationStatisticsDto getConfigurationStatistics(String enterpriseId) {
        Long totalConfigurations = alertConfigurationRepository.countByEnterpriseId(enterpriseId);
        Long enabledConfigurations = alertConfigurationRepository.countEnabledByEnterpriseId(enterpriseId);
        Long disabledConfigurations = totalConfigurations - enabledConfigurations;

        // Calculate snapshot statistics
        List<AlertConfiguration> configurations = alertConfigurationRepository.findByEnterpriseId(enterpriseId);
        Long totalSnapshots = configurations.stream()
                .mapToLong(config -> snapshotRepository.countByConfigurationId(config.getId()))
                .sum();

        Long averageSnapshotsPerConfiguration = totalConfigurations > 0 ? totalSnapshots / totalConfigurations : 0L;

        return new ConfigurationStatisticsDto(
                totalConfigurations,
                enabledConfigurations,
                disabledConfigurations,
                totalSnapshots,
                averageSnapshotsPerConfiguration);
    }

}
