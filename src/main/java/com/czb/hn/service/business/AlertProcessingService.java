package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertResultResponseDto;

import java.util.List;

/**
 * Alert Processing Service Interface
 * Processes Elasticsearch documents against alert configurations to generate
 * alerts
 * Supports multi-tenant isolation and batch processing
 */
public interface AlertProcessingService {

    /**
     * Process alerts for all active enterprises (scheduled task)
     */
    void processAlerts();

    /**
     * Process alerts for a specific enterprise
     * 
     * @param enterpriseId Enterprise ID for tenant isolation
     * @return List of generated alert results
     */
    List<AlertResultResponseDto> processEnterpriseAlerts(String enterpriseId);

    /**
     * Process alerts for a specific plan
     * 
     * @param planId Plan ID
     * @return List of generated alert results
     */
    List<AlertResultResponseDto> processPlanAlerts(Long planId);

    /**
     * Process alerts for a specific configuration
     * 
     * @param configurationId Configuration ID
     * @return List of generated alert results
     */
    List<AlertResultResponseDto> processConfigurationAlerts(Long configurationId);

    /**
     * Get list of active enterprises for processing
     * 
     * @return List of enterprise IDs
     */
    List<String> getActiveEnterprises();

    /**
     * Get processing statistics for monitoring
     * 
     * @param enterpriseId Enterprise ID
     * @return Processing statistics
     */
    ProcessingStatistics getProcessingStatistics(String enterpriseId);

    /**
     * Processing statistics record
     */
    record ProcessingStatistics(
            String enterpriseId,
            int totalDocumentsProcessed,
            int alertsGenerated,
            int configurationsProcessed,
            long processingTimeMs,
            String status) {
    }
}
