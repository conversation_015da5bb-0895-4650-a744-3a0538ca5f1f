package com.czb.hn.service.business;

import com.czb.hn.dto.monitor.MonitorConfigurationResponseDto;
import com.czb.hn.dto.monitor.MonitorConfigurationUpdateDto;

public interface MonitorConfigurationService {

    /**
     * 根据方案id获取监控配置
     *
     * @param planId
     * @return MonitorConfigurationResponseDto
     */
    MonitorConfigurationResponseDto getConfigurationsByPlanId(Long planId);

    /**
     * 根据方案id更新监控配置
     *
     * @param planId
     * @param updateDto
     * @return MonitorConfigurationResponseDto
     */
    MonitorConfigurationResponseDto updateConfiguration(Long planId, MonitorConfigurationUpdateDto updateDto);
}
