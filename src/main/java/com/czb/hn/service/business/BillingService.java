package com.czb.hn.service.business;

import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.enums.SubscriptionStatus;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Core billing service interface
 * Provides enterprise subscription management and access control
 */
public interface BillingService {

    // ==================== 核心权限检查方法 ====================

    /**
     * Check if enterprise has access (most important method)
     * Supports both enterprise ID and credit code
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @return true if has access, false otherwise
     */
    boolean hasAccess(String enterpriseIdentifier);

    /**
     * Check if enterprise has active subscription
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @return true if has active subscription, false otherwise
     */
    boolean hasActiveSubscription(String enterpriseIdentifier);

    /**
     * Validate access and throw exception if no access
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @throws com.czb.hn.exception.SubscriptionExpiredException  if subscription
     *                                                            expired
     * @throws com.czb.hn.exception.SubscriptionNotFoundException if subscription
     *                                                            not found
     */
    void validateAccess(String enterpriseIdentifier);

    // ==================== 订阅查询方法 ====================

    /**
     * Get current subscription for enterprise
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @return subscription if found
     */
    Optional<EnterpriseSubscription> getCurrentSubscription(String enterpriseIdentifier);

    /**
     * Get subscription by enterprise ID
     * 
     * @param enterpriseId enterprise ID
     * @return subscription if found
     */
    Optional<EnterpriseSubscription> getSubscriptionByEnterpriseId(String enterpriseId);

    /**
     * Get subscription by credit code
     * 
     * @param creditCode enterprise credit code
     * @return subscription if found
     */
    Optional<EnterpriseSubscription> getSubscriptionByCreditCode(String creditCode);

    // ==================== 订阅管理方法 ====================

    /**
     * Create new subscription for enterprise
     * 
     * @param enterpriseId enterprise ID
     * @param creditCode   enterprise credit code (optional)
     * @param startDate    subscription start date
     * @param endDate      subscription end date
     * @return created subscription
     */
    EnterpriseSubscription createSubscription(String enterpriseId, String creditCode, LocalDate startDate,
            LocalDate endDate);

    /**
     * Extend subscription end date
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @param newEndDate           new end date
     * @return updated subscription
     */
    EnterpriseSubscription extendSubscription(String enterpriseIdentifier, LocalDate newEndDate);

    /**
     * Cancel subscription
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @return updated subscription
     */
    EnterpriseSubscription cancelSubscription(String enterpriseIdentifier);

    /**
     * Reactivate subscription
     * 
     * @param enterpriseIdentifier enterprise ID or credit code
     * @param newEndDate           new end date
     * @return updated subscription
     */
    EnterpriseSubscription reactivateSubscription(String enterpriseIdentifier, LocalDate newEndDate);

    // ==================== 批量操作和统计方法 ====================

    /**
     * Get subscriptions expiring within specified days
     * 
     * @param days number of days
     * @return list of expiring subscriptions
     */
    List<EnterpriseSubscription> getExpiringSubscriptions(int days);

    /**
     * Get subscription statistics
     * 
     * @return statistics map
     */
    java.util.Map<String, Long> getSubscriptionStatistics();

    /**
     * Get all subscriptions by status
     * 
     * @param status subscription status
     * @return list of subscriptions
     */
    List<EnterpriseSubscription> getSubscriptionsByStatus(SubscriptionStatus status);
}
