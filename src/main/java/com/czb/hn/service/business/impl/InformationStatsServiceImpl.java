package com.czb.hn.service.business.impl;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.workbench.InformationStatsDTO;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.service.business.InformationStatsService;
import com.czb.hn.service.business.PlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 信息统计服务实现类
 */
@Service
public class InformationStatsServiceImpl implements InformationStatsService {

        private static final Logger logger = LoggerFactory.getLogger(InformationStatsServiceImpl.class);
        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        @Autowired
        private ElasticsearchSearchService elasticsearchSearchService;

        @Autowired
        private AlertResultRepository alertResultRepository;

        @Autowired
        private PlanService planService;

        @Override
        public InformationStatsDTO getInformationStats(Long planId, LocalDateTime startTime, LocalDateTime endTime) {
                try {
                        logger.info("Getting information stats for plan: {}, time range: {} to {}", planId, startTime,
                                        endTime);

                        // 获取方案信息
                        PlanDTO planDto = planService.getPlanById(planId);
                        String planName = planDto.name();

                        // 格式化时间
                        String startTimeStr = startTime.format(FORMATTER);
                        String endTimeStr = endTime.format(FORMATTER);

                        // 统计舆情信息总量
                        Long totalInformationCount = elasticsearchSearchService.countInformationTotal(planId,
                                        startTimeStr,
                                        endTimeStr);

                        // 统计舆情敏感信息总量
                        Long sensitiveInformationCount = elasticsearchSearchService.countSensitiveInformationTotal(
                                        planId,
                                        startTimeStr, endTimeStr);

                        // 统计预警信息总量
                        Long alertCount = alertResultRepository.countByPlanIdAndWarningTimeBetween(planId, startTime,
                                        endTime);

                        logger.info("Information stats for plan {}: total={}, sensitive={}, alerts={}",
                                        planName, totalInformationCount, sensitiveInformationCount, alertCount);

                        return new InformationStatsDTO(
                                        startTime,
                                        endTime,
                                        planId,
                                        planName,
                                        totalInformationCount,
                                        sensitiveInformationCount,
                                        alertCount);

                } catch (Exception e) {
                        logger.error("Error getting information stats for plan {}: {}", planId, e.getMessage(), e);
                        throw new RuntimeException("Failed to get information statistics: " + e.getMessage(), e);
                }
        }

        @Override
        public InformationStatsDTO getEnterpriseInformationStats(String enterpriseId, LocalDateTime startTime,
                        LocalDateTime endTime) {
                try {
                        logger.info("Getting enterprise information stats for enterprise: {}, time range: {} to {}",
                                        enterpriseId, startTime, endTime);

                        // 获取企业下所有方案
                        List<PlanDTO> plans = planService.getPlansByEnterpriseId(enterpriseId);

                        if (plans.isEmpty()) {
                                logger.warn("No plans found for enterprise: {}", enterpriseId);
                                return new InformationStatsDTO(
                                                startTime,
                                                endTime,
                                                null,
                                                "企业下所有方案",
                                                0L,
                                                0L,
                                                0L);
                        }

                        // 格式化时间
                        String startTimeStr = startTime.format(FORMATTER);
                        String endTimeStr = endTime.format(FORMATTER);

                        // 提取方案ID列表
                        List<Long> planIds = plans.stream().map(PlanDTO::id).collect(Collectors.toList());

                        // 批量统计舆情信息，提高性能
                        Long totalInformationCount = 0L;
                        Long sensitiveInformationCount = 0L;

                        try {
                                Map<Long, Long> totalInfoCounts = elasticsearchSearchService.batchCountInformationTotal(
                                                planIds,
                                                startTimeStr, endTimeStr);
                                totalInformationCount = totalInfoCounts.values().stream().mapToLong(Long::longValue)
                                                .sum();
                        } catch (Exception e) {
                                logger.warn("Error getting batch total information counts: {}", e.getMessage());
                                // 降级到单个查询
                                for (Long planId : planIds) {
                                        try {
                                                Long count = elasticsearchSearchService.countInformationTotal(planId,
                                                                startTimeStr, endTimeStr);
                                                totalInformationCount += count;
                                        } catch (Exception ex) {
                                                logger.warn("Error getting total information count for plan {}: {}",
                                                                planId, ex.getMessage());
                                        }
                                }
                        }

                        try {
                                Map<Long, Long> sensitiveInfoCounts = elasticsearchSearchService
                                                .batchCountSensitiveInformationTotal(planIds, startTimeStr, endTimeStr);
                                sensitiveInformationCount = sensitiveInfoCounts.values().stream()
                                                .mapToLong(Long::longValue).sum();
                        } catch (Exception e) {
                                logger.warn("Error getting batch sensitive information counts: {}", e.getMessage());
                                // 降级到单个查询
                                for (Long planId : planIds) {
                                        try {
                                                Long count = elasticsearchSearchService.countSensitiveInformationTotal(
                                                                planId, startTimeStr, endTimeStr);
                                                sensitiveInformationCount += count;
                                        } catch (Exception ex) {
                                                logger.warn("Error getting sensitive information count for plan {}: {}",
                                                                planId, ex.getMessage());
                                        }
                                }
                        }

                        // 批量统计预警信息（数据库查询）
                        Long alertCount = 0L;
                        try {
                                List<Object[]> alertCounts = alertResultRepository
                                                .batchCountByPlanIdsAndWarningTimeBetween(planIds,
                                                                startTime, endTime);
                                alertCount = alertCounts.stream()
                                                .mapToLong(result -> ((Number) result[1]).longValue())
                                                .sum();
                        } catch (Exception e) {
                                logger.warn("Error getting batch alert counts: {}", e.getMessage());
                                // 降级到单个查询
                                for (Long planId : planIds) {
                                        try {
                                                Long planAlertCount = alertResultRepository
                                                                .countByPlanIdAndWarningTimeBetween(planId,
                                                                                startTime, endTime);
                                                alertCount += planAlertCount;
                                        } catch (Exception ex) {
                                                logger.warn("Error getting alert count for plan {}: {}", planId,
                                                                ex.getMessage());
                                        }
                                }
                        }

                        logger.debug("Batch stats completed: {} plans processed, total={}, sensitive={}, alerts={}",
                                        plans.size(), totalInformationCount, sensitiveInformationCount, alertCount);

                        logger.info("Enterprise {} total stats: total={}, sensitive={}, alerts={}",
                                        enterpriseId, totalInformationCount, sensitiveInformationCount, alertCount);

                        return new InformationStatsDTO(
                                        startTime,
                                        endTime,
                                        null,
                                        "企业下所有方案",
                                        totalInformationCount,
                                        sensitiveInformationCount,
                                        alertCount);

                } catch (Exception e) {
                        logger.error("Error getting enterprise information stats for enterprise {}: {}", enterpriseId,
                                        e.getMessage(), e);
                        throw new RuntimeException("Failed to get enterprise information statistics: " + e.getMessage(),
                                        e);
                }
        }
}
