package com.czb.hn.service.job;

import com.czb.hn.jpa.securadar.entity.JobEntity;
import java.util.List;

/**
 * 任务服务接口
 */
public interface JobService {
    /** 新建任务 */
    JobEntity createJob(JobEntity job);
    /** 更新任务 */
    JobEntity updateJob(JobEntity job);
    /** 删除任务 */
    void deleteJob(Long id);
    /** 启用任务 */
    void enableJob(Long id);
    /** 禁用任务 */
    void disableJob(Long id);
    /** 根据ID查询任务 */
    JobEntity getJobById(Long id);
    /** 查询所有任务 */
    List<JobEntity> getAllJobs();

    /**
     * 按方案ID查找所有任务
     * 
     * @param planId 方案ID
     * @return 任务列表
     */
    List<JobEntity> getJobsByPlanId(Long planId);


} 