package com.czb.hn.service.job.impl;

import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.service.job.JobInfo;

/**
 * JobInfo接口实现类
 */
public class JobInfoImpl implements JobInfo {
    private final Long id;
    private final String name;
    private String jobParams;
    private final String cycleType;
    private final String executionDay;

    public JobInfoImpl(Long id, String name, String jobParams, String cycleType, String executionDay) {
        this.id = id;
        this.name = name;
        this.jobParams = jobParams;
        this.cycleType = cycleType;
        this.executionDay = executionDay;
    }

    /**
     * 通过JobEntity构造
     */
    public JobInfoImpl(JobEntity entity) {
        this(entity.getId(), entity.getName(), entity.getJobParams(), entity.getCycleType(), entity.getExecutionDay());
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getJobParams() {
        return jobParams;
    }
    
    @Override
    public void setJobParams(String jobParams) {
        this.jobParams = jobParams;
    }

    @Override
    public String getCycleType() {
        return cycleType;
    }

    @Override
    public String getExecutionDay() {
        return executionDay;
    }
} 