package com.czb.hn.service.job.impl;

import com.czb.hn.dto.bulletin.BulletinParams;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import com.czb.hn.jpa.securadar.entity.BulletinPushRecordEntity;
import com.czb.hn.enums.BulletinStatus;
import com.czb.hn.enums.PushMethod;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.BulletinGenerationRecordRepository;
import com.czb.hn.jpa.securadar.repository.BulletinPushRecordRepository;
import com.czb.hn.service.bulletin.BulletinRecordService;
import com.czb.hn.service.holidays.HolidayService;
import com.czb.hn.service.job.JobInfo;
import com.czb.hn.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 简报发送任务
 */
@Component("bulletinSenderJob")
public class BulletinSenderJob {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinSenderJob.class);
    
    private final BulletinGenerationRecordRepository generationRecordRepository;
    
    private final BulletinPushRecordRepository pushRecordRepository;
    
    private final HolidayService holidayService;
    
    private final BulletinRecordService bulletinRecordService;

    public BulletinSenderJob(
            BulletinGenerationRecordRepository generationRecordRepository, 
            BulletinPushRecordRepository pushRecordRepository,
            HolidayService holidayService,
            BulletinRecordService bulletinRecordService) {
        this.generationRecordRepository = generationRecordRepository;
        this.pushRecordRepository = pushRecordRepository;
        this.holidayService = holidayService;
        this.bulletinRecordService = bulletinRecordService;
    }

    /**
     * 简报发送任务入口 - 在配置的时间执行
     * 配置为 handler="bulletinSenderJob#send"
     */
    public void send(JobInfo jobInfo) {
        try {
            log.info("开始发送简报: {}", jobInfo.getName());
            
            // 解析任务参数
            BulletinParams params = JsonUtil.fromJson(jobInfo.getJobParams(), BulletinParams.class);
            if (params == null) {
                log.error("简报任务参数解析失败: {}", jobInfo.getName());
                return;
            }
            
            // 获取今天日期
            LocalDate today = LocalDate.now();
            
            // 判断今天是否应该发送简报
            boolean shouldSendToday = shouldSendBulletin(params, today);
            
            if (shouldSendToday) {
                // 获取需要发送的所有简报（包括补发）
                List<BulletinGenerationRecordEntity> bulletinsToSend = getBulletinsToSend(jobInfo, params, today);
                
                if (bulletinsToSend.isEmpty()) {
                    log.info("没有需要发送的简报: {}", jobInfo.getName());
                    return;
                }
                
                // 发送简报
                for (BulletinGenerationRecordEntity bulletin : bulletinsToSend) {
                    sendBulletin(jobInfo, params, bulletin);
                }
                
                // 更新最后发送日期
                updateLastSendDate(jobInfo, params, today);
            } else {
                log.info("今日不需要发送简报: {}, 日期: {}", jobInfo.getName(), today);
            }
            
        } catch (Exception e) {
            log.error("简报发送异常: {}", jobInfo.getName(), e);
        }
    }
    
    /**
     * 获取所有需要发送的简报
     */
    private List<BulletinGenerationRecordEntity> getBulletinsToSend(JobInfo jobInfo, BulletinParams params, LocalDate today) {
        List<BulletinGenerationRecordEntity> result = new ArrayList<>();
        
        // 1. 今天的简报（如果已生成）
        Optional<BulletinGenerationRecordEntity> todayBulletin = generationRecordRepository.findByJobIdAndBulletinDate(jobInfo.getId(), today);
        if (todayBulletin.isPresent() && "SUCCESS".equals(todayBulletin.get().getStatus())) {
            result.add(todayBulletin.get());
        }
        
        // 2. 需要补发的简报
        if (params.needCompensate()) {
            LocalDate lastSendDate = params.lastSendDate();
            if (lastSendDate == null) {
                // 如果没有最后发送日期，则只发送今天的
                return result;
            }
            
            // 获取上次发送日期之后到昨天为止所有未发送的简报
            LocalDate yesterday = today.minusDays(1);
            if (lastSendDate.isBefore(yesterday)) {
                List<BulletinGenerationRecordEntity> unsentBulletins = generationRecordRepository.findUnsentBulletins(
                    jobInfo.getId(), lastSendDate.plusDays(1), yesterday);
                
                for (BulletinGenerationRecordEntity bulletin : unsentBulletins) {
                    // 只补发满足条件的日期（在工作日生成但未发送的简报）
                    if (shouldCompensate(bulletin.getBulletinDate())) {
                        result.add(bulletin);
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 判断指定日期是否应该发送简报
     */
    private boolean shouldSendBulletin(BulletinParams params, LocalDate date) {
        // 注意：这里和需求相反，只在非工作日发送
        boolean isWorkday = holidayService.isWorkingDay(date);
        
        // 如果配置为在非工作日发送，且今天是非工作日，则发送
        return (params.sendOnNonWorkday() && !isWorkday);
    }
    
    /**
     * 判断指定日期是否需要补发
     */
    private boolean shouldCompensate(LocalDate date) {
        // 需要补发的是工作日生成的简报
        return holidayService.isWorkingDay(date);
    }
    
    /**
     * 发送简报
     */
    private void sendBulletin(JobInfo jobInfo, BulletinParams params, BulletinGenerationRecordEntity bulletin) {
        try {
            if (bulletin.getFileObjectName() == null) {
                log.error("简报文件不存在，无法发送: {}, 日期: {}", jobInfo.getName(), bulletin.getBulletinDate());
                return;
            }
            
            String title = bulletin.getBulletinTitle() + " - " + 
                    bulletin.getBulletinDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            
            LocalDateTime now = LocalDateTime.now();
            
            // 获取预签名URL（有效期24小时）
            String fileUrl = bulletinRecordService.getBulletinFileUrl(bulletin.getId(), 24 * 60 * 60);
            
            // 发送邮件
            if (params.emailReceivers() != null && !params.emailReceivers().isEmpty()) {
                for (String email : params.emailReceivers()) {
                    // 创建推送记录实体
                    BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        bulletin.getId(), 
                        email, 
                        PushType.EMAIL.name(), 
                        PushMethod.AUTO.name(), 
                        now
                    );
                    
                    // 保存推送记录
                    BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                    
                    try {
                        // 实际发送邮件的逻辑
                        // 使用预签名URL替代附件
                        String emailContent = "请通过以下链接查看简报：<br><a href='" + fileUrl + "'>点击下载简报</a><br>链接有效期24小时。";
                        
                        // TODO: 调用实际的邮件发送服务
                        // emailService.sendHtmlEmail(email, title, emailContent);
                        
                        // 更新推送状态为成功
                        updatePushRecordStatus(savedRecord, PushStatus.SUCCESS);
                    } catch (Exception e) {
                        log.error("发送邮件失败: {}", email, e);
                        
                        // 更新推送状态为失败
                        updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                    }
                }
            }
            
            // 发送短信
            if (params.smsReceivers() != null && !params.smsReceivers().isEmpty()) {
                for (String phone : params.smsReceivers()) {
                    // 创建推送记录实体
                    BulletinPushRecordEntity pushRecord = createPushRecordEntity(
                        bulletin.getId(), 
                        phone, 
                        PushType.SMS.name(), 
                        PushMethod.AUTO.name(), 
                        now
                    );
                    
                    // 保存推送记录
                    BulletinPushRecordEntity savedRecord = pushRecordRepository.save(pushRecord);
                    
                    try {
                        // 实际发送短信的逻辑
                        String smsContent = bulletin.getBulletinTitle() + " 已生成，请登录系统查看详情。";
                        
                        // TODO: 调用实际的短信发送服务
                        // smsService.sendSms(phone, smsContent);
                        
                        // 更新推送状态为成功
                        updatePushRecordStatus(savedRecord, PushStatus.SUCCESS);
                    } catch (Exception e) {
                        log.error("发送短信失败: {}", phone, e);
                        
                        // 更新推送状态为失败
                        updatePushRecordStatus(savedRecord, PushStatus.FAILURE);
                    }
                }
            }
            
            // 更新简报状态为已发送
            updateBulletinStatus(bulletin, "SENT");
            
            log.info("简报发送成功: {}, 日期: {}", jobInfo.getName(), bulletin.getBulletinDate());
        } catch (Exception e) {
            log.error("发送简报失败: {}, 日期: {}", jobInfo.getName(), bulletin.getBulletinDate(), e);
        }
    }
    
    /**
     * 创建推送记录实体
     */
    private BulletinPushRecordEntity createPushRecordEntity(Long generationId, String account, String pushType, String pushMethod, LocalDateTime now) {
        BulletinPushRecordEntity pushRecord = new BulletinPushRecordEntity();
        pushRecord.setGenerationId(generationId);
        pushRecord.setAccount(account);
        pushRecord.setPushType(pushType);
        pushRecord.setPushMethod(pushMethod);
        pushRecord.setPushTime(now);
        pushRecord.setStatus(PushStatus.PENDING.name());
        pushRecord.setCreatedAt(now);
        pushRecord.setUpdatedAt(now);
        return pushRecord;
    }
    
    /**
     * 更新推送记录状态
     */
    private void updatePushRecordStatus(BulletinPushRecordEntity pushRecord, PushStatus status) {
        pushRecord.setStatus(status.name());
        pushRecord.setUpdatedAt(LocalDateTime.now());
        pushRecordRepository.save(pushRecord);
    }
    
    /**
     * 更新简报状态
     */
    private void updateBulletinStatus(BulletinGenerationRecordEntity bulletin, String status) {
        bulletin.setStatus(status);
        bulletin.setUpdatedAt(LocalDateTime.now());
        generationRecordRepository.save(bulletin);
    }
    
    /**
     * 更新最后发送日期
     */
    private void updateLastSendDate(JobInfo jobInfo, BulletinParams params, LocalDate sendDate) {
        // 创建新的参数对象，更新最后发送日期
        BulletinParams updatedParams = new BulletinParams(
            params.bulletinTitle(),
            params.emailReceivers(),
            params.smsReceivers(),
            params.params(),
            params.sendOnNonWorkday(),
            params.needCompensate(),
            sendDate, // 更新为当前日期
            params.groupIds(),
            params.dataRange()
        );
            
        // 更新任务参数
        String updatedParamsJson = JsonUtil.toJson(updatedParams);
        
        // 更新JobInfo任务参数
        jobInfo.setJobParams(updatedParamsJson);
        
        // TODO: 更新任务参数到数据库
        // jobRepository.updateJobParams(jobInfo.getId(), updatedParamsJson);
        
        log.info("更新最后发送日期: {}, 日期: {}", jobInfo.getName(), sendDate);
    }
} 