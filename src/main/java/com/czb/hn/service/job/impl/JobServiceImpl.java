package com.czb.hn.service.job.impl;

import com.czb.hn.jpa.securadar.entity.JobEntity;
import com.czb.hn.jpa.securadar.repository.JobRepository;
import com.czb.hn.service.job.JobService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务服务实现类
 */
@Service
public class JobServiceImpl implements JobService {
    private final JobRepository jobRepository;

    public JobServiceImpl(JobRepository jobRepository) {
        this.jobRepository = jobRepository;
    }

    @Override
    @Transactional
    public JobEntity createJob(JobEntity job) {
        setJobProperty(job, "setCreateTime", LocalDateTime.now());
        setJobProperty(job, "setUpdateTime", LocalDateTime.now());
        return jobRepository.save(job);
    }

    @Override
    @Transactional
    public JobEntity updateJob(JobEntity job) {
        setJobProperty(job, "setUpdateTime", LocalDateTime.now());
        return jobRepository.save(job);
    }

    @Override
    @Transactional
    public void deleteJob(Long id) {
        jobRepository.deleteById(id);
    }

    @Override
    @Transactional
    public void enableJob(Long id) {
        JobEntity job = jobRepository.findById(id).orElseThrow(() -> new RuntimeException("任务不存在: " + id));
        setJobProperty(job, "setEnabled", true);
        setJobProperty(job, "setUpdateTime", LocalDateTime.now());
        jobRepository.save(job);
    }

    @Override
    @Transactional
    public void disableJob(Long id) {
        JobEntity job = jobRepository.findById(id).orElseThrow(() -> new RuntimeException("任务不存在: " + id));
        setJobProperty(job, "setEnabled", false);
        setJobProperty(job, "setUpdateTime", LocalDateTime.now());
        jobRepository.save(job);
    }

    @Override
    public JobEntity getJobById(Long id) {
        return jobRepository.findById(id).orElse(null);
    }

    @Override
    public List<JobEntity> getAllJobs() {
        return jobRepository.findAll();
    }

    @Override
    public List<JobEntity> getJobsByPlanId(Long planId) {
        return jobRepository.findByPlanId(planId);
    }
    
    /**
     * 使用反射设置JobEntity的属性
     */
    private void setJobProperty(JobEntity job, String methodName, Object value) {
        try {
            Method method = null;
            
            // 获取正确的方法
            for (Method m : job.getClass().getMethods()) {
                if (m.getName().equals(methodName) && m.getParameterCount() == 1) {
                    method = m;
                    break;
                }
            }
            
            if (method != null) {
                method.invoke(job, value);
            } else {
                throw new NoSuchMethodException("找不到方法: " + methodName);
            }
        } catch (Exception e) {
            throw new RuntimeException("设置JobEntity属性失败: " + methodName, e);
        }
    }
} 