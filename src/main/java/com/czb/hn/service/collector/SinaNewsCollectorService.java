package com.czb.hn.service.collector;

import com.czb.hn.dto.sina.data.SinaDataRequestDto;
import com.czb.hn.dto.sina.data.SinaDataResponseDto;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 新浪舆情数据收集器服务接口
 * 用于从新浪舆情通API获取数据
 */
public interface SinaNewsCollectorService extends DataCollectorService<SinaDataRequestDto, List<SinaDataResponseDto>> {
    
    /**
     * 根据票据和偏移量获取数据
     * 
     * @param ticket 票据
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 响应数据列表
     */
    List<SinaDataResponseDto> fetchDataByTicket(String ticket, Long offset, Integer limit);
    
    /**
     * 获取最新的数据偏移量
     * 
     * @return 最新的数据偏移量
     */
    Long getLatestOffset();
    
    /**
     * 保存原始数据到ODS层
     * 
     * @param responseDataList 响应数据列表
     * @return 保存的记录数
     */
    int saveToOds(List<SinaDataResponseDto> responseDataList);
} 