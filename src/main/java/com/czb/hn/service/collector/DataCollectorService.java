package com.czb.hn.service.collector;

import java.time.LocalDateTime;

/**
 * 数据收集服务接口
 * 抽象定义用于从外部数据源收集数据的方法
 */
public interface DataCollectorService<RequestT, ResponseT> {

    /**
     * 使用指定的请求参数从数据源获取数据
     * 
     * @param request 请求参数
     * @return 响应数据
     */
    ResponseT fetchData(RequestT request);

    /**
     * 启动定时任务获取数据
     */
    void scheduledFetch();

    /**
     * 根据时间范围获取数据
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageSize  页大小
     * @return 响应数据
     */
    ResponseT fetchDataByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Integer pageSize);

    /**
     * 根据关键词获取数据
     * 
     * @param keyword  关键词
     * @param pageSize 页大小
     * @return 响应数据
     */
    ResponseT fetchDataByKeyword(String keyword, Integer pageSize);
}