package com.czb.hn.service.essync;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 新浪舆情Elasticsearch服务接口
 */
public interface SinaNewsEsSnycService {
    
    /**
     * 将DWD实体同步到Elasticsearch
     * 
     * @param dwdEntity DWD实体
     * @return 同步后的Elasticsearch文档
     */
    SinaNewsDocument syncToElasticsearch(SinaNewsDwdEntity dwdEntity);
    
    /**
     * 批量将DWD实体同步到Elasticsearch
     * 
     * @param dwdEntities DWD实体列表
     * @return 同步的文档数量
     */
    int bulkSyncToElasticsearch(List<SinaNewsDwdEntity> dwdEntities);
    
    /**
     * 同步未处理的DWD数据到Elasticsearch
     * 
     * @return 同步的文档数量
     */
    int syncUnprocessedDwdToElasticsearch();
    
    /**
     * 异步同步未处理的DWD数据到Elasticsearch
     * 
     * @return 异步任务
     */
    CompletableFuture<Integer> asyncSyncUnprocessedDwdToElasticsearch();
    
} 