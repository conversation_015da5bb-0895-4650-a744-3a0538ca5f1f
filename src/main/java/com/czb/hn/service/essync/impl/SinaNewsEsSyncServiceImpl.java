package com.czb.hn.service.essync.impl;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.elasticsearch.SinaNewsDocumentDto;
import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;
import com.czb.hn.jpa.securadar.repository.SinaNewsDwdRepository;
import com.czb.hn.jpa.securadar.repository.elasticsearch.SinaNewsElasticsearchRepository;
import com.czb.hn.service.essync.SinaNewsEsSnycService;
import com.czb.hn.util.DateTimeUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 新浪舆情Elasticsearch服务实现
 */
@Service
public class SinaNewsEsSyncServiceImpl implements SinaNewsEsSnycService {

    private static final Logger logger = LoggerFactory.getLogger(SinaNewsEsSyncServiceImpl.class);

    @Autowired
    private SinaNewsDwdRepository dwdRepository;

    @Autowired
    private SinaNewsElasticsearchRepository elasticsearchRepository;

    @Value("${elasticsearch.batch.size:100}")
    private int batchSize;

    @Override
    public SinaNewsDocument syncToElasticsearch(SinaNewsDwdEntity dwdEntity) {
        if (dwdEntity == null) {
            logger.warn("Cannot sync null DWD entity to Elasticsearch");
            return null;
        }

        try {
            // 转换DWD实体到Elasticsearch文档
            SinaNewsDocument document = SinaNewsDocumentDto.fromDwdEntity(dwdEntity);

            // 调试：记录转换前的时间信息
            logTimeFieldsDebug("Before validation", document, dwdEntity.getContentId());

            // 验证并修复时间格式
            validateAndFixTimeFields(document, dwdEntity.getContentId());

            // 调试：记录验证后的时间信息
            logTimeFieldsDebug("After validation", document, dwdEntity.getContentId());

            // 保存到Elasticsearch
            SinaNewsDocument savedDocument = elasticsearchRepository.save(document);

            // 调试：记录保存后的时间信息
            logTimeFieldsDebug("After save", savedDocument, dwdEntity.getContentId());

            logger.debug("Successfully synced DWD entity to Elasticsearch: {}", dwdEntity.getContentId());
            return savedDocument;

        } catch (Exception e) {
            logger.error("Error syncing DWD entity to Elasticsearch: {}", e.getMessage(), e);
            throw new RuntimeException("Error syncing to Elasticsearch", e);
        }
    }

    @Override
    public int bulkSyncToElasticsearch(List<SinaNewsDwdEntity> dwdEntities) {
        if (dwdEntities == null || dwdEntities.isEmpty()) {
            logger.info("No DWD entities to sync to Elasticsearch");
            return 0;
        }

        try {
            // 转换DWD实体列表到Elasticsearch文档列表
            List<SinaNewsDocument> documents = dwdEntities.stream()
                    .map(dwdEntity -> {
                        SinaNewsDocument document = SinaNewsDocumentDto.fromDwdEntity(dwdEntity);
                        // 验证并修复时间格式
                        validateAndFixTimeFields(document, dwdEntity.getContentId());
                        return document;
                    })
                    .collect(Collectors.toList());

            // 批量保存到Elasticsearch
            Iterable<SinaNewsDocument> savedDocuments = elasticsearchRepository.saveAll(documents);

            // 计算保存的文档数量
            int count = 0;
            for (SinaNewsDocument doc : savedDocuments) {
                count++;
            }

            logger.info("Successfully bulk synced {} DWD entities to Elasticsearch", count);
            return count;

        } catch (Exception e) {
            logger.error("Error bulk syncing DWD entities to Elasticsearch: {}", e.getMessage(), e);
            throw new RuntimeException("Error bulk syncing to Elasticsearch", e);
        }
    }

    @Override
    public int syncUnprocessedDwdToElasticsearch() {
        // 移除事务，逐条独立处理ES同步，避免批量回滚问题
        List<SinaNewsDwdEntity> unprocessedRecords = dwdRepository.findUnprocessedToDwsRecords();

        if (unprocessedRecords.isEmpty()) {
            logger.info("No unprocessed DWD records found for ES sync");
            return 0;
        }

        logger.info("Found {} unprocessed DWD records for ES sync, processing independently",
                unprocessedRecords.size());

        int totalSynced = 0;
        int totalFailed = 0;

        for (int i = 0; i < unprocessedRecords.size(); i++) {
            SinaNewsDwdEntity entity = unprocessedRecords.get(i);

            try {
                if (syncSingleRecordSafely(entity)) {
                    totalSynced++;
                } else {
                    totalFailed++;
                }

                // 每处理100条记录输出一次进度
                if ((i + 1) % 100 == 0) {
                    logger.info("ES sync progress: {}/{} records. Synced: {}, Failed: {}",
                            i + 1, unprocessedRecords.size(), totalSynced, totalFailed);
                }

            } catch (Exception e) {
                totalFailed++;
                logger.error("Unexpected error syncing record {} to ES: {}", entity.getContentId(), e.getMessage(), e);
            }
        }

        logger.info("Independent ES sync completed. Total: {}, Synced: {}, Failed: {}",
                unprocessedRecords.size(), totalSynced, totalFailed);

        return totalSynced;
    }

    /**
     * 安全地同步单条记录到ES
     */
    private boolean syncSingleRecordSafely(SinaNewsDwdEntity entity) {
        try {
            // 检查是否已经同步
            if (Boolean.TRUE.equals(entity.getProcessedToDws())) {
                logger.debug("Record already synced to ES: {}", entity.getContentId());
                return true;
            }

            // 同步到ES
            SinaNewsDocument document = syncToElasticsearch(entity);
            if (document == null) {
                logger.error("Failed to sync record to ES: {}", entity.getContentId());
                return false;
            }

            // 更新状态
            entity.setProcessedToDws(true);
            dwdRepository.save(entity);

            logger.debug("Successfully synced record to ES: {}", entity.getContentId());
            return true;

        } catch (Exception e) {
            logger.error("Error syncing record {} to ES: {}", entity.getContentId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async
    public CompletableFuture<Integer> asyncSyncUnprocessedDwdToElasticsearch() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Starting async sync of DWD data to Elasticsearch");

                int syncedCount = syncUnprocessedDwdToElasticsearch();

                logger.info("Async sync completed, synced {} records to Elasticsearch", syncedCount);
                return syncedCount;

            } catch (Exception e) {
                logger.error("Error in async sync to Elasticsearch: {}", e.getMessage(), e);
                throw new RuntimeException("Error in async sync to Elasticsearch", e);
            }
        });
    }

    /**
     * 验证并修复ES文档中的时间字段格式
     * 确保所有时间字段都符合系统标准格式，包含完整的时分秒信息
     *
     * @param document  ES文档
     * @param contentId 内容ID（用于日志）
     */
    private void validateAndFixTimeFields(SinaNewsDocument document, String contentId) {
        try {
            // 验证并修复publishTime
            document.setPublishTime(validateAndFixSingleTimeField(document.getPublishTime(), "publishTime", contentId));

            // 验证并修复captureTime
            document.setCaptureTime(validateAndFixSingleTimeField(document.getCaptureTime(), "captureTime", contentId));

            // 验证并修复processTime
            document.setProcessTime(validateAndFixSingleTimeField(document.getProcessTime(), "processTime", contentId));

        } catch (Exception e) {
            logger.warn("Error validating time fields for content {}: {}", contentId, e.getMessage());
        }
    }

    /**
     * 验证并修复单个时间字段
     * 确保时间包含完整的时分秒信息
     *
     * @param dateTime  原始时间
     * @param fieldName 字段名称（用于日志）
     * @param contentId 内容ID（用于日志）
     * @return 修复后的时间，如果无效则返回null
     */
    private LocalDateTime validateAndFixSingleTimeField(LocalDateTime dateTime, String fieldName, String contentId) {
        if (dateTime == null) {
            return null;
        }

        try {
            // 验证时间是否可以正确格式化为标准格式
            String timeStr = DateTimeUtil.formatToStandardString(dateTime);
            if (timeStr == null) {
                logger.warn("Invalid {} for content {}, setting to null", fieldName, contentId);
                return null;
            }

            // 检查时间是否包含完整的时分秒信息
            // 如果时间是午夜00:00:00，可能是只有日期部分的数据
            if (dateTime.getHour() == 0 && dateTime.getMinute() == 0 && dateTime.getSecond() == 0) {
                logger.debug("Time field {} for content {} appears to be date-only ({}), keeping as is",
                        fieldName, contentId, timeStr);
            }

            logger.debug("Validated {} for content {}: {}", fieldName, contentId, timeStr);
            return dateTime;

        } catch (Exception e) {
            logger.warn("Error validating {} for content {}: {}, setting to null", fieldName, contentId,
                    e.getMessage());
            return null;
        }
    }

    /**
     * 调试方法：记录时间字段的详细信息
     *
     * @param stage     阶段描述
     * @param document  ES文档
     * @param contentId 内容ID
     */
    private void logTimeFieldsDebug(String stage, SinaNewsDocument document, String contentId) {
        if (document == null) {
            logger.debug("[{}] Document is null for content: {}", stage, contentId);
            return;
        }

        logger.debug("[{}] Time fields for content {}: publishTime={}, captureTime={}, processTime={}",
                stage, contentId,
                formatTimeForDebug(document.getPublishTime()),
                formatTimeForDebug(document.getCaptureTime()),
                formatTimeForDebug(document.getProcessTime()));
    }

    /**
     * 格式化时间用于调试输出
     *
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    private String formatTimeForDebug(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "null";
        }
        return DateTimeUtil.formatToStandardString(dateTime) + " (H:" + dateTime.getHour() +
                " M:" + dateTime.getMinute() + " S:" + dateTime.getSecond() + ")";
    }
}