package com.czb.hn.service.sina.impl;

import com.czb.hn.dto.sina.auth.*;
import com.czb.hn.service.sina.SinaAuthService;
import com.czb.hn.service.sina.SinaTokenCacheService;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 新浪舆情通认证服务实现
 */
@Service
public class SinaAuthServiceImpl implements SinaAuthService {

    private static final Logger logger = LoggerFactory.getLogger(SinaAuthServiceImpl.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SinaTokenCacheService tokenCacheService;

    @Value("${sina.api.auth.url:https://api-open-wx-www.yqt365.com/dataapp/api}")
    private String apiBaseUrl;

    @Value("${sina.api.appId}")
    private String defaultAppId;

    @Value("${sina.api.appSecret}")
    private String defaultAppSecret;

    // 访问令牌缓存
    private String cachedAccessToken;
    private Instant tokenExpireTime;
    private final ReentrantLock tokenLock = new ReentrantLock();

    /**
     * 服务初始化时恢复持久化的令牌
     */
    @PostConstruct
    public void initializeTokenCache() {
        try {
            logger.info("Initializing token cache from persistent storage...");

            // 尝试从持久化缓存恢复令牌
            Optional<String> persistentToken = tokenCacheService.getValidToken(defaultAppId);
            if (persistentToken.isPresent()) {
                try {
                    tokenLock.lock();
                    cachedAccessToken = persistentToken.get();
                    tokenExpireTime = Instant.now().plus(22, ChronoUnit.HOURS);
                    logger.info("Successfully restored token from persistent cache for appId: {}", defaultAppId);
                } finally {
                    tokenLock.unlock();
                }
            } else {
                logger.info("No valid persistent token found for appId: {}", defaultAppId);
            }

            // 清理过期的令牌
            tokenCacheService.cleanupExpiredTokens();

        } catch (Exception e) {
            logger.error("Failed to initialize token cache: {}", e.getMessage(), e);
            // 不抛出异常，允许服务正常启动
        }
    }

    @Override
    public SinaAuthorizeResponseDto getAuthorizeCode(String appId, String state) {
        try {
            String url = apiBaseUrl + "/oauth2/authorize";

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("appId", appId);
            params.add("responseType", "code");
            if (state != null && !state.isEmpty()) {
                params.add("state", state);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/x-www-form-urlencoded");

            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

            ResponseEntity<SinaAuthorizeResponseDto> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    SinaAuthorizeResponseDto.class);

            if (response.getBody() != null) {
                logger.info("Successfully got authorize code for appId: {}", appId);
                return response.getBody();
            } else {
                logger.error("Failed to get authorize code. Response body is null.");
                throw new RuntimeException("Failed to get authorize code. Response body is null.");
            }

        } catch (Exception e) {
            logger.error("Error getting authorize code: {}", e.getMessage(), e);
            throw new RuntimeException("Error getting authorize code", e);
        }
    }

    @Override
    public SinaTokenResponseDto getAccessToken(String appId, String appSecret, String authorizeCode) {
        try {
            // 检查是否还能获取新令牌
            if (!tokenCacheService.canFetchNewToken(appId)) {
                long todayCount = tokenCacheService.getTodayFetchCount(appId);
                logger.error("Daily token fetch limit exceeded for appId: {}, today count: {}/5", appId, todayCount);
                throw new RuntimeException("Daily token fetch limit exceeded. Please try again tomorrow.");
            }

            String url = apiBaseUrl + "/oauth2/token";

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("appId", appId);
            params.add("appSecret", appSecret);
            params.add("grantType", "authorization_code");
            params.add("authorizeCode", authorizeCode);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/x-www-form-urlencoded");

            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

            ResponseEntity<SinaTokenResponseDto> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    SinaTokenResponseDto.class);

            if (response.getBody() != null) {
                logger.info("Successfully got access token for appId: {}", appId);

                // 持久化缓存访问令牌
                SinaTokenResponseDto tokenResponse = response.getBody();
                if (tokenResponse.accessToken() != null) {
                    try {
                        tokenLock.lock();
                        // 保存到数据库
                        tokenCacheService.saveToken(appId, tokenResponse);

                        // 同时更新内存缓存（向后兼容）
                        cachedAccessToken = tokenResponse.accessToken().accessToken();
                        tokenExpireTime = Instant.now().plus(22, ChronoUnit.HOURS);

                        logger.info("Token saved to persistent cache for appId: {}", appId);
                    } finally {
                        tokenLock.unlock();
                    }
                }

                return tokenResponse;
            } else {
                logger.error("Failed to get access token. Response body is null.");
                throw new RuntimeException("Failed to get access token. Response body is null.");
            }

        } catch (Exception e) {
            logger.error("Error getting access token: {}", e.getMessage(), e);
            throw new RuntimeException("Error getting access token", e);
        }
    }

    @Override
    public String getCurrentAccessToken() {
        return getCurrentAccessToken(defaultAppId);
    }

    /**
     * 获取当前有效的访问令牌（支持指定appId）
     * 优先从持久化缓存获取，如果没有或过期则自动刷新
     *
     * @param appId 应用ID
     * @return 有效的访问令牌
     */
    public String getCurrentAccessToken(String appId) {
        try {
            tokenLock.lock();

            // 检查内存缓存
            if (cachedAccessToken != null && tokenExpireTime != null && Instant.now().isBefore(tokenExpireTime)) {
                logger.debug("Using valid token from memory cache for appId: {}", appId);
                return cachedAccessToken;
            } else {
                logger.debug("Memory cache token is expired or not exist for appId: {}", appId);
            }

            // 首先尝试从持久化缓存获取
            Optional<String> persistentToken = tokenCacheService.getValidToken(appId);
            if (persistentToken.isPresent()) {
                logger.debug("Retrieved valid token from persistent cache for appId: {}", appId);
                cachedAccessToken = persistentToken.get();
                tokenExpireTime = Instant.now().plus(22, ChronoUnit.HOURS);
                return persistentToken.get();
            }

            // 检查是否还能获取新令牌
            if (!tokenCacheService.canFetchNewToken(appId)) {
                long todayCount = tokenCacheService.getTodayFetchCount(appId);
                logger.error("Cannot refresh token - daily limit exceeded for appId: {}, count: {}/5", appId,
                        todayCount);
                throw new RuntimeException("Daily token fetch limit exceeded. Cannot refresh token.");
            }

            // 获取新令牌
            logger.info("Access token is expired or not exist, refreshing token for appId: {}", appId);

            // 先获取授权码
            SinaAuthorizeResponseDto authorizeResponse = getAuthorizeCode(appId, "refresh");
            if (authorizeResponse.authorizeCode() == null) {
                throw new RuntimeException("Failed to get authorize code for token refresh");
            }

            // 再获取访问令牌
            SinaTokenResponseDto tokenResponse = getAccessToken(
                    appId,
                    defaultAppSecret,
                    authorizeResponse.authorizeCode().authorizeCode());

            if (tokenResponse.accessToken() == null) {
                throw new RuntimeException("Failed to get access token for token refresh");
            }

            return cachedAccessToken;

        } catch (Exception e) {
            logger.error("Error refreshing access token for appId: {}, error: {}", appId, e.getMessage(), e);
            throw new RuntimeException("Error refreshing access token", e);
        } finally {
            tokenLock.unlock();
        }
    }
}