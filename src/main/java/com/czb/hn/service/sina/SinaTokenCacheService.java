package com.czb.hn.service.sina;

import com.czb.hn.dto.sina.auth.SinaTokenResponseDto;
import com.czb.hn.jpa.securadar.entity.SinaTokenCache;

import java.util.Optional;

/**
 * 新浪舆情通令牌缓存服务接口
 */
public interface SinaTokenCacheService {

    /**
     * 保存令牌到缓存
     * 
     * @param appId         应用ID
     * @param tokenResponse 令牌响应
     * @return 保存的令牌缓存实体
     */
    SinaTokenCache saveToken(String appId, SinaTokenResponseDto tokenResponse);

    /**
     * 获取有效的缓存令牌
     * 
     * @param appId 应用ID
     * @return 有效的令牌，如果不存在则返回空
     */
    Optional<String> getValidToken(String appId);

    /**
     * 检查今日是否还能获取新令牌
     * 
     * @param appId 应用ID
     * @return 是否可以获取新令牌
     */
    boolean canFetchNewToken(String appId);

    /**
     * 获取今日已获取令牌次数
     * 
     * @param appId 应用ID
     * @return 今日获取次数
     */
    long getTodayFetchCount(String appId);

    /**
     * 更新令牌使用时间
     * 
     * @param appId 应用ID
     */
    void updateTokenUsage(String appId);

    /**
     * 清理过期令牌
     */
    void cleanupExpiredTokens();

    /**
     * 检查是否有即将过期的令牌
     * 
     * @param appId 应用ID
     * @return 是否有即将过期的令牌
     */
    boolean hasTokenNearExpiry(String appId);

    /**
     * 使所有令牌失效（用于强制刷新）
     * 
     * @param appId 应用ID
     */
    void invalidateAllTokens(String appId);
}
