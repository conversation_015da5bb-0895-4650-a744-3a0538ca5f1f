package com.czb.hn.service.sina;

import com.czb.hn.dto.sina.auth.SinaAuthorizeResponseDto;
import com.czb.hn.dto.sina.auth.SinaTokenResponseDto;

/**
 * 新浪舆情通认证服务接口
 */
public interface SinaAuthService {
    
    /**
     * 获取授权码
     * 
     * @param appId 应用ID
     * @param state 状态参数（可选）
     * @return 授权响应
     */
    SinaAuthorizeResponseDto getAuthorizeCode(String appId, String state);
    
    /**
     * 获取访问令牌
     * 
     * @param appId 应用ID
     * @param appSecret 应用密钥
     * @param authorizeCode 授权码
     * @return 令牌响应
     */
    SinaTokenResponseDto getAccessToken(String appId, String appSecret, String authorizeCode);
    
    /**
     * 获取当前有效的访问令牌
     * 如果令牌不存在或已过期，将自动获取新令牌
     * 
     * @return 当前有效的访问令牌
     */
    String getCurrentAccessToken();
} 