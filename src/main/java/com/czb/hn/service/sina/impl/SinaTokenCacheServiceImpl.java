package com.czb.hn.service.sina.impl;

import com.czb.hn.dto.sina.auth.SinaTokenResponseDto;
import com.czb.hn.jpa.securadar.entity.SinaTokenCache;
import com.czb.hn.jpa.securadar.repository.SinaTokenCacheRepository;
import com.czb.hn.service.sina.SinaTokenCacheService;
import com.czb.hn.util.TokenEncryptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

/**
 * 新浪舆情通令牌缓存服务实现
 */
@Service
@Transactional
public class SinaTokenCacheServiceImpl implements SinaTokenCacheService {

    private static final Logger logger = LoggerFactory.getLogger(SinaTokenCacheServiceImpl.class);

    private static final int MAX_DAILY_FETCH_COUNT = 5; // 每日最大获取次数
    private static final int TOKEN_EXPIRY_HOURS = 24; // 令牌有效期（小时）
    private static final int EARLY_REFRESH_HOURS = 2; // 提前刷新时间（小时）

    @Autowired
    private SinaTokenCacheRepository tokenCacheRepository;

    @Autowired
    private TokenEncryptionUtil encryptionUtil;

    @Value("${sina.token.cache.cleanup.days:7}")
    private int cleanupDays;

    @Override
    public SinaTokenCache saveToken(String appId, SinaTokenResponseDto tokenResponse) {
        try {
            // 先将现有的活跃令牌设置为非活跃
            tokenCacheRepository.deactivateAllTokensByAppId(appId, LocalDateTime.now());

            // 加密令牌信息
            String encryptedAccessToken = encryptionUtil.encrypt(tokenResponse.accessToken().accessToken());
            String encryptedRefreshToken = tokenResponse.accessToken().refreshToken() != null
                    ? encryptionUtil.encrypt(tokenResponse.accessToken().refreshToken())
                    : null;

            // 计算过期时间（比实际过期时间提前2小时）
            LocalDateTime expireTime = LocalDateTime.now().plus(TOKEN_EXPIRY_HOURS - EARLY_REFRESH_HOURS,
                    ChronoUnit.HOURS);

            // 获取今日获取次数
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = todayStart.plusDays(1);
            long todayCount = tokenCacheRepository.countTokenFetchesByAppIdAndDate(appId, todayStart, todayEnd);

            // 创建新的令牌缓存记录
            SinaTokenCache tokenCache = SinaTokenCache.builder()
                    .appId(appId)
                    .encryptedAccessToken(encryptedAccessToken)
                    .encryptedRefreshToken(encryptedRefreshToken)
                    .expireTime(expireTime)
                    .createdTime(LocalDateTime.now())
                    .createdDate(todayStart)
                    .isActive(true)
                    .dailyFetchCount((int) todayCount + 1)
                    .remarks("Auto saved by SinaTokenCacheService")
                    .build();

            SinaTokenCache savedToken = tokenCacheRepository.save(tokenCache);
            logger.info("Successfully saved encrypted token for appId: {}, daily count: {}", appId, todayCount + 1);

            return savedToken;

        } catch (Exception e) {
            logger.error("Failed to save token for appId: {}, error: {}", appId, e.getMessage(), e);
            throw new RuntimeException("Failed to save token", e);
        }
    }

    @Override
    public Optional<String> getValidToken(String appId) {
        try {
            Optional<SinaTokenCache> tokenCacheOpt = tokenCacheRepository.findActiveTokenByAppId(appId,
                    LocalDateTime.now());

            if (tokenCacheOpt.isPresent()) {
                SinaTokenCache tokenCache = tokenCacheOpt.get();
                String decryptedToken = encryptionUtil.decrypt(tokenCache.getEncryptedAccessToken());

                // 更新最后使用时间
                tokenCacheRepository.updateLastUsedTime(tokenCache.getId(), LocalDateTime.now(), LocalDateTime.now());

                logger.debug("Retrieved valid token for appId: {}", appId);
                return Optional.of(decryptedToken);
            }

            logger.debug("No valid token found for appId: {}", appId);
            return Optional.empty();

        } catch (Exception e) {
            logger.error("Failed to get valid token for appId: {}, error: {}", appId, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public boolean canFetchNewToken(String appId) {
        try {
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = todayStart.plusDays(1);
            long todayCount = tokenCacheRepository.countTokenFetchesByAppIdAndDate(appId, todayStart, todayEnd);

            boolean canFetch = todayCount < MAX_DAILY_FETCH_COUNT;
            logger.debug("AppId: {} can fetch new token: {}, today count: {}/{}", appId, canFetch, todayCount,
                    MAX_DAILY_FETCH_COUNT);

            return canFetch;

        } catch (Exception e) {
            logger.error("Failed to check fetch permission for appId: {}, error: {}", appId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long getTodayFetchCount(String appId) {
        try {
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = todayStart.plusDays(1);
            return tokenCacheRepository.countTokenFetchesByAppIdAndDate(appId, todayStart, todayEnd);

        } catch (Exception e) {
            logger.error("Failed to get today fetch count for appId: {}, error: {}", appId, e.getMessage(), e);
            return MAX_DAILY_FETCH_COUNT; // 返回最大值，防止继续获取
        }
    }

    @Override
    public void updateTokenUsage(String appId) {
        try {
            Optional<SinaTokenCache> tokenCacheOpt = tokenCacheRepository.findActiveTokenByAppId(appId,
                    LocalDateTime.now());
            if (tokenCacheOpt.isPresent()) {
                SinaTokenCache tokenCache = tokenCacheOpt.get();
                tokenCacheRepository.updateLastUsedTime(tokenCache.getId(), LocalDateTime.now(), LocalDateTime.now());
            }
        } catch (Exception e) {
            logger.error("Failed to update token usage for appId: {}, error: {}", appId, e.getMessage(), e);
        }
    }

    @Override
    public void cleanupExpiredTokens() {
        try {
            LocalDateTime cleanupThreshold = LocalDateTime.now().minusDays(cleanupDays);
            tokenCacheRepository.deleteExpiredTokens(cleanupThreshold);
            logger.info("Cleaned up expired tokens older than {} days", cleanupDays);

        } catch (Exception e) {
            logger.error("Failed to cleanup expired tokens, error: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean hasTokenNearExpiry(String appId) {
        try {
            LocalDateTime thresholdTime = LocalDateTime.now().plus(EARLY_REFRESH_HOURS, ChronoUnit.HOURS);
            return !tokenCacheRepository.findTokensNearExpiry(thresholdTime, LocalDateTime.now()).isEmpty();

        } catch (Exception e) {
            logger.error("Failed to check token near expiry for appId: {}, error: {}", appId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void invalidateAllTokens(String appId) {
        try {
            tokenCacheRepository.deactivateAllTokensByAppId(appId, LocalDateTime.now());
            logger.info("Invalidated all tokens for appId: {}", appId);

        } catch (Exception e) {
            logger.error("Failed to invalidate tokens for appId: {}, error: {}", appId, e.getMessage(), e);
        }
    }
}
