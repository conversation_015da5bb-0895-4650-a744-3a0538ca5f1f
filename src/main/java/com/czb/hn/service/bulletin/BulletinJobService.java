package com.czb.hn.service.bulletin;

import com.czb.hn.dto.bulletin.BulletinJobRequest;

import java.util.List;

/**
 * 简报任务服务接口
 * 负责简报任务的创建、更新、启用、禁用和删除等操作
 */
public interface BulletinJobService {


    /**
     *  新增简报配置
     * <AUTHOR>
     * @date 2025/7/2 14:11
     * @param planId
     */
    void createBulletinConfiguration(Long planId);

    /**
     * 创建或更新简报任务
     *
     * @param request 简报任务请求
     * @return 发送任务ID
     */
    Long saveBulletinJob(BulletinJobRequest request);
    
    /**
     * 启用简报任务
     *
     * @param id 任务ID
     */
    void enableBulletinJob(Long id);
    
    /**
     * 禁用简报任务
     *
     * @param id 任务ID
     */
    void disableBulletinJob(Long id);
    
    /**
     * 删除简报任务
     *
     * @param id 任务ID
     */
    void deleteBulletinJob(Long id);
    
    /**
     * 根据方案ID获取所有简报任务
     *
     * @param planId 方案ID
     * @return 简报任务请求列表
     */
    List<BulletinJobRequest> getBulletinJobsByPlanId(Long planId);
    
    /**
     * 更新方案下的所有简报任务
     * 会处理新增、更新和删除操作
     *
     * @param planId 方案ID
     * @param requests 简报任务请求列表
     * @return 更新后的任务ID列表
     */
    List<Long> updateBulletinJobsByPlanId(Long planId, List<BulletinJobRequest> requests);
    
    /**
     * 为方案创建默认的日报、周报和月报任务
     *
     * @param planId 方案ID
     * @param planName 方案名称
     * @return 创建的任务ID列表
     */
    List<Long> createDefaultBulletinJobsForPlan(Long planId, String planName);
} 