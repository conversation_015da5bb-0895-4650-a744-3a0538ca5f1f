package com.czb.hn.service.user.impl;

import com.czb.hn.dto.user.UserLoginStatsDTO;
import com.czb.hn.jpa.securadar.entity.UserLoginRecord;
import com.czb.hn.jpa.securadar.repository.UserLoginRecordRepository;
import com.czb.hn.service.user.UserLoginRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * User Login Record Service Implementation
 * Provides enterprise-level user login tracking and statistics functionality
 */
@Slf4j
@Service
public class UserLoginRecordServiceImpl implements UserLoginRecordService {

    @Autowired
    private UserLoginRecordRepository userLoginRecordRepository;

    @Override
    @Transactional
    public void recordLogin(String userId, String userName, String enterpriseId, String enterpriseCode) {
        try {
            UserLoginRecord record = new UserLoginRecord();
            record.setUserId(userId);
            record.setUserName(userName);
            record.setEnterpriseId(enterpriseId);
            record.setEnterpriseCode(enterpriseCode);
            record.setLoginTime(LocalDateTime.now());

            userLoginRecordRepository.save(record);
            log.debug("Recorded login for user: {} in enterprise: {} ({})", userId, enterpriseCode, enterpriseId);
        } catch (Exception e) {
            log.error("Failed to record login for user: {} in enterprise: {}", userId, enterpriseId, e);
            // Don't throw exception to avoid affecting login process
        }
    }

    @Override
    public LocalDateTime getLastLoginTime(String userId) {
        try {
            Optional<UserLoginRecord> lastRecord = userLoginRecordRepository
                    .findTopByUserIdOrderByLoginTimeDesc(userId);
            return lastRecord.map(UserLoginRecord::getLoginTime).orElse(null);
        } catch (Exception e) {
            log.error("Failed to get last login time for user: {}", userId, e);
            return null;
        }
    }

    @Override
    public long getLoginCountInDays(String userId, int days) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
            return userLoginRecordRepository.countByUserIdAndLoginTimeAfter(userId, cutoffTime);
        } catch (Exception e) {
            log.error("Failed to get login count for user: {} in last {} days", userId, days, e);
            return 0;
        }
    }

    @Override
    public long getLoginCountInTimeRange(String userId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return userLoginRecordRepository.countByUserIdAndLoginTimeBetween(userId, startTime, endTime);
        } catch (Exception e) {
            log.error("Failed to get login count for user: {} between {} and {}", userId, startTime, endTime, e);
            return 0;
        }
    }

    @Override
    @Transactional
    public int cleanupOldRecords() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            long totalOldRecords = userLoginRecordRepository.countByLoginTimeBefore(cutoffTime);

            if (totalOldRecords == 0) {
                log.info("No old login records to cleanup");
                return 0;
            }

            int deletedCount = userLoginRecordRepository.deleteOldRecordsButKeepLatest(cutoffTime);
            log.info(
                    "Cleaned up {} old login records (out of {} total old records), keeping latest record for each user",
                    deletedCount, totalOldRecords);
            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to cleanup old login records", e);
            return 0;
        }
    }

    @Override
    public UserLoginStatsDTO getUserLoginStats(String userId) {
        try {
            Optional<UserLoginRecord> lastRecord = userLoginRecordRepository
                    .findTopByUserIdOrderByLoginTimeDesc(userId);
            if (lastRecord.isEmpty()) {
                return null;
            }

            UserLoginRecord record = lastRecord.get();
            long sevenDayCount = getLoginCountInDays(userId, 7);

            return new UserLoginStatsDTO(
                    record.getUserId(),
                    record.getUserName(),
                    record.getEnterpriseId(),
                    record.getEnterpriseCode(),
                    record.getLoginTime(),
                    sevenDayCount);
        } catch (Exception e) {
            log.error("Failed to get login stats for user: {}", userId, e);
            return null;
        }
    }

    /**
     * Scheduled task to cleanup old login records
     * Runs daily at 2:00 AM to avoid business peak hours
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void scheduledCleanup() {
        log.info("Starting scheduled cleanup of old login records");
        int deletedCount = cleanupOldRecords();
        log.info("Scheduled cleanup completed, deleted {} records", deletedCount);
    }
}
