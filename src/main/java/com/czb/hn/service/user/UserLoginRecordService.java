package com.czb.hn.service.user;

import com.czb.hn.dto.user.UserLoginStatsDTO;
import java.time.LocalDateTime;

/**
 * User Login Record Service
 * Provides functionality for tracking enterprise-level user login activities
 * and statistics
 */
public interface UserLoginRecordService {

    /**
     * Record a user login event
     *
     * @param userId         User ID from authentication
     * @param userName       User display name
     * @param enterpriseId   Enterprise ID for enterprise-level tracking
     * @param enterpriseCode Enterprise code for enterprise identification
     */
    void recordLogin(String userId, String userName, String enterpriseId, String enterpriseCode);

    /**
     * Get the last login time for a specific user
     * 
     * @param userId User ID
     * @return Last login time, or null if user has never logged in
     */
    LocalDateTime getLastLoginTime(String userId);

    /**
     * Get the count of login events for a user within the last N days
     * 
     * @param userId User ID
     * @param days   Number of days to look back (e.g., 7 for last 7 days)
     * @return Number of login events
     */
    long getLoginCountInDays(String userId, int days);

    /**
     * Clean up old login records according to retention policy
     * Removes records older than 30 days but keeps at least one record per user
     * 
     * @return Number of records deleted
     */
    int cleanupOldRecords();

    /**
     * Get the count of login events for a user within a specific time range
     *
     * @param userId    User ID
     * @param startTime Start time (inclusive)
     * @param endTime   End time (exclusive)
     * @return Number of login events
     */
    long getLoginCountInTimeRange(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Get comprehensive login statistics for a user
     *
     * @param userId User ID
     * @return User login statistics DTO, or null if user has no login records
     */
    UserLoginStatsDTO getUserLoginStats(String userId);
}
