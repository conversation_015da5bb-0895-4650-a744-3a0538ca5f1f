package com.czb.hn.service.minio.impl;

import com.czb.hn.config.MinioConfig;
import com.czb.hn.service.minio.MinioStorageService;
import io.minio.*;
import io.minio.http.Method;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MinIO存储服务实现类
 */
@Service
public class MinioStorageServiceImpl implements MinioStorageService {
    private static final Logger log = LoggerFactory.getLogger(MinioStorageServiceImpl.class);
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    
    public MinioStorageServiceImpl(MinioConfig minioConfig) {
        this.minioConfig = minioConfig;
        this.minioClient = MinioClient.builder()
            .endpoint(minioConfig.getEndpoint())
            .credentials(minioConfig.getAccessKey(), minioConfig.getSecretKey())
            .build();
        createBucketIfNotExists();
    }
    
    @Override
    public String uploadFile(byte[] content, String filename, String contentType) {
        try {
            // 生成对象名：年月/UUID_原始文件名
            String folder = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
            String objectName = folder + "/" + UUID.randomUUID() + "_" + filename;
            
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(new ByteArrayInputStream(content), content.length, -1)
                    .contentType(contentType)
                    .build()
            );
            
            log.info("文件上传成功: {}", objectName);
            return objectName;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public byte[] downloadFile(String objectName) {
        try {
            GetObjectResponse response = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .build()
            );
            
            return IOUtils.toByteArray(response);
        } catch (Exception e) {
            log.error("文件下载失败: {}", objectName, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getPresignedUrl(String objectName, int expirySeconds) {
        try {
            return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .method(Method.GET)
                    .expiry(expirySeconds, TimeUnit.SECONDS)
                    .build()
            );
        } catch (Exception e) {
            log.error("获取预签名URL失败: {}", objectName, e);
            throw new RuntimeException("获取预签名URL失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void deleteFile(String objectName) {
        try {
            minioClient.removeObject(
                RemoveObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .build()
            );
            log.info("文件删除成功: {}", objectName);
        } catch (Exception e) {
            log.error("文件删除失败: {}", objectName, e);
            throw new RuntimeException("文件删除失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建桶（如果不存在）
     */
    private void createBucketIfNotExists() {
        try {
            boolean bucketExists = minioClient.bucketExists(
                BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build()
            );
            
            if (!bucketExists) {
                minioClient.makeBucket(
                    MakeBucketArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .build()
                );
                log.info("创建MinIO桶成功: {}", minioConfig.getBucketName());
            }
        } catch (Exception e) {
            log.error("创建MinIO桶失败", e);
            throw new RuntimeException("创建MinIO桶失败: " + e.getMessage(), e);
        }
    }
} 