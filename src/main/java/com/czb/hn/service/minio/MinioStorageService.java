package com.czb.hn.service.minio;

/**
 * MinIO存储服务接口
 */
public interface MinioStorageService {
    
    /**
     * 上传文件
     * @param content 文件内容
     * @param filename 文件名
     * @param contentType 内容类型
     * @return 文件对象名
     */
    String uploadFile(byte[] content, String filename, String contentType);
    
    /**
     * 下载文件
     * @param objectName 对象名
     * @return 文件内容
     */
    byte[] downloadFile(String objectName);
    
    /**
     * 获取文件预签名URL
     * @param objectName 对象名
     * @param expirySeconds 过期时间（秒）
     * @return 预签名URL
     */
    String getPresignedUrl(String objectName, int expirySeconds);
    
    /**
     * 删除文件
     * @param objectName 对象名
     */
    void deleteFile(String objectName);
}