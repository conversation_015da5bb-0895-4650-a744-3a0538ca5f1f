package com.czb.hn.service.cleaner;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 数据清洗服务接口
 * 抽象定义用于清洗和转换数据的方法
 */
public interface DataCleanerService<SourceT, TargetT> {
    
    /**
     * 处理单个数据记录
     * 
     * @param source 源数据记录
     * @return 处理后的数据记录
     */
    TargetT processRecord(SourceT source);
    
    /**
     * 处理所有未处理的数据记录
     * 
     * @return 处理的记录数量
     */
    int processAllUnprocessedRecords();
    
    /**
     * 启动定时任务处理数据
     * 
     * @return 异步执行结果
     */
    CompletableFuture<Void> scheduledProcessing();
    
    /**
     * 根据指定日期查找处理后的数据
     *
     * @param date 日期
     * @return 处理后的数据列表
     */
    List<TargetT> findByDate(LocalDate date);
} 