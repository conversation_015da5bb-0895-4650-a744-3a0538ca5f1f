package com.czb.hn.service.holidays;


import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/04/01  11:00
 */
public interface HolidayService {



    /**
     * 获取两个日期相隔多少个工作日
     *
     * @param startDate
     * @param endDate
     * @return int
     * <AUTHOR>
     * @date 2024/4/1 16:39
     */
    int getIntervalDays(LocalDate startDate, LocalDate endDate);



    /**
     *  获取interval天后的日期，排除节假日
     * <AUTHOR>
     * @date 2024/4/1 16:58
     * @param startDate
     * @param interval
     * @return java.time.LocalDate
     */
    LocalDate getTargetDate(LocalDate startDate, int interval);


    /**
     *  校验当前日期是否是工作日
     * <AUTHOR>
     * @date 2024/4/2 15:16
     * @param date
     * @return boolean
     */
    boolean isWorkingDay(LocalDate date);

}
