package com.czb.hn.service.holidays.impl;

import com.czb.hn.jpa.hnylt.repository.HolidayRepository;
import com.czb.hn.service.holidays.HolidayService;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/01  11:00
 */
@Service
public class HolidayServiceImpl implements HolidayService {


    private final HolidayRepository holidayRepository;

    public HolidayServiceImpl(HolidayRepository holidayRepository) {
        this.holidayRepository = holidayRepository;
    }


    @Override
    public int getIntervalDays(LocalDate startDate, LocalDate endDate) {
        // 确保startDate不大于endDate
        if (startDate.isAfter(endDate)) {
            throw new RuntimeException("开始日期不能晚于结束日期");
        }
        // 获取两个日期之间的总天数
        int intervalDays = Period.between(startDate, endDate).getDays() + 1; // 包含结束日期当天

        // 查询在两个日期之间的非工作日数量（周末和节假日）
        Integer nonWorkingDays = holidayRepository.getNonWorkingDays(startDate, endDate);

        // 工作日 = 总天数 - 非工作日数
        return intervalDays - nonWorkingDays;
    }



    @Override
    public LocalDate getTargetDate(LocalDate startDate, int interval) {
        List<LocalDate> holidays = holidayRepository.findAllHolidays();
        List<LocalDate> extraWorkdays = holidayRepository.findAllExtraWorkdays();

        int workingDaysCount = 0;
        LocalDate currentDate = startDate;

        while (workingDaysCount < interval) {
            // 如果当前日期是工作日（即不是节假日，或是调休的工作日），则计数
            if ((!holidays.contains(currentDate) || extraWorkdays.contains(currentDate)) &&
                    !(currentDate.getDayOfWeek() == DayOfWeek.SATURDAY ||
                            currentDate.getDayOfWeek() == DayOfWeek.SUNDAY)) {
                workingDaysCount++;
            } else if (extraWorkdays.contains(currentDate)) { // 当前日期是调休工作日
                workingDaysCount++;
            }

            // 在达到所需的工作日数之前，继续检查下一天
            if (workingDaysCount < interval) {
                currentDate = currentDate.plusDays(1);
            }
        }
        return currentDate;
    }

    @Override
    public boolean isWorkingDay(LocalDate date) {
        List<LocalDate> holidays = holidayRepository.findAllHolidays();
        List<LocalDate> extraWorkdays = holidayRepository.findAllExtraWorkdays();

        DayOfWeek dayOfWeek = date.getDayOfWeek();
        boolean isWeekend = (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY);

        // 判断今天是否是工作日：
        // 1. 不是节假日
        // 2. 如果是周末，需要检查是否是调休的工作日
        return !holidays.contains(date) && (!isWeekend || extraWorkdays.contains(date));
    }

    /**
     *  获取所有的周末日期
     * <AUTHOR>
     * @date 2024/4/1 11:18
     * @param year
     * @return java.time.LocalDate
     */
    private List<LocalDate> getWeekendDates(Integer year) {

        // 存储周末日期的列表
        List<LocalDate> weekends = new ArrayList<>();
        // 找到当年第一个周六
        LocalDate firstSaturday = LocalDate.of(year, 1, 1)
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY));
        // 从第一个周六开始遍历每个周末
        LocalDate currentWeekend = firstSaturday;
        while (currentWeekend.getYear() == year) {
            // 添加周六
            weekends.add(currentWeekend);
            // 检查周日是否仍然在相同的年份
            LocalDate sunday = currentWeekend.plusDays(1);
            if (sunday.getYear() == year) {
                weekends.add(sunday);
            }
            // 移到下一周的周六
            currentWeekend = currentWeekend.plusWeeks(1);
        }
        return weekends;
    }
}
