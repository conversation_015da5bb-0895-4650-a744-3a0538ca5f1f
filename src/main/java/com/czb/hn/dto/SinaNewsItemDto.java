package com.czb.hn.dto;

import java.time.LocalDateTime;

/**
 * DTO for a single news item from Sina API
 */
public record SinaNewsItemDto(
    String newsUuid,
    String title,
    String content,
    String url,
    String mediaName,
    LocalDateTime publishTime,
    String keywords,
    String category,
    String author
) {
    // Compact canonical constructor for validation
    public SinaNewsItemDto {
        if (newsUuid == null || newsUuid.isBlank()) {
            throw new IllegalArgumentException("News UUID cannot be null or blank");
        }
        
        if (title == null || title.isBlank()) {
            throw new IllegalArgumentException("Title cannot be null or blank");
        }
        
        if (url == null || url.isBlank()) {
            throw new IllegalArgumentException("URL cannot be null or blank");
        }
        
        if (publishTime == null) {
            throw new IllegalArgumentException("Publish time cannot be null");
        }
    }
} 