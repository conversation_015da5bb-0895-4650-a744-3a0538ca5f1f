package com.czb.hn.dto.sina.auth;

/**
 * 新浪舆情通Token响应DTO
 */
public record SinaTokenResponseDto(
    String code,
    String message,
    String transactionId,
    SinaAccessTokenDto accessToken
) {
    // AccessToken信息
    public record SinaAccessTokenDto(
        String accessToken,
        String expireIn,
        String refreshToken
    ) {
        // 规范的构造器进行验证
        public SinaAccessTokenDto {
            if (accessToken == null || accessToken.isBlank()) {
                throw new IllegalArgumentException("AccessToken不能为空");
            }
        }
    }
} 