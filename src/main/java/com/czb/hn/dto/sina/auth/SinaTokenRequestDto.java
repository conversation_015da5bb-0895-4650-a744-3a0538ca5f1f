package com.czb.hn.dto.sina.auth;

/**
 * 新浪舆情通Token请求DTO
 */
public record SinaTokenRequestDto(
    String appId,
    String appSecret,
    String grantType,
    String authorizeCode
) {
    // 规范的构造器进行验证
    public SinaTokenRequestDto {
        if (appId == null || appId.isBlank()) {
            throw new IllegalArgumentException("AppId不能为空");
        }
        
        if (appSecret == null || appSecret.isBlank()) {
            throw new IllegalArgumentException("AppSecret不能为空");
        }
        
        if (grantType == null || grantType.isBlank()) {
            throw new IllegalArgumentException("GrantType不能为空");
        }
        
        if (authorizeCode == null || authorizeCode.isBlank()) {
            throw new IllegalArgumentException("AuthorizeCode不能为空");
        }
    }
} 