package com.czb.hn.dto.sina.data;

/**
 * 新浪舆情通数据请求DTO
 */
public record SinaDataRequestDto(
    String accessToken,
    String ticket,
    Integer offset,
    Integer pollType,
    Integer num
) {
    // 规范的构造器进行验证
    public SinaDataRequestDto {
        if (accessToken == null || accessToken.isBlank()) {
            throw new IllegalArgumentException("AccessToken不能为空");
        }
        
        if (ticket == null || ticket.isBlank()) {
            throw new IllegalArgumentException("Ticket不能为空");
        }
        
        if (offset == null || offset <= 0) {
            throw new IllegalArgumentException("Offset必须大于0");
        }
        
        // pollType和num是可选的，但如果提供了pollType，必须提供num
        if (pollType != null && num == null) {
            throw new IllegalArgumentException("如果提供了PollType，必须同时提供Num参数");
        }
        
        // 如果提供了num，验证其范围
        if (num != null && (num < 100 || num > 10000)) {
            throw new IllegalArgumentException("Num必须在100到10000之间");
        }
    }
} 