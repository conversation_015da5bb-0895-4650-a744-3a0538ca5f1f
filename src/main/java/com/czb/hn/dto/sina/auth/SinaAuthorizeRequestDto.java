package com.czb.hn.dto.sina.auth;

/**
 * 新浪舆情通授权请求DTO
 */
public record SinaAuthorizeRequestDto(
    String appId,
    String responseType,
    String state
) {
    // 规范的构造器进行验证
    public SinaAuthorizeRequestDto {
        if (appId == null || appId.isBlank()) {
            throw new IllegalArgumentException("AppId不能为空");
        }
        
        if (responseType == null || responseType.isBlank()) {
            throw new IllegalArgumentException("ResponseType不能为空");
        }
        
        // state是可选的，不做验证
    }
} 