package com.czb.hn.dto.sina.auth;

/**
 * 新浪舆情通授权响应DTO
 */
public record SinaAuthorizeResponseDto(
    String code,
    String message,
    String transactionId,
    SinaAuthorizeCodeDto authorizeCode
) {
    // 授权码信息
    public record SinaAuthorizeCodeDto(
        String authorizeCode,
        String state
    ) {
        // 规范的构造器进行验证
        public SinaAuthorizeCodeDto {
            if (authorizeCode == null || authorizeCode.isBlank()) {
                throw new IllegalArgumentException("授权码不能为空");
            }
        }
    }
} 