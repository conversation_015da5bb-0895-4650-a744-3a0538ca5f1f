package com.czb.hn.dto.briefing;

import com.czb.hn.dto.alert.config.ContentSettingsDto;
import com.czb.hn.dto.alert.config.LevelSettingsDto;
import com.czb.hn.dto.alert.config.ThresholdSettingsDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import io.swagger.v3.oas.annotations.media.Schema;

public record BriefingConfigurationUpdateDto(
    @Schema(description = "Configuration name", example = "华能资本")
    String name,

    @Schema(description = "Whether the configuration is active", example = "true")
    Boolean isActive,

    @Schema(description = "Content filtering settings")
    ContentSettingsDto contentSettings,

    @Schema(description = "Alert threshold conditions")
    ThresholdSettingsDto thresholdSettings,

    @Schema(description = "Alert level classification settings")
    LevelSettingsDto levelSettings,

    @Schema(description = "Whether the daily briefing is active", example = "true")
    Boolean dailyBriefingIsActive,

    @Schema(description = "Daily briefing reception settings")
    BriefingReceptionSettingsDto dailyReceptionSettings,

    @Schema(description = "Whether the weekly briefing is active", example = "true")
    Boolean weeklyBriefingIsActive,

    @Schema(description = "Weekly briefing reception settings")
    BriefingReceptionSettingsDto weeklyReceptionSettings,

    @Schema(description = "Whether the monthly briefing is active", example = "true")
    Boolean monthlyBriefingIsActive,

    @Schema(description = "Monthly briefing reception settings")
    BriefingReceptionSettingsDto monthlyReceptionSettings
    ){
    public BriefingConfigurationUpdateDto {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Configuration name cannot be null or blank");
        }
        if (contentSettings == null) {
            throw new IllegalArgumentException("Content settings cannot be null");
        }
        if (thresholdSettings == null) {
            throw new IllegalArgumentException("Threshold settings cannot be null");
        }
        if (levelSettings == null) {
            throw new IllegalArgumentException("Level settings cannot be null");
        }

        if (!dailyBriefingIsActive && !weeklyBriefingIsActive && !monthlyBriefingIsActive) {
            throw new IllegalArgumentException("At least one briefing type must be active");
        }
        if (dailyBriefingIsActive && dailyReceptionSettings == null) {
            throw new IllegalArgumentException("Daily briefing is active but no reception settings provided");
        }
        if (dailyBriefingIsActive && !dailyReceptionSettings.receptionMethods().email().enabled() && !dailyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Daily briefing is active but no valid reception methods provided");
        }
        if (weeklyBriefingIsActive && weeklyReceptionSettings == null) {
            throw new IllegalArgumentException("Weekly briefing is active but no reception settings provided");
        }
        if (weeklyBriefingIsActive && !weeklyReceptionSettings.receptionMethods().email().enabled() && !weeklyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Weekly briefing is active but no valid reception methods provided");
        }
        if (monthlyBriefingIsActive && monthlyReceptionSettings == null) {
            throw new IllegalArgumentException("Monthly briefing is active but no reception settings provided");
        }
        if (monthlyBriefingIsActive && !monthlyReceptionSettings.receptionMethods().email().enabled() && !monthlyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Monthly briefing is active but no valid reception methods provided");
        }
    }
}
