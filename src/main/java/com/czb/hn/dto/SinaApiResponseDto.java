package com.czb.hn.dto;

import java.util.List;

/**
 * DTO for handling responses from Sina API
 */
public record SinaApiResponseDto(
    Integer code,
    String message,
    SinaApiPaginationDto pagination,
    List<SinaNewsItemDto> data
) {
    // Compact canonical constructor for validation
    public SinaApiResponseDto {
        if (code == null) {
            throw new IllegalArgumentException("Response code cannot be null");
        }
    }
    
    // Inner class for pagination information
    public record SinaApiPaginationDto(
        Integer total,
        Integer pageSize,
        Integer pageNum,
        Integer totalPages
    ) {
        // Compact canonical constructor for validation
        public SinaApiPaginationDto {
            if (total == null || total < 0) {
                throw new IllegalArgumentException("Total count cannot be null or negative");
            }
            
            if (pageSize == null || pageSize <= 0) {
                throw new IllegalArgumentException("Page size must be greater than zero");
            }
            
            if (pageNum == null || pageNum <= 0) {
                throw new IllegalArgumentException("Page number must be greater than zero");
            }
            
            if (totalPages == null || totalPages < 0) {
                throw new IllegalArgumentException("Total pages cannot be null or negative");
            }
        }
    }
} 