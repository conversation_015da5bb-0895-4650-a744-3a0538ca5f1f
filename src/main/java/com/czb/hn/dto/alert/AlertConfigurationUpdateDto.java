package com.czb.hn.dto.alert;

import com.czb.hn.dto.alert.config.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO for updating existing alert configurations
 * All fields are optional to support partial updates
 */
public record AlertConfigurationUpdateDto(
        @Schema(description = "Configuration name", example = "Updated Critical News Alert") String name,

        @Schema(description = "Configuration description", example = "Updated alert configuration for critical news monitoring") String description,

        @Schema(description = "Associated plan ID", example = "2") Long planId,

        @Schema(description = "Whether the alert is enabled", example = "false") Boolean enabled,

        @Schema(description = "Alert keywords configuration") AlertKeywordsDto alertKeywords,

        @Schema(description = "Content filtering settings") ContentSettingsDto contentSettings,

        @Schema(description = "Alert threshold conditions") ThresholdSettingsDto thresholdSettings,

        @Schema(description = "Alert level classification settings") LevelSettingsDto levelSettings,

        @Schema(description = "Alert reception and notification settings") ReceptionSettingsDto receptionSettings,

        @Schema(description = "User updating the configuration", example = "admin") String updatedBy,

        @Schema(description = "Reason for updating this configuration", example = "Adjusted threshold values") String changeReason) {
    // Compact canonical constructor for validation
    public AlertConfigurationUpdateDto {
        if (name != null && name.isBlank()) {
            throw new IllegalArgumentException("Configuration name cannot be blank");
        }
        if (name != null && name.length() > 255) {
            throw new IllegalArgumentException("Configuration name cannot exceed 255 characters");
        }
        if (description != null && description.length() > 1000) {
            throw new IllegalArgumentException("Description cannot exceed 1000 characters");
        }
        if (changeReason != null && changeReason.length() > 500) {
            throw new IllegalArgumentException("Change reason cannot exceed 500 characters");
        }
        if (updatedBy != null && updatedBy.length() > 255) {
            throw new IllegalArgumentException("Updated by cannot exceed 255 characters");
        }

        // If reception settings are provided, validate they have at least one valid
        // recipient
        if (receptionSettings != null) {
            validateReceptionSettings(receptionSettings);
        }
    }

    /**
     * Validates that reception settings contain at least one valid recipient
     * 
     * @param receptionSettings the reception settings to validate
     * @throws IllegalArgumentException if no valid recipients are configured
     */
    private static void validateReceptionSettings(ReceptionSettingsDto receptionSettings) {
        if (receptionSettings.receptionMethods() == null) {
            throw new IllegalArgumentException("Reception methods configuration cannot be null");
        }

        boolean hasValidRecipient = false;

        // Check email recipients
        if (receptionSettings.receptionMethods().email() != null &&
                Boolean.TRUE.equals(receptionSettings.receptionMethods().email().enabled()) &&
                receptionSettings.receptionMethods().email().recipients() != null &&
                !receptionSettings.receptionMethods().email().recipients().isEmpty()) {
            hasValidRecipient = true;
        }

        // Check SMS recipients
        if (receptionSettings.receptionMethods().sms() != null &&
                Boolean.TRUE.equals(receptionSettings.receptionMethods().sms().enabled()) &&
                receptionSettings.receptionMethods().sms().recipients() != null &&
                !receptionSettings.receptionMethods().sms().recipients().isEmpty()) {
            hasValidRecipient = true;
        }

        if (!hasValidRecipient) {
            throw new IllegalArgumentException(
                    "At least one valid recipient must be configured in reception settings (email or SMS with enabled status and non-empty recipients list)");
        }
    }
}
