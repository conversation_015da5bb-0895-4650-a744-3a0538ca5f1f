package com.czb.hn.dto.alert;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;

import java.time.LocalDateTime;

/**
 * Alert Push Detail Search DTO
 * Used for searching and filtering push detail records
 */
public record AlertPushDetailSearchDto(
    @Schema(description = "预警ID", example = "1")
    Long alertId,

    @Schema(description = "企业ID", example = "enterprise123")
    String enterpriseId,

    @Schema(description = "推送类型", example = "EMAIL", allowableValues = {"EMAIL", "SMS", "SYSTEM"})
    PushType pushType,

    @Schema(description = "推送状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILURE"})
    PushStatus pushStatus,

    @Schema(description = "账户信息", example = "<EMAIL>")
    String accountInfo,

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime startTime,

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime endTime,

    @Schema(description = "页码", example = "0")
    @Min(value = 0, message = "Page number must be non-negative")
    Integer page,

    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "Page size must be positive")
    Integer size,

    @Schema(description = "排序字段", example = "pushTime", allowableValues = {
        "pushTime", "createdAt", "updatedAt", "retryCount"
    })
    String sortBy,

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    String sortDirection
) {
    // Compact canonical constructor for validation
    public AlertPushDetailSearchDto {
        // Set default values
        if (page == null) {
            page = 0;
        }
        if (size == null) {
            size = 20;
        }
        if (sortBy == null) {
            sortBy = "pushTime";
        }
        if (sortDirection == null) {
            sortDirection = "desc";
        }

        // Validate page size limits
        if (size > 100) {
            throw new IllegalArgumentException("Page size cannot exceed 100");
        }

        // Validate sort direction
        if (!sortDirection.equalsIgnoreCase("asc") && !sortDirection.equalsIgnoreCase("desc")) {
            throw new IllegalArgumentException("Sort direction must be 'asc' or 'desc'");
        }

        // Validate sort field
        if (!java.util.List.of("pushTime", "createdAt", "updatedAt", "retryCount").contains(sortBy)) {
            throw new IllegalArgumentException("Invalid sort field: " + sortBy);
        }

        // Validate time range
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("Start time cannot be after end time");
        }

        // Validate time range is not too large (max 1 year)
        if (startTime != null && endTime != null) {
            if (startTime.plusYears(1).isBefore(endTime)) {
                throw new IllegalArgumentException("Time range cannot exceed 1 year");
            }
        }
    }

    /**
     * Check if this is a search for a specific alert
     */
    public boolean isAlertSpecificSearch() {
        return alertId != null;
    }

    /**
     * Check if this search has time range filters
     */
    public boolean hasTimeRangeFilter() {
        return startTime != null || endTime != null;
    }

    /**
     * Check if this search has status filter
     */
    public boolean hasStatusFilter() {
        return pushStatus != null;
    }

    /**
     * Check if this search has type filter
     */
    public boolean hasTypeFilter() {
        return pushType != null;
    }
}
