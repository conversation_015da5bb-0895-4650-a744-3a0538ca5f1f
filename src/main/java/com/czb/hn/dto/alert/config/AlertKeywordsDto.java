package com.czb.hn.dto.alert.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO for alert keywords configuration
 * Defines the keywords that trigger alerts and their description
 */
public record AlertKeywordsDto(
    @Schema(description = "List of alert keywords", example = "[\"critical\", \"urgent\", \"breaking news\"]", requiredMode = Schema.RequiredMode.REQUIRED)
    List<String> keywords,
    
    @Schema(description = "Description of the alert keywords", example = "Keywords for critical news monitoring")
    String description
) {
    // Compact canonical constructor for validation
    public AlertKeywordsDto {
        if (keywords == null || keywords.isEmpty()) {
            throw new IllegalArgumentException("Alert keywords cannot be null or empty");
        }
        
        // Validate individual keywords
        for (String keyword : keywords) {
            if (keyword == null || keyword.isBlank()) {
                throw new IllegalArgumentException("Individual keywords cannot be null or blank");
            }
            if (keyword.length() > 100) {
                throw new IllegalArgumentException("Individual keywords cannot exceed 100 characters");
            }
        }
        
        // Check for duplicates
        if (keywords.size() != keywords.stream().distinct().count()) {
            throw new IllegalArgumentException("Duplicate keywords are not allowed");
        }
        
        // Limit total number of keywords
        if (keywords.size() > 50) {
            throw new IllegalArgumentException("Cannot have more than 50 alert keywords");
        }
        
        if (description != null && description.length() > 500) {
            throw new IllegalArgumentException("Keywords description cannot exceed 500 characters");
        }
    }
}
