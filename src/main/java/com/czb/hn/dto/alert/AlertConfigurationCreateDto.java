package com.czb.hn.dto.alert;

import com.czb.hn.dto.alert.config.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO for creating new alert configurations
 * Contains all necessary fields for alert configuration creation with
 * validation
 */
public record AlertConfigurationCreateDto(
        @Schema(description = "Configuration name", example = "Critical News Alert", requiredMode = Schema.RequiredMode.REQUIRED) String name,

        @Schema(description = "Configuration description", example = "Alert configuration for critical news monitoring") String description,

        @Schema(description = "Associated plan ID", example = "1") Long planId,

        @Schema(description = "Enterprise ID", example = "enterprise123", requiredMode = Schema.RequiredMode.REQUIRED) String enterpriseId,

        @Schema(description = "Whether the alert is enabled", example = "true") Boolean enabled,

        @Schema(description = "Alert keywords configuration", requiredMode = Schema.RequiredMode.REQUIRED) AlertKeywordsDto alertKeywords,

        @Schema(description = "Content filtering settings") ContentSettingsDto contentSettings,

        @Schema(description = "Alert threshold conditions") ThresholdSettingsDto thresholdSettings,

        @Schema(description = "Alert level classification settings") LevelSettingsDto levelSettings,

        @Schema(description = "Alert reception and notification settings", requiredMode = Schema.RequiredMode.REQUIRED) ReceptionSettingsDto receptionSettings,

        @Schema(description = "User creating the configuration", example = "admin") String createdBy,

        @Schema(description = "Reason for creating this configuration", example = "Initial setup for news monitoring") String changeReason) {
    // Compact canonical constructor for validation
    public AlertConfigurationCreateDto {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Configuration name cannot be null or blank");
        }
        if (name.length() > 255) {
            throw new IllegalArgumentException("Configuration name cannot exceed 255 characters");
        }
        if (enterpriseId == null || enterpriseId.isBlank()) {
            throw new IllegalArgumentException("Enterprise ID cannot be null or blank");
        }
        if (enterpriseId.length() > 255) {
            throw new IllegalArgumentException("Enterprise ID cannot exceed 255 characters");
        }
        if (alertKeywords == null) {
            throw new IllegalArgumentException("Alert keywords configuration cannot be null");
        }
        // ContentSettings, ThresholdSettings, and LevelSettings are now optional
        // ReceptionSettings is required and must have at least one valid recipient
        if (receptionSettings == null) {
            throw new IllegalArgumentException("Reception settings cannot be null");
        }
        validateReceptionSettings(receptionSettings);
        if (description != null && description.length() > 1000) {
            throw new IllegalArgumentException("Description cannot exceed 1000 characters");
        }
        if (changeReason != null && changeReason.length() > 500) {
            throw new IllegalArgumentException("Change reason cannot exceed 500 characters");
        }

        // Set default values
        if (enabled == null) {
            enabled = true;
        }
    }

    /**
     * Validates that reception settings contain at least one valid recipient
     * 
     * @param receptionSettings the reception settings to validate
     * @throws IllegalArgumentException if no valid recipients are configured
     */
    private static void validateReceptionSettings(ReceptionSettingsDto receptionSettings) {
        if (receptionSettings.receptionMethods() == null) {
            throw new IllegalArgumentException("Reception methods configuration cannot be null");
        }

        boolean hasValidRecipient = false;

        // Check email recipients
        if (receptionSettings.receptionMethods().email() != null &&
                Boolean.TRUE.equals(receptionSettings.receptionMethods().email().enabled()) &&
                receptionSettings.receptionMethods().email().recipients() != null &&
                !receptionSettings.receptionMethods().email().recipients().isEmpty()) {
            hasValidRecipient = true;
        }

        // Check SMS recipients
        if (receptionSettings.receptionMethods().sms() != null &&
                Boolean.TRUE.equals(receptionSettings.receptionMethods().sms().enabled()) &&
                receptionSettings.receptionMethods().sms().recipients() != null &&
                !receptionSettings.receptionMethods().sms().recipients().isEmpty()) {
            hasValidRecipient = true;
        }

        if (!hasValidRecipient) {
            throw new IllegalArgumentException(
                    "At least one valid recipient must be configured in reception settings (email or SMS with enabled status and non-empty recipients list)");
        }
    }
}
