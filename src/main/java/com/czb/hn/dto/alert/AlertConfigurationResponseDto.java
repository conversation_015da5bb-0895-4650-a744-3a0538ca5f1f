package com.czb.hn.dto.alert;

import com.czb.hn.dto.alert.config.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * DTO for alert configuration responses
 * Contains complete configuration data for API responses
 */
public record AlertConfigurationResponseDto(
        @Schema(description = "Configuration ID", example = "1") Long id,

        @Schema(description = "Configuration name", example = "Critical News Alert") String name,

        @Schema(description = "Configuration description", example = "Alert configuration for critical news monitoring") String description,

        @Schema(description = "Associated plan ID", example = "1") Long planId,

        @Schema(description = "Enterprise ID", example = "enterprise123") String enterpriseId,

        @Schema(description = "Whether the alert is enabled", example = "true") Boolean enabled,

        @Schema(description = "Alert keywords configuration") AlertKeywordsDto alertKeywords,

        @Schema(description = "Content filtering settings") ContentSettingsDto contentSettings,

        @Schema(description = "Alert threshold conditions") ThresholdSettingsDto thresholdSettings,

        @Schema(description = "Alert level classification settings") LevelSettingsDto levelSettings,

        @Schema(description = "Alert reception and notification settings") ReceptionSettingsDto receptionSettings,

        @Schema(description = "Creation timestamp", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createdAt,

        @Schema(description = "Last update timestamp", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updatedAt,

        @Schema(description = "User who created the configuration", example = "admin") String createdBy,

        @Schema(description = "User who last updated the configuration", example = "admin") String updatedBy,

        @Schema(description = "Current version number", example = "3") Integer currentVersion,

        @Schema(description = "Whether the configuration is active", example = "true") Boolean isActive,

        @Schema(description = "Last snapshot creation timestamp", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 16:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime lastSnapshotAt) {
    // No validation needed for response DTOs
}
