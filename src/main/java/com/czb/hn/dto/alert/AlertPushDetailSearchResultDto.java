package com.czb.hn.dto.alert;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Alert Push Detail Search Result DTO
 * Contains paginated search results for push detail records
 */
public record AlertPushDetailSearchResultDto(
        @Schema(description = "推送详情列表") List<AlertPushDetailResponseDto> pushDetails,

        @Schema(description = "总记录数", example = "150") long totalElements,

        @Schema(description = "总页数", example = "8") int totalPages,

        @Schema(description = "当前页码", example = "0") int currentPage,

        @Schema(description = "每页大小", example = "20") int pageSize,

        @Schema(description = "是否为第一页", example = "true") boolean isFirst,

        @Schema(description = "是否为最后一页", example = "false") boolean isLast,

        @Schema(description = "是否有下一页", example = "true") boolean hasNext,

        @Schema(description = "是否有上一页", example = "false") boolean hasPrevious) {
    /**
     * Check if the search returned any results
     */
    public boolean hasResults() {
        return pushDetails != null && !pushDetails.isEmpty();
    }

    /**
     * Get the number of results in current page
     */
    public int getCurrentPageSize() {
        return pushDetails != null ? pushDetails.size() : 0;
    }

    /**
     * Check if pagination is needed
     */
    public boolean isPaginated() {
        return totalPages > 1;
    }
}
