package com.czb.hn.dto.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Alert Statistics DTO
 * 预警统计信息DTO，包含指定时间段内指定方案的预警统计数据
 */
@Schema(description = "预警统计信息DTO")
public record AlertStatisticsDto(
        @Schema(description = "方案ID", example = "1") Long planId,

        @Schema(description = "方案名称", example = "华能资本舆情监控方案") String planName,

        @Schema(description = "统计开始时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

        @Schema(description = "统计结束时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 18:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,

        @Schema(description = "预警总条数", example = "156") Long totalAlertCount,

        @Schema(description = "敏感信息类型预警条数", example = "42") Long sensitiveAlertCount) {
    /**
     * 紧凑构造函数，验证参数
     */
    public AlertStatisticsDto {
        if (planId == null) {
            throw new IllegalArgumentException("方案ID不能为空");
        }
        if (planName == null || planName.isBlank()) {
            throw new IllegalArgumentException("方案名称不能为空");
        }
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        if (totalAlertCount == null) {
            totalAlertCount = 0L;
        }
        if (sensitiveAlertCount == null) {
            sensitiveAlertCount = 0L;
        }
        if (sensitiveAlertCount > totalAlertCount) {
            throw new IllegalArgumentException("敏感预警条数不能大于总预警条数");
        }
    }
}
