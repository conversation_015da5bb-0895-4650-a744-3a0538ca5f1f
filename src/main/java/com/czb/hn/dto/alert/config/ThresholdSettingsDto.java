package com.czb.hn.dto.alert.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO for alert threshold settings
 * Defines the conditions that must be met to trigger alerts
 */
public record ThresholdSettingsDto(
        @Schema(description = "Condition relationship", example = "AND", allowableValues = {
                "AND", "OR" }) String conditionRelation,

        @Schema(description = "Interaction count threshold configuration") ThresholdConditionDto interactionCount,

        @Schema(description = "Fans count threshold configuration") ThresholdConditionDto fansCount,

        @Schema(description = "Read count threshold configuration") ThresholdConditionDto readCount,

        @Schema(description = "Similar article count threshold configuration") ThresholdConditionDto similarArticleCount,

        @Schema(description = "Keyword frequency conditions (max 3 groups)") List<KeywordFrequencyDto> keywordFrequency){
    // Compact canonical constructor for validation
    public ThresholdSettingsDto {
        // Set default values
        if (conditionRelation == null) {
            conditionRelation = "OR";
        }

        // Validate enum values
        if (!List.of("AND", "OR").contains(conditionRelation)) {
            throw new IllegalArgumentException("conditionRelation must be either 'AND' or 'OR'");
        }

        // Validate keyword frequency list
        if (keywordFrequency != null && keywordFrequency.size() > 3) {
            throw new IllegalArgumentException("Cannot have more than 3 keyword frequency conditions");
        }

        // Validate that at least one condition is enabled
        boolean hasEnabledCondition = false;
        if (interactionCount != null && interactionCount.enabled()) {
            hasEnabledCondition = true;
        }
        if (fansCount != null && fansCount.enabled()) {
            hasEnabledCondition = true;
        }
        if (readCount != null && readCount.enabled()) {
            hasEnabledCondition = true;
        }
        if (similarArticleCount != null && similarArticleCount.enabled()) {
            hasEnabledCondition = true;
        }
        if (keywordFrequency != null && !keywordFrequency.isEmpty()) {
            hasEnabledCondition = true;
        }

        if (!hasEnabledCondition) {
            throw new IllegalArgumentException("At least one threshold condition must be enabled");
        }
    }

    /**
     * DTO for individual threshold conditions
     */
    public record ThresholdConditionDto(
            @Schema(description = "Whether this condition is enabled", example = "true") Boolean enabled,

            @Schema(description = "Threshold value", example = "100") Long threshold) {
        public ThresholdConditionDto {
            if (enabled == null) {
                enabled = false;
            }
            if (enabled && (threshold == null || threshold < 0)) {
                throw new IllegalArgumentException("Threshold value must be non-negative when condition is enabled");
            }
        }
    }

    /**
     * DTO for keyword frequency conditions
     */
    public record KeywordFrequencyDto(
            @Schema(description = "Keyword to monitor", example = "urgent", requiredMode = Schema.RequiredMode.REQUIRED) String keyword,

            @Schema(description = "Minimum frequency required", example = "3", requiredMode = Schema.RequiredMode.REQUIRED) Integer frequency) {
        public KeywordFrequencyDto {
            if (keyword == null || keyword.isBlank()) {
                throw new IllegalArgumentException("Keyword cannot be null or blank");
            }
            if (keyword.length() > 100) {
                throw new IllegalArgumentException("Keyword cannot exceed 100 characters");
            }
            if (frequency == null || frequency < 1) {
                throw new IllegalArgumentException("Frequency must be at least 1");
            }
        }
    }
}
