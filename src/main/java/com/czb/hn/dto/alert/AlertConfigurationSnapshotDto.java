package com.czb.hn.dto.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * DTO for alert configuration snapshots
 * Contains snapshot metadata and data for version control
 */
public record AlertConfigurationSnapshotDto(
        @Schema(description = "Snapshot ID", example = "1") Long id,

        @Schema(description = "Configuration ID", example = "1") Long configurationId,

        @Schema(description = "Version number", example = "3") Integer versionNumber,

        @Schema(description = "Complete snapshot data as JSON string") String snapshotData,

        @Schema(description = "Snapshot creation timestamp", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 16:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createdAt,

        @Schema(description = "User who created the snapshot", example = "admin") String createdBy,

        @Schema(description = "Reason for creating this snapshot", example = "Updated threshold values") String changeReason,

        @Schema(description = "Whether this is the currently active snapshot", example = "true") Boolean isActive,

        @Schema(description = "Operation type that triggered this snapshot", example = "UPDATE") String operationType,

        @Schema(description = "Size of the snapshot data in bytes", example = "2048") Long dataSize,

        @Schema(description = "Checksum for data integrity verification") String checksum) {
    // No validation needed for response DTOs
}
