package com.czb.hn.dto.alert.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO for content filtering settings
 * Defines various filtering criteria for alert content
 */
public record ContentSettingsDto(
                @Schema(description = "Information sensitivity type", example = "1", allowableValues = {
                                "1", "2", "3" }) String sensitivityType,

                @Schema(description = "Source types to monitor", example = "[\"wb\", \"wx\"]", allowableValues = {
                                "hdlt", "wb", "wx", "zmtapp", "sp", "szb", "wz" }) List<String> sourceTypes,

                @Schema(description = "Content types to include", example = "[\"1\", \"2\"]", allowableValues = {
                                "1", "2", "3", "4" }) List<String> contentTypes,

                @Schema(description = "Result display rule", example = "1", allowableValues = { "1",
                                "2" }) String resultDisplay,

                @Schema(description = "Weibo verification types", example = "[\"-1\", \"1\"]", allowableValues = {
                                "-1", "0", "1", "200",
                                "600" }) List<String> weiboVerification,

                @Schema(description = "Information source levels", example = "[\"央级\", \"省级\"]", allowableValues = {
                                "央级", "省级", "地市", "重点", "中小",
                                "企业商业" }) List<String> sourceLevel,

                @Schema(description = "Content category", example = "1", allowableValues = { "1",
                                "2" }) String contentCategory){

        // Compact canonical constructor for validation
        public ContentSettingsDto {

                // Validate enum values
                validateEnumValue(sensitivityType, List.of("1", "2", "3"),
                                "sensitivityType");
                validateEnumValue(resultDisplay, List.of("1", "2"),
                                "resultDisplay");

                validateEnumValue(contentCategory, List.of("1", "2"), "contentCategory");

                // Validate list enum values
                validateListEnumValues(sourceTypes,
                                List.of("hdlt", "wb", "wx", "zmtapp", "sp", "szb", "wz"),
                                "sourceTypes");
                validateListEnumValues(contentTypes, List.of("1", "2", "3", "4"),
                                "contentTypes");
                validateListEnumValues(weiboVerification,
                                List.of("-1", "0", "1", "200", "600"),
                                "weiboVerification");
                validateListEnumValues(sourceLevel,
                                List.of("央级", "省级", "地市", "重点", "中小",
                                                "企业商业"),
                                "sourceLevel");
        }

        private static void validateEnumValue(String value, List<String> allowedValues, String fieldName) {
                if (value != null && !allowedValues.contains(value)) {
                        throw new IllegalArgumentException(fieldName + " must be one of: " + allowedValues);
                }
        }

        private static void validateListEnumValues(List<String> values, List<String> allowedValues, String fieldName) {
                if (values != null) {
                        for (String value : values) {
                                if (value != null && !allowedValues.contains(value)) {
                                        throw new IllegalArgumentException(
                                                        fieldName + " contains invalid value '" + value
                                                                        + "'. Must be one of: " + allowedValues);
                                }
                        }
                }
        }
}
