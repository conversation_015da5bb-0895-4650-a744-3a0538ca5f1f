package com.czb.hn.dto.alert.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO for alert reception and notification settings
 * Defines how and when alerts are delivered to recipients
 */
public record ReceptionSettingsDto(
    @Schema(description = "Reception time schedule", example = "DAILY", allowableValues = {"DAILY", "WORKDAYS", "HOLIDAYS"})
    String receptionTime,
    
    @Schema(description = "Alert interval in minutes (minimum 30)", example = "30")
    Integer alertInterval,
    
    @Schema(description = "Reception time period")
    TimePeriodDto receptionPeriod,
    
    @Schema(description = "Whether information push is enabled during non-alert hours", example = "true")
    Boolean infoPush,
    
    @Schema(description = "Reception methods configuration")
    ReceptionMethodsDto receptionMethods,
    
    @Schema(description = "Whether no-alert notification is enabled", example = "true")
    Boolean noAlertNotification
) {
    // Compact canonical constructor for validation
    public ReceptionSettingsDto {
        // Set default values
        if (receptionTime == null) {
            receptionTime = "DAILY";
        }
        if (alertInterval == null) {
            alertInterval = 30;
        }
        if (infoPush == null) {
            infoPush = false;
        }
        if (noAlertNotification == null) {
            noAlertNotification = false;
        }
        
        // Validate enum values
        if (!List.of("DAILY", "WORKDAYS", "HOLIDAYS").contains(receptionTime)) {
            throw new IllegalArgumentException("receptionTime must be one of: DAILY, WORKDAYS, HOLIDAYS");
        }
        
        // Validate alert interval
        if (alertInterval < 30) {
            throw new IllegalArgumentException("Alert interval must be at least 30 minutes");
        }
        if (alertInterval > 1440) { // 24 hours
            throw new IllegalArgumentException("Alert interval cannot exceed 1440 minutes (24 hours)");
        }
        
        // Validate reception methods
        if (receptionMethods == null) {
            throw new IllegalArgumentException("Reception methods configuration cannot be null");
        }
        
        // Validate that at least one reception method is enabled
        boolean hasEnabledMethod = false;
        if (receptionMethods.email() != null && receptionMethods.email().enabled()) {
            hasEnabledMethod = true;
        }
        if (receptionMethods.sms() != null && receptionMethods.sms().enabled()) {
            hasEnabledMethod = true;
        }
        
        if (!hasEnabledMethod) {
            throw new IllegalArgumentException("At least one reception method (email or SMS) must be enabled");
        }
    }
    
    /**
     * DTO for time period definition
     */
    public record TimePeriodDto(
        @Schema(description = "Start time in HH:mm format", example = "00:00")
        String start,
        
        @Schema(description = "End time in HH:mm format", example = "24:00")
        String end
    ) {
        public TimePeriodDto {
            if (start == null) {
                start = "00:00";
            }
            if (end == null) {
                end = "24:00";
            }
            
            // Validate time format
            if (!start.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$") && !start.equals("24:00")) {
                throw new IllegalArgumentException("Start time must be in HH:mm format (00:00-23:59)");
            }
            if (!end.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$") && !end.equals("24:00")) {
                throw new IllegalArgumentException("End time must be in HH:mm format (00:00-23:59 or 24:00)");
            }
        }
    }
    
    /**
     * DTO for reception methods configuration
     */
    public record ReceptionMethodsDto(
        @Schema(description = "Email notification configuration")
        EmailConfigDto email,
        
        @Schema(description = "SMS notification configuration")
        SmsConfigDto sms
    ) {
        // No additional validation needed here
    }
    
    /**
     * DTO for email notification configuration
     */
    public record EmailConfigDto(
        @Schema(description = "Whether email notifications are enabled", example = "true")
        Boolean enabled,
        
        @Schema(description = "Email recipients list")
        List<EmailRecipientDto> recipients
    ) {
        public EmailConfigDto {
            if (enabled == null) {
                enabled = false;
            }
            if (enabled && (recipients == null || recipients.isEmpty())) {
                throw new IllegalArgumentException("Email recipients cannot be empty when email is enabled");
            }
            if (recipients != null && recipients.size() > 20) {
                throw new IllegalArgumentException("Cannot have more than 20 email recipients");
            }
        }
    }
    
    /**
     * DTO for SMS notification configuration
     */
    public record SmsConfigDto(
        @Schema(description = "Whether SMS notifications are enabled", example = "true")
        Boolean enabled,
        
        @Schema(description = "SMS recipients list")
        List<SmsRecipientDto> recipients
    ) {
        public SmsConfigDto {
            if (enabled == null) {
                enabled = false;
            }
            if (enabled && (recipients == null || recipients.isEmpty())) {
                throw new IllegalArgumentException("SMS recipients cannot be empty when SMS is enabled");
            }
            if (recipients != null && recipients.size() > 10) {
                throw new IllegalArgumentException("Cannot have more than 10 SMS recipients");
            }
        }
    }
    
    /**
     * DTO for email recipient
     */
    public record EmailRecipientDto(
        @Schema(description = "Recipient name", example = "John Doe", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,
        
        @Schema(description = "Email address", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
        String email
    ) {
        public EmailRecipientDto {
            if (name == null || name.isBlank()) {
                throw new IllegalArgumentException("Recipient name cannot be null or blank");
            }
            if (name.length() > 100) {
                throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
            }
            if (email == null || email.isBlank()) {
                throw new IllegalArgumentException("Email address cannot be null or blank");
            }
            if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
                throw new IllegalArgumentException("Invalid email address format");
            }
        }
    }
    
    /**
     * DTO for SMS recipient
     */
    public record SmsRecipientDto(
        @Schema(description = "Recipient name", example = "John Doe", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,
        
        @Schema(description = "Phone number", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
        String phone
    ) {
        public SmsRecipientDto {
            if (name == null || name.isBlank()) {
                throw new IllegalArgumentException("Recipient name cannot be null or blank");
            }
            if (name.length() > 100) {
                throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
            }
            if (phone == null || phone.isBlank()) {
                throw new IllegalArgumentException("Phone number cannot be null or blank");
            }
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                throw new IllegalArgumentException("Invalid phone number format (must be Chinese mobile number)");
            }
        }
    }
}
