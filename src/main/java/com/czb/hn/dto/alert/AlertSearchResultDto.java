package com.czb.hn.dto.alert;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Alert Search Result DTO
 * 预警信息搜索结果DTO，包含分页信息和统计数据
 */
public record AlertSearchResultDto(
        @Schema(description = "搜索结果列表") List<AlertResultResponseDto> content,

        @Schema(description = "分页信息") PageInfo pageInfo,

        @Schema(description = "搜索条件") AlertSearchCriteriaDto searchCriteria) {

    /**
     * 分页信息
     */
    public record PageInfo(
            @Schema(description = "当前页码", example = "0") int currentPage,

            @Schema(description = "页大小", example = "20") int pageSize,

            @Schema(description = "总页数", example = "5") int totalPages,

            @Schema(description = "总记录数", example = "100") long totalElements,

            @Schema(description = "是否为第一页", example = "true") boolean isFirst,

            @Schema(description = "是否为最后一页", example = "false") boolean isLast,

            @Schema(description = "是否有下一页", example = "true") boolean hasNext,

            @Schema(description = "是否有上一页", example = "false") boolean hasPrevious) {
    }
}
