package com.czb.hn.dto.bulletin;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 简报任务请求DTO
 */
public record BulletinJobRequest(
        Long id, // 更新时使用
        Long planId,
        String name, // 任务名称
        String bulletinType, // 简报类型：DAILY/WEEKLY/MONTHLY
        String sendTime, // 发送时间，如"10:00"
        String executionDay, // 执行日，周报为星期几(1-7)，月报为每月几号(1-31)
        Boolean enabled, // 是否启用
        String bulletinTitle, // 简报标题
        List<String> emailReceivers, // 邮件接收人
        List<String> smsReceivers, // 短信接收人
        String params,
        List<String> groupIds // 部门/组织IDs

) {
    /**
     * 紧凑型规范构造函数，用于验证输入参数
     */
    public BulletinJobRequest {
        // 默认值处理
        if (enabled == null) {
            enabled = true;
        }
        if (StringUtils.isBlank(executionDay)) {
            executionDay = "1";
        }
    }
}