package com.czb.hn.dto.bulletin;

import java.time.LocalDate;
import java.util.List;

/**
 * 简报参数DTO
 */
public record BulletinParams(
    // 基本信息
    String bulletinTitle, // 简报标题
    
    // 接收人配置
    List<String> emailReceivers, // 邮件接收人
    List<String> smsReceivers, // 短信接收人
    String params,
    // 发送规则配置
    Boolean sendOnNonWorkday, // 是否仅在非工作日发送
    Boolean needCompensate, // 是否需要补发
    LocalDate lastSendDate, // 上次发送日期
    
    // 数据范围配置
    List<String> groupIds, // 部门/组织IDs
    Integer dataRange // 数据范围：日报(1天)，周报(7天)，月报(30天)
) {
    /**
     * 紧凑型规范构造函数，用于验证输入参数
     */
    public BulletinParams {
        // 默认值处理
        if (sendOnNonWorkday == null) {
            sendOnNonWorkday = true;
        }
        if (needCompensate == null) {
            needCompensate = true;
        }
    }
} 