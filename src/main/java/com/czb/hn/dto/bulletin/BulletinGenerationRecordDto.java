package com.czb.hn.dto.bulletin;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 简报生成记录DTO
 */
public record BulletinGenerationRecordDto(
    Long id,
    String bulletinTitle,
    String bulletinType,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime startTime,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime endTime,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime generationTime,
    Long jobId,
    Boolean canPush,
    String status
) {
    /**
     * 紧凑的规范构造函数，用于验证输入参数
     */
    public BulletinGenerationRecordDto {
        if (bulletinTitle == null || bulletinTitle.isBlank()) {
            throw new IllegalArgumentException("简报标题不能为空");
        }
        if (bulletinType == null || bulletinType.isBlank()) {
            throw new IllegalArgumentException("简报类型不能为空");
        }
    }
} 