package com.czb.hn.dto;

import java.time.LocalDateTime;

/**
 * DTO for making requests to Sina API
 */
public record SinaApiRequestDto(
    String keyword,
    String category,
    LocalDateTime startTime,
    LocalDateTime endTime,
    Integer pageSize,
    Integer pageNum,
    String sortField,
    String sortOrder
) {
    // Compact canonical constructor for validation
    public SinaApiRequestDto {
        // Validate pagination parameters
        if (pageSize != null && pageSize <= 0) {
            throw new IllegalArgumentException("Page size must be greater than zero");
        }
        if (pageNum != null && pageNum <= 0) {
            throw new IllegalArgumentException("Page number must be greater than zero");
        }
        
        // Validate time range
        if (startTime != null && endTime != null && endTime.isBefore(startTime)) {
            throw new IllegalArgumentException("End time cannot be before start time");
        }
        
        // Validate sort order if provided
        if (sortOrder != null && !sortOrder.equals("asc") && !sortOrder.equals("desc")) {
            throw new IllegalArgumentException("Sort order must be either 'asc' or 'desc'");
        }
    }
} 