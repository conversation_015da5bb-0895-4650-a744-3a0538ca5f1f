package com.czb.hn.dto.auth;

import cn.com.ycfin.onepass.client.model.Group;
import cn.com.ycfin.onepass.client.model.UserRole;
import cn.com.ycfin.onepass.client.security.OnePassAuthenticationToken;
import com.fasterxml.jackson.databind.JsonNode;
import com.nimbusds.openid.connect.sdk.token.OIDCTokens;
import org.springframework.security.core.GrantedAuthority;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;

public class OnePassToken extends OnePassAuthenticationToken {

    private final String userId;

    private final String name;

    private final List<UserRole> userRoles;

    private final List<Group> groups;

    public OnePassToken(OIDCTokens oidcTokens, JsonNode userinfo,
                                        String userId, String name, List<UserRole> userRoles, List<Group> groups, Collection<? extends GrantedAuthority> authorities) throws ParseException {
        super(oidcTokens, userinfo, authorities);
        this.userId = userId;
        this.name = name;
        this.userRoles = userRoles;
        this.groups = groups;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getUserId() {
        return userId;
    }

    public List<UserRole> getUserRoles() {
        return userRoles;
    }

    public List<Group> getGroups() {
        return groups;
    }
}
