package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import java.util.List;

@Value
public class MediaTierDto {
    @Schema(description = "媒体级别")
    String mediaTier;

    @Schema(description = "数量")
    long mediaCount;

    @Schema(description = "占比")
    Double mediaRatio;

    // todo: 添加热门媒体
//    @Schema(description = "热门媒体")
//    List<String> hotMedia;
}
