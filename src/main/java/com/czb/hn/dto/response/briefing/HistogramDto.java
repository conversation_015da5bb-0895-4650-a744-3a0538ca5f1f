package com.czb.hn.dto.response.briefing;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;
import java.time.LocalDateTime;

/**
 * 时间统计DTO
 * 用于封装时间维度的聚合统计结果
 */
@Value
public class HistogramDto {
    @Schema(description = "时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime time; // 时间戳

    @Schema(description = "文档数量")
    Long count; // 文档数量
}