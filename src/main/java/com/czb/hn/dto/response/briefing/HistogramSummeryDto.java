package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Value;

import java.util.List;

@Data
public class HistogramSummeryDto {

    @Schema(description = "敏感信息分布")
    SensitivityTypeDistributionDto sensitiveInfoDistribution;

    @Schema(description = "敏感趋势图")
    List<HistogramDto> sensitiveInfoTrend;

    @Schema(description = "中性信息分布")
    SensitivityTypeDistributionDto neutralInfoDistribution;

    @Schema(description = "中性趋势图")
    List<HistogramDto> neutralInfoTrend;

    @Schema(description = "非敏感信息分布")
    SensitivityTypeDistributionDto nonSensitiveInfoDistribution;

    @Schema(description = "非敏感趋势图")
    List<HistogramDto> nonSensitiveInfoTrend;
}
