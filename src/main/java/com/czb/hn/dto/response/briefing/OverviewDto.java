package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class OverviewDto {
    @Schema(description = "相关内容数")
    long contentTotal;

    @Schema(description = "相关评论数")
    long commentTotal;

    @Schema(description = "敏感信息条数")
    long sensitiveTotal;

    @Schema(description = "敏感信息占比")
    double sensitiveRatio;

    @Schema(description = "非敏感信息条数")
    long nonSensitiveTotal;

    @Schema(description = "非敏感信息占比")
    double nonSensitiveRatio;

    @Schema(description = "中性条数")
    long neutralTotal;

    @Schema(description = "中性占比")
    double neutralRatio;

    @Schema(description = "主要报道来源")
    String primarySource;

    @Schema(description = "媒体来源分布")
    List<MediaDistributionDto> mediaDistribution;

    @Schema(description = "转发量")
    long forwardCount;

    @Schema(description = "评论量")
    long replyCount;

    @Schema(description = "点赞量")
    long likeCount;

    @Schema(description = "阅读量")
    long readCount;
}
