package com.czb.hn.dto.response;

import com.czb.hn.document.SinaNewsDocument;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 新浪舆情数据响应DTO
 * 用于控制器返回数据，避免直接暴露ES文档结构
 */
@Data
@NoArgsConstructor
@Schema(description = "新浪舆情数据响应对象")
public class SinaNewsResponseDto {

    @Schema(description = "内容ID")
    private String contentId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容摘要")
    private String summary;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "来源网站")
    private String sourceWebsite;

    @Schema(description = "媒体类型")
    private String mediaType;

    @Schema(description = "媒体等级")
    private String mediaLevel;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @Schema(description = "情感类型")
    private String emotion;

    @Schema(description = "情感分数")
    private Double sentimentScore;

    @Schema(description = "敏感度级别")
    private Integer sensitivityType;

    @Schema(description = "分类")
    private String category;

    @Schema(description = "关键词")
    private String keywords;

    @Schema(description = "评论数")
    private Long commentCount;

    @Schema(description = "转发数")
    private Long forwardCount;

    @Schema(description = "点赞数")
    private Long praiseCount;

    @Schema(description = "互动总数")
    private Long interactionCount;

    @Schema(description = "影响力分数")
    private Double influenceScore;

    /**
     * 将Elasticsearch文档转换为DTO
     *
     * @param document Elasticsearch文档
     * @return DTO对象
     */
    public static SinaNewsResponseDto fromDocument(SinaNewsDocument document) {
        if (document == null) {
            return null;
        }

        SinaNewsResponseDto dto = new SinaNewsResponseDto();

        dto.setContentId(document.getContentId());
        dto.setTitle(document.getTitle());
        dto.setSummary(document.getSummary());
        dto.setSource(document.getSource());
        dto.setSourceWebsite(document.getSourceWebsite());
        dto.setMediaType(document.getMediaType());
        dto.setMediaLevel(document.getMediaLevel());
        dto.setAuthor(document.getAuthor());
        dto.setUrl(document.getUrl());
        dto.setPublishTime(document.getPublishTime());
        dto.setEmotion(document.getEmotion());
        dto.setSentimentScore(document.getSentimentScore());
        dto.setSensitivityType(document.getSensitivityType());
        dto.setCommentCount(document.getCommentCount());
        dto.setForwardCount(document.getForwardCount());
        dto.setPraiseCount(document.getPraiseCount());
        dto.setInteractionCount(document.getInteractionCount());

        return dto;
    }
}