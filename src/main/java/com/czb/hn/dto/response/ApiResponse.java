package com.czb.hn.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API统一响应格式")
public class ApiResponse<T> {
  @Schema(description = "响应结果", example = "SUCCESS", requiredMode = Schema.RequiredMode.REQUIRED)
  private String result;    // SUCCESS or ERROR
  
  @Schema(description = "响应消息", example = "操作成功")
  private String message;   // success or error message
  
  @Schema(description = "响应数据")
  private T data;           // return object from service class, if successful

  public static <T> ApiResponse<T> success(T data) {
    return new ApiResponse<>("SUCCESS", "操作成功", data);
  }

  public static <T> ApiResponse<T> error(String message) {
    return new ApiResponse<>("ERROR", message, null);
  }

  public static <T> ApiResponse<T> error(String message, T data) {    
    return new ApiResponse<>("ERROR", message, data);
  }

}
