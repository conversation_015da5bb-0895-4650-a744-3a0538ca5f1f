package com.czb.hn.dto.response.briefing;

import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import java.time.LocalDateTime;
import java.util.List;

@Value
public class BriefingSummaryDto {
    @Schema(description = "简报开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime startTime;

    @Schema(description = "简报结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime endTime;

    @Schema(description = "监测概述")
    OverviewDto overview;

    @Schema(description = "敏感信息趋势图")
    HistogramSummeryDto histogramSummary;

    @Schema(description = "媒体来源分布")
    List<MediaDistributionDto> mediaDistribution;

    @Schema(description = "媒体参与详情")
    MediaTierDto mediaDetail;

    @Schema(description = "高频词")
    List<HighFrequencyWordDto> highFrequencyWords;

    @Schema(description = "情感分布")
    List<EmotionDistributionDto> emotionDistribution;

    @Schema(description = "敏感信息导读")
    List<SinaNewsSearchResponseDto> sensitiveInfoSummary;
}
