package com.czb.hn.dto.response.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 新浪舆情搜索结果DTO
 * 用于控制器返回数据，避免直接暴露ES文档结构
 */
@Data
@NoArgsConstructor
@Schema(description = "新浪舆情数据搜索结果对象")
@JsonIgnoreProperties(ignoreUnknown = true) //忽略所有不匹配字段
public class SinaNewsSearchResponseDto {

    @Schema(description = "内容ID")
    private String contentId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "高亮摘要")
    private Map<String, List<String>> highlight;

    @Schema(description = "信息属性")
    private Integer sensitivityType;

    @Schema(description = "是否原创")
    private Integer isOriginal;

    @Schema(description = "粉丝数")
    private Long authorFollowersCount;

    @Schema(description = "转发数")
    private Long forwardCount;

    @Schema(description = "评论数")
    private Long commentCount;

    @Schema(description = "点赞数")
    private Long praiseCount;

    @Schema(description = "阅读数")
    private Long lookingCount;

    @Schema(description = "行业信息")
    private List<String> secondTrades;

    @Schema(description = "涉及词")
    private List<Map<String,Integer>> highLightWords;

    @Schema(description = "发布地")
    private String publishProvince;

    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @Schema(description = "网页地址")
    private String url;

    @Schema(description = "相似文章数")
    private Long similarityNum;

    @Schema(description = "来源")
    private String captureWebsite;

}
