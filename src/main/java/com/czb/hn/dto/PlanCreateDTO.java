package com.czb.hn.dto;

import com.czb.hn.util.KeywordUtils;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "方案创建DTO")
public record PlanCreateDTO(
        @Schema(description = "方案名称", example = "华能资本舆情监控方案", requiredMode = Schema.RequiredMode.REQUIRED, minLength = 1, maxLength = 255) String name,

        @Schema(description = "方案描述", example = "针对华能资本的全面舆情监控方案，包括投资动态、市场表现、风险预警等多个维度的监控", maxLength = 1000) String description,

        @Schema(description = "监控关键词（支持逻辑运算符：+表示AND，|表示OR，@@转义+字符）", example = "华能资本+投资|华能集团+金融|新能源+华能", requiredMode = Schema.RequiredMode.REQUIRED, minLength = 1, maxLength = 5000) String monitorKeywords,

        @Schema(description = "排除关键词（使用|分隔，表示OR逻辑）", example = "广告|招聘|测试|Demo", maxLength = 2000) String excludeKeywords,

        @Schema(description = "企业ID", example = "enterprise123", requiredMode = Schema.RequiredMode.REQUIRED, minLength = 1, maxLength = 255) String enterpriseId) {
    public PlanCreateDTO {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Plan name cannot be null or blank");
        }
        if (monitorKeywords == null || monitorKeywords.isBlank()) {
            throw new IllegalArgumentException("Monitor keywords cannot be null or blank");
        }

        // 验证监控关键词格式（支持逻辑运算符）
        String monitorKeywordsError = KeywordUtils.getMonitorKeywordsValidationErrorMessage(monitorKeywords);
        if (monitorKeywordsError != null) {
            throw new IllegalArgumentException("Monitor keywords validation failed: " + monitorKeywordsError);
        }

        // 验证排除关键词格式（如果提供）
        if (excludeKeywords != null && !excludeKeywords.isBlank()) {
            String excludeKeywordsError = KeywordUtils.getExcludeKeywordsValidationErrorMessage(excludeKeywords);
            if (excludeKeywordsError != null) {
                throw new IllegalArgumentException("Exclude keywords validation failed: " + excludeKeywordsError);
            }
        }

        if (enterpriseId == null) {
            throw new IllegalArgumentException("Enterprise ID cannot be null");
        }
    }
}