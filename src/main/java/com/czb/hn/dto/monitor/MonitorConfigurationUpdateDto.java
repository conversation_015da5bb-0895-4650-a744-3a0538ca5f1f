package com.czb.hn.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public record MonitorConfigurationUpdateDto(
        @Schema(description = "Configuration ID")
        Long id,

        @Schema(description = "Configuration name", example = "华能资本")
        String name,

        @Schema(description = "Associated plan ID", example = "1")
        Long planId,

        @Schema(description = "Monitor time", example = "2")
        Integer time,

        @Schema(description = "Start time", example = "2025-07-01 00:00:00")
        String startTime,

        @Schema(description = "End time", example = "2025-07-01 23:59:59")
        String endTime,

        @Schema(description = "Sort rule", example = "1")
        Integer sortRule,

        @Schema(description = "Information sensitivity type", example = "0")
        Integer sensitivityType,

        @Schema(description = "Similarity display rule", example = "false")
        Boolean SimilarityDisplayRule,

        @Schema(description = "Match method", example = "0")
        Integer MatchMethod,

        @Schema(description = "Media type")
        List<String> MediaType,

        @Schema(description = "Media type second")
        List<String> MediaTypeSecond,

        @Schema(description = "Content type")
        List<Integer> ContentType,

        @Schema(description = "Is original", example = "0")
        Integer isOriginal,

        @Schema(description = "Image text mode", example = "0")
        Integer imageTextMode,

        @Schema(description = "Second trades")
        List<String> SecondTrades,

        @Schema(description = "Author followers count min", example = "0")
        Long authorFollowersCountMin,

        @Schema(description = "Author followers count max", example = "100000000")
        Long authorFollowersCountMax,

        @Schema(description = "Media level")
        List<String> MediaLevel
) {
        public MonitorConfigurationUpdateDto {
                if (SimilarityDisplayRule == false && sortRule ==3) {
                        throw new IllegalArgumentException("Similarity display rule cannot be false when sort rule is 相似文章数.");
                }
        }
}
