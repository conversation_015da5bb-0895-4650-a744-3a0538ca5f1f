package com.czb.hn.dto.billing;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * Subscription status DTO for API responses
 */
@Schema(description = "企业订阅状态信息")
public record SubscriptionStatusDto(
        @Schema(description = "企业ID", example = "enterprise_demo_001") 
        String enterpriseId,

        @Schema(description = "企业信用代码", example = "91110000000000001X") 
        String enterpriseCreditCode,

        @Schema(description = "订阅开始日期", example = "2024-01-01") 
        LocalDate startDate,

        @Schema(description = "订阅结束日期", example = "2024-12-31") 
        LocalDate endDate,

        @Schema(description = "订阅状态", example = "ACTIVE", allowableValues = {"ACTIVE", "EXPIRED", "SUSPENDED", "CANCELLED"}) 
        String status,

        @Schema(description = "是否有效（未过期且状态为ACTIVE）", example = "true") 
        Boolean isValid,

        @Schema(description = "距离过期天数（负数表示已过期）", example = "30") 
        Long daysUntilExpiration,

        @Schema(description = "是否自动续费", example = "false") 
        Boolean autoRenew,

        @Schema(description = "备注信息", example = "正常订阅") 
        String notes
) {
    public SubscriptionStatusDto {
        if (enterpriseId == null || enterpriseId.isBlank()) {
            throw new IllegalArgumentException("Enterprise ID cannot be null or blank");
        }
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        if (endDate == null) {
            throw new IllegalArgumentException("End date cannot be null");
        }
        if (status == null || status.isBlank()) {
            throw new IllegalArgumentException("Status cannot be null or blank");
        }
    }
}
