package com.czb.hn.dto.billing;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * Request DTO for extending subscription
 */
@Schema(description = "延长订阅请求")
public record ExtendSubscriptionRequest(
        @Schema(description = "新的结束日期", example = "2025-12-31", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "新的结束日期不能为空")
        LocalDate newEndDate,

        @Schema(description = "备注信息", example = "订阅延期")
        String notes
) {
    public ExtendSubscriptionRequest {
        // 验证逻辑在紧凑构造函数中
        if (newEndDate != null && newEndDate.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("新的结束日期不能早于今天");
        }
    }
}
