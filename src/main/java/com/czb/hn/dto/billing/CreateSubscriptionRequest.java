package com.czb.hn.dto.billing;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * Request DTO for creating new subscription
 */
@Schema(description = "创建订阅请求")
public record CreateSubscriptionRequest(
        @Schema(description = "企业ID", example = "enterprise_demo_001", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "企业ID不能为空")
        String enterpriseId,

        @Schema(description = "企业信用代码（可选）", example = "91110000000000001X")
        String enterpriseCreditCode,

        @Schema(description = "订阅开始日期", example = "2024-01-01", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "开始日期不能为空")
        LocalDate startDate,

        @Schema(description = "订阅结束日期", example = "2024-12-31", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "结束日期不能为空")
        LocalDate endDate,

        @Schema(description = "是否自动续费", example = "false")
        Boolean autoRenew,

        @Schema(description = "备注信息", example = "新企业订阅")
        String notes
) {
    public CreateSubscriptionRequest {
        if (enterpriseId != null) {
            enterpriseId = enterpriseId.trim();
        }
        if (enterpriseCreditCode != null && !enterpriseCreditCode.isBlank()) {
            enterpriseCreditCode = enterpriseCreditCode.trim();
        }
        if (autoRenew == null) {
            autoRenew = false;
        }
    }
}
