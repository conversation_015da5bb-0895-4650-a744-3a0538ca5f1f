package com.czb.hn.dto.common;

import java.util.List;

/**
 * 分页结果DTO
 *
 * @param <T> 分页数据类型
 */
public record PageResult<T>(
    List<T> content,          // 当前页数据
    long totalElements,       // 总记录数
    int totalPages,           // 总页数
    int page,                 // 当前页码（从0开始）
    int size,                 // 每页大小
    boolean first,            // 是否为第一页
    boolean last,             // 是否为最后一页
    boolean empty             // 是否为空
) {
    /**
     * 紧凑的规范构造函数
     */
    public PageResult {
        if (content == null) {
            throw new IllegalArgumentException("内容不能为空");
        }
        if (page < 0) {
            throw new IllegalArgumentException("页码不能小于0");
        }
        if (size <= 0) {
            throw new IllegalArgumentException("每页大小必须大于0");
        }
    }
} 