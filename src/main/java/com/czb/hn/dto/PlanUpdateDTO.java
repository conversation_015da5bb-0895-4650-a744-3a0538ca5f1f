package com.czb.hn.dto;

import com.czb.hn.util.KeywordUtils;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "方案更新DTO（所有字段均为可选，支持部分更新）")
public record PlanUpdateDTO(
        @Schema(description = "方案名称", example = "华能资本舆情监控方案（已更新）", minLength = 1, maxLength = 255) String name,

        @Schema(description = "方案描述", example = "更新后的方案描述，增加了更多监控维度和预警机制", maxLength = 1000) String description,

        @Schema(description = "监控关键词（支持逻辑运算符：+表示AND，|表示OR，@@转义+字符）", example = "华能资本+投资|华能集团+金融", minLength = 1, maxLength = 5000) String monitorKeywords,

        @Schema(description = "排除关键词（使用|分隔，表示OR逻辑）", example = "广告|招聘", maxLength = 2000) String excludeKeywords,

        @Schema(description = "企业ID", example = "enterprise123", minLength = 1, maxLength = 255) String enterpriseId) {
    public PlanUpdateDTO {
        // All fields can be null in update DTO as they might not be updated
        if (name != null && name.isBlank()) {
            throw new IllegalArgumentException("Plan name cannot be blank");
        }

        // 验证监控关键词格式（如果提供，支持逻辑运算符）
        if (monitorKeywords != null && !monitorKeywords.isBlank()) {
            String monitorKeywordsError = KeywordUtils.getMonitorKeywordsValidationErrorMessage(monitorKeywords);
            if (monitorKeywordsError != null) {
                throw new IllegalArgumentException("Monitor keywords validation failed: " + monitorKeywordsError);
            }
        } else if (monitorKeywords != null && monitorKeywords.isBlank()) {
            throw new IllegalArgumentException("Monitor keywords cannot be blank");
        }

        // 验证排除关键词格式（如果提供）
        if (excludeKeywords != null && !excludeKeywords.isBlank()) {
            String excludeKeywordsError = KeywordUtils.getExcludeKeywordsValidationErrorMessage(excludeKeywords);
            if (excludeKeywordsError != null) {
                throw new IllegalArgumentException("Exclude keywords validation failed: " + excludeKeywordsError);
            }
        }
    }
}