package com.czb.hn.dto.workbench;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 信息统计DTO
 * 用于工作台展示舆情信息、敏感信息和预警信息的统计数据
 */
@Schema(description = "信息统计数据")
public record InformationStatsDTO(
        @Schema(description = "统计开始时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

        @Schema(description = "统计结束时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 18:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,

        @Schema(description = "方案ID（为空时表示企业下所有方案）", example = "1") Long planId,

        @Schema(description = "方案名称（为空时表示企业下所有方案）", example = "华能资本舆情监控方案") String planName,

        @Schema(description = "舆情信息总量", example = "1250") Long totalInformationCount,

        @Schema(description = "舆情敏感信息总量", example = "85") Long sensitiveInformationCount,

        @Schema(description = "预警信息总量", example = "12") Long alertCount) {
    public InformationStatsDTO {
        // 验证参数
        if (totalInformationCount != null && totalInformationCount < 0) {
            throw new IllegalArgumentException("Total information count cannot be negative");
        }
        if (sensitiveInformationCount != null && sensitiveInformationCount < 0) {
            throw new IllegalArgumentException("Sensitive information count cannot be negative");
        }
        if (alertCount != null && alertCount < 0) {
            throw new IllegalArgumentException("Alert count cannot be negative");
        }
    }
}
