package com.czb.hn.dto.workbench;

import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.InformationSensitivityType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 预警信息列表项DTO
 * 用于工作台展示预警信息列表
 */
@Schema(description = "预警信息列表项")
public record AlertListItemDTO(
        @Schema(description = "预警ID", example = "1") Long id,

        @Schema(description = "预警标题", example = "重要安全预警") String title,

        @Schema(description = "预警内容", example = "发现重要安全漏洞，请及时关注") String content,

        @Schema(description = "敏感度", example = "1", allowableValues = {
                "1", "2", "3" }) InformationSensitivityType sensitivityType,

        @Schema(description = "敏感度描述", example = "敏感") String sensitivityDescription,

        @Schema(description = "预警时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime warningTime,

        @Schema(description = "来源", example = "微博") String source,

        @Schema(description = "涉及关键词", example = "安全,漏洞,预警") String involvedKeywords,

        @Schema(description = "预警级别", example = "SEVERE", allowableValues = { "GENERAL", "MODERATE",
                "SEVERE" }) AlertResult.WarningLevel warningLevel,

        @Schema(description = "预警级别描述", example = "严重") String warningLevelDescription,

        @Schema(description = "内容类别", example = "1", allowableValues = { "1",
                "2" }) ContentCategory contentCategory,

        @Schema(description = "内容类别描述", example = "原创") String contentCategoryDescription,

        @Schema(description = "相似文章数量", example = "5") Integer similarArticleCount){
    public AlertListItemDTO {
        // 验证参数
        if (title != null && title.length() > 500) {
            throw new IllegalArgumentException("Title length cannot exceed 500 characters");
        }
        if (content != null && content.length() > 2000) {
            throw new IllegalArgumentException("Content length cannot exceed 2000 characters");
        }
        if (similarArticleCount != null && similarArticleCount < 0) {
            throw new IllegalArgumentException("Similar article count cannot be negative");
        }
    }

    /**
     * 从AlertResult实体创建DTO
     */
    public static AlertListItemDTO fromEntity(AlertResult alertResult) {
        if (alertResult == null) {
            return null;
        }

        return new AlertListItemDTO(
                alertResult.getId(),
                alertResult.getTitle(),
                alertResult.getContent(),
                alertResult.getInformationSensitivityType(),
                alertResult.getInformationSensitivityType() != null
                        ? alertResult.getInformationSensitivityType().getDescription()
                        : null,
                alertResult.getWarningTime(),
                alertResult.getSource(),
                alertResult.getInvolvedKeywords(),
                alertResult.getWarningLevel(),
                alertResult.getWarningLevel() != null ? alertResult.getWarningLevel().getDescription() : null,
                alertResult.getContentCategory(),
                alertResult.getContentCategory() != null ? alertResult.getContentCategory().getDescription() : null,
                alertResult.getSimilarArticleCount());
    }
}
