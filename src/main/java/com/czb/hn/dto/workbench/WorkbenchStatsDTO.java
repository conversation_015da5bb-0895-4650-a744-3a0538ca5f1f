package com.czb.hn.dto.workbench;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工作台统计信息DTO
 */
@Schema(description = "工作台统计信息")
public record WorkbenchStatsDTO(
        @Schema(description = "用户上次登录时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime lastLoginTime,

        @Schema(description = "近7天登录次数", example = "5") Long sevenDayLoginCount,

        @Schema(description = "企业账号到期时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-12-31 23:59:59") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime enterpriseExpirationDate,

        @Schema(description = "企业方案数量", example = "3") Long planCount) {
    public WorkbenchStatsDTO {
        // 验证参数
        if (sevenDayLoginCount != null && sevenDayLoginCount < 0) {
            throw new IllegalArgumentException("Seven day login count cannot be negative");
        }
        if (planCount != null && planCount < 0) {
            throw new IllegalArgumentException("Plan count cannot be negative");
        }
    }
}
