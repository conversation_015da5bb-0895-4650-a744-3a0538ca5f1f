package com.czb.hn.dto.user;

public class LoginUserContextHolder {

    private static final ThreadLocal<LoginUserContext> userContextHolder = new ThreadLocal<>();

    public static LoginUserContext getContext() {
        return userContextHolder.get();
    }

    public static String getUserId() {
        LoginUserContext userContext = getContext();
        if (userContext != null && userContext.getUser() != null) {
            return userContext.getUser().getUserId();
        }
        return null;
    }

    public static LoginUser getUser() {
        LoginUserContext userContext = getContext();
        if (userContext != null) {
            return userContext.getUser();
        }
        return null;
    }

    public static void clearContext() {
        userContextHolder.remove();
    }

    public static void setContext(LoginUserContext userContext) {
        if (userContext == null) {
            clearContext();
        } else {
            userContextHolder.set(userContext);
        }
    }

}
