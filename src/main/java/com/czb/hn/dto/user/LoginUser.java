package com.czb.hn.dto.user;


import cn.com.ycfin.onepass.client.model.UserRole;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

public class LoginUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;

    private String name;

    private String phoneNumber;

    private String employeeId;

    private List<UserRole> userRoles;

    private Set<String> groupNames;

    private String primaryGroupName;

    private String primaryGroupId;

    private String primaryGroupCode;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public List<UserRole> getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public Set<String> getGroupNames() {
        return groupNames;
    }

    public void setGroupNames(Set<String> groupNames) {
        this.groupNames = groupNames;
    }

    public String getPrimaryGroupName() {
        return primaryGroupName;
    }

    public void setPrimaryGroupName(String primaryGroupName) {
        this.primaryGroupName = primaryGroupName;
    }

    public String getPrimaryGroupId() {
        return primaryGroupId;
    }

    public void setPrimaryGroupId(String primaryGroupId) {
        this.primaryGroupId = primaryGroupId;
    }

    public String getPrimaryGroupCode() {
        return primaryGroupCode;
    }

    public void setPrimaryGroupCode(String primaryGroupCode) {
        this.primaryGroupCode = primaryGroupCode;
    }
}
