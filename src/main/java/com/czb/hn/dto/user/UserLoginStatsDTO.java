package com.czb.hn.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * User Login Statistics DTO
 * Contains user login statistics information
 */
@Schema(description = "用户登录统计信息")
public record UserLoginStatsDTO(
    
    @Schema(description = "用户ID", example = "user123")
    String userId,
    
    @Schema(description = "用户名称", example = "张三")
    String userName,
    
    @Schema(description = "企业ID", example = "enterprise123")
    String enterpriseId,
    
    @Schema(description = "企业代码", example = "COMP001")
    String enterpriseCode,
    
    @Schema(description = "上次登录时间", example = "2025-07-01 10:30:00")
    LocalDateTime lastLoginTime,
    
    @Schema(description = "7天内登录次数", example = "5")
    long loginCountInSevenDays
) {
}
