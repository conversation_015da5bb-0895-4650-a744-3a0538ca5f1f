package com.czb.hn.dto.user;

import java.io.Serializable;
import java.util.List;

/**
 * 部门/组织信息DTO
 */
public record GroupInfo(
    String groupId,
    String groupName,
    String groupCode,
    String parentId,
    int level,
    List<GroupInfo> children
) implements Serializable {
    
    // Compact canonical constructor for validation
    public GroupInfo {
        if (groupId == null) {
            throw new IllegalArgumentException("Group ID cannot be null");
        }
    }
} 