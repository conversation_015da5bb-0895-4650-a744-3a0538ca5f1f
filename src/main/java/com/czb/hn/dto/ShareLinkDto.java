package com.czb.hn.dto;

import java.time.LocalDateTime;

/**
 * 分享链接DTO
 */
public record ShareLinkDto(
    String shareCode,
    String originUrl,
    LocalDateTime expireTime
) {
    public ShareLinkDto {
        if (shareCode == null || shareCode.isBlank()) throw new IllegalArgumentException("分享码不能为空");
        if (originUrl == null || originUrl.isBlank()) throw new IllegalArgumentException("分享链接不能为空");
        if (expireTime == null) throw new IllegalArgumentException("过期时间不能为空");
    }
}