package com.czb.hn.dto;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;

@Schema(description = "方案信息DTO")
public record PlanDTO(
        @Schema(description = "方案ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED) Long id,

        @Schema(description = "方案名称", example = "华能资本舆情监控方案", requiredMode = Schema.RequiredMode.REQUIRED) String name,

        @Schema(description = "方案描述", example = "针对华能资本的全面舆情监控方案，包括投资动态、市场表现、风险预警等多个维度的监控") String description,

        @Schema(description = "监控关键词（支持逻辑运算符：+表示AND，|表示OR，@@转义+字符）", example = "华能资本+投资|华能集团+金融|新能源+华能", requiredMode = Schema.RequiredMode.REQUIRED) String monitorKeywords,

        @Schema(description = "排除关键词（使用|分隔，表示OR逻辑）", example = "广告|招聘|测试|Demo") String excludeKeywords,

        @Schema(description = "关联企业信息", requiredMode = Schema.RequiredMode.REQUIRED) EnterpriseDTO enterprise,

        @Schema(description = "创建时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00", requiredMode = Schema.RequiredMode.REQUIRED) @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createdAt,

        @Schema(description = "更新时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00", requiredMode = Schema.RequiredMode.REQUIRED) @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updatedAt) {
    public PlanDTO {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Plan name cannot be null or blank");
        }
        if (monitorKeywords == null || monitorKeywords.isBlank()) {
            throw new IllegalArgumentException("Monitor keywords cannot be null or blank");
        }
    }
}