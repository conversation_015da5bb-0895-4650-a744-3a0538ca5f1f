package com.czb.hn.dto.elasticsearch;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.jpa.securadar.entity.SinaNewsDwdEntity;
import com.czb.hn.util.DateTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 新浪舆情Elasticsearch文档DTO
 * 用于转换DWD实体到Elasticsearch文档
 */
@Data
@NoArgsConstructor
public class SinaNewsDocumentDto {

        /**
         * 将DWD实体转换为Elasticsearch文档
         *
         * @param dwdEntity DWD实体
         * @return Elasticsearch文档
         */
        public static SinaNewsDocument fromDwdEntity(SinaNewsDwdEntity dwdEntity) {
                if (dwdEntity == null) {
                        return null;
                }

                SinaNewsDocument document = new SinaNewsDocument();

                // 设置基本字段
                document.setContentId(dwdEntity.getContentId());
                document.setTextId(dwdEntity.getTextId());
                document.setTitle(dwdEntity.getTitle());
                document.setContent(dwdEntity.getContent());
                document.setSummary(dwdEntity.getSummary());
                document.setUrl(dwdEntity.getUrl());
                document.setSource(dwdEntity.getSource());
                document.setSourceWebsite(dwdEntity.getSourceWebsite());
                document.setCaptureWebsite(dwdEntity.getCaptureWebsite());
                document.setMediaType(dwdEntity.getMediaType() != null ? dwdEntity.getMediaType().getValue() : null);
                document.setMediaTypeSecond(
                                dwdEntity.getMediaTypeSecond() != null ? dwdEntity.getMediaTypeSecond().getValue()
                                                : null);
                document.setMediaLevel(dwdEntity.getMediaLevel() != null ? dwdEntity.getMediaLevel().getValue() : null);
                document.setNewsColumn(dwdEntity.getColumn()); // 注意这里使用newsColumn字段

                // 设置时间字段 - 确保时间格式正确
                document.setPublishTime(dwdEntity.getPublishTime());
                document.setCaptureTime(dwdEntity.getCaptureTime());
                document.setProcessTime(dwdEntity.getProcessTime());

                // 设置作者信息
                document.setAuthor(dwdEntity.getAuthor());
                document.setAuthorId(dwdEntity.getAuthorId());
                document.setAuthorGender(dwdEntity.getAuthorGender());
                document.setAuthorProfileUrl(dwdEntity.getAuthorProfileUrl());
                document.setAuthorHomePage(dwdEntity.getAuthorHomePage());
                document.setAuthorProvince(dwdEntity.getAuthorProvince());
                document.setAuthorCity(dwdEntity.getAuthorCity());
                document.setAuthorVerified(dwdEntity.getAuthorVerified());
                document.setAuthorVerifiedType(
                                dwdEntity.getAuthorVerifiedType() != null ? dwdEntity.getAuthorVerifiedType().getValue()
                                                : null);
                document.setAuthorTags(dwdEntity.getAuthorTags());
                document.setAuthorContentCount(dwdEntity.getAuthorContentCount());
                document.setAuthorFollowingCount(dwdEntity.getAuthorFollowingCount());
                document.setAuthorFollowersCount(dwdEntity.getAuthorFollowersCount());

                // 设置地理位置信息
                document.setPublishProvince(dwdEntity.getPublishProvince());
                document.setPublishCity(dwdEntity.getPublishCity());
                document.setContentProvince(dwdEntity.getContentProvince());
                document.setContentCity(dwdEntity.getContentCity());
                document.setLocation(dwdEntity.getLocation());

                // 设置分类信息
                document.setSecondTrades(Arrays.stream(dwdEntity.getSecondTrades().split(",")).toList());

                // 设置情感分析
                document.setEmotion(dwdEntity.getEmotion() != null ? dwdEntity.getEmotion().getValue() : null);
                document.setSentimentScore(dwdEntity.getSentimentScore());
                document.setSensitivityType(
                                dwdEntity.getSensitivityType() != null ? dwdEntity.getSensitivityType().getValue()
                                                : null);

                document.setContentTypes(
                                dwdEntity.getContentTypes() != null
                                                ? Integer.parseInt(dwdEntity.getContentTypes().getValue())
                                                : null);

                // 设置互动数据
                document.setCommentCount(dwdEntity.getCommentCount());
                document.setForwardCount(dwdEntity.getForwardCount());
                document.setPraiseCount(dwdEntity.getPraiseCount());
                document.setShareCount(dwdEntity.getShareCount());
                document.setCollectionCount(dwdEntity.getCollectionCount());
                document.setAnswerCount(dwdEntity.getAnswerCount());
                document.setLookingCount(dwdEntity.getLookingCount());
                document.setInteractionCount(dwdEntity.getInteractionCount());

                // 设置相似性信息
                document.setSimilarityNum(dwdEntity.getSimilarityNum());
                document.setSimilarityTag(dwdEntity.getSimilarityTag());

                // 设置转发信息
                document.setIsOriginal(dwdEntity.getIsOriginal() != null ? dwdEntity.getIsOriginal().getValue() : null);
                document.setIsForward(dwdEntity.getIsForward());
                document.setWbForwardType(dwdEntity.getWbForwardType());
                document.setRootContentId(dwdEntity.getRootContentId());

                // 设置匹配信息
                document.setMatchType(
                                dwdEntity.getMatchType() != null ? Integer.parseInt(dwdEntity.getMatchType().getValue())
                                                : null);
                document.setMatchInfo(dwdEntity.getMatchInfo());
                document.setMatchTicket(dwdEntity.getMatchTicket());
                document.setMatchName(dwdEntity.getMatchName());
                document.setResultView(dwdEntity.getResultView() != null ? dwdEntity.getResultView().getValue() : null);

                // 设置媒体信息
                document.setImages(dwdEntity.getImages());
                document.setVideoUrl(dwdEntity.getVideoUrl());
                document.setVideoCoverUrl(dwdEntity.getVideoCoverUrl());

                // 设置OCR内容
                document.setOcrContent(dwdEntity.getOcrContents());

                // 设置视频内容（合并多个视频文本字段）
                document.setVideoContent(combineVideoContent(dwdEntity));

                // 设置音频内容
                document.setAudioContent(dwdEntity.getVideoAudioText());

                // 设置原始数据关联
                document.setOdsId(dwdEntity.getOdsId());
                document.setDwdId(dwdEntity.getId());

                return document;
        }

        /**
         * 合并多个视频文本字段为一个完整的视频内容
         *
         * @param dwdEntity DWD实体
         * @return 合并后的视频内容文本
         */
        private static String combineVideoContent(SinaNewsDwdEntity dwdEntity) {
                if (dwdEntity == null) {
                        return null;
                }

                StringBuilder videoContent = new StringBuilder();

                // 添加视频字幕文本
                if (dwdEntity.getVideoSubtitleText() != null && !dwdEntity.getVideoSubtitleText().trim().isEmpty()) {
                        videoContent.append(dwdEntity.getVideoSubtitleText().trim());
                }

                // 添加视频背景文本
                if (dwdEntity.getVideoBackgroundText() != null
                                && !dwdEntity.getVideoBackgroundText().trim().isEmpty()) {
                        if (videoContent.length() > 0) {
                                videoContent.append(" ");
                        }
                        videoContent.append(dwdEntity.getVideoBackgroundText().trim());
                }

                // 添加动态字幕文本
                if (dwdEntity.getVideoDynamicSubtitleText() != null
                                && !dwdEntity.getVideoDynamicSubtitleText().trim().isEmpty()) {
                        if (videoContent.length() > 0) {
                                videoContent.append(" ");
                        }
                        videoContent.append(dwdEntity.getVideoDynamicSubtitleText().trim());
                }

                // 添加静态字幕文本
                if (dwdEntity.getVideoStaticSubtitleText() != null
                                && !dwdEntity.getVideoStaticSubtitleText().trim().isEmpty()) {
                        if (videoContent.length() > 0) {
                                videoContent.append(" ");
                        }
                        videoContent.append(dwdEntity.getVideoStaticSubtitleText().trim());
                }

                return videoContent.length() > 0 ? videoContent.toString() : null;
        }
}