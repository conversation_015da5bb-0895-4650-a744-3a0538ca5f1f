-- ==================== 计费系统预制数据SQL ====================
-- 包含测试数据、示例数据和常用查询脚本
-- 执行前请确保已运行 database_billing_core.sql 创建表结构

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==================== 清理现有数据 ====================
-- 清理测试数据（可选，生产环境请谨慎使用）
-- DELETE FROM enterprise_subscriptions WHERE enterprise_id LIKE 'demo_%' OR enterprise_id LIKE 'test_%';

-- ==================== 预制企业订阅数据 ====================

-- 1. 正常活跃订阅企业
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('enterprise_active_001', '91110000000000001X', '2024-01-01', '2024-12-31', 'ACTIVE', FALSE, '正常活跃企业 - 年度订阅'),
('enterprise_active_002', '91110000000000002Y', '2024-06-01', '2025-05-31', 'ACTIVE', TRUE, '正常活跃企业 - 自动续费'),
('enterprise_active_003', '91110000000000003Z', '2024-03-15', '2025-03-14', 'ACTIVE', FALSE, '正常活跃企业 - 长期订阅');

-- 2. 即将过期的订阅企业（7天内）
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('enterprise_expiring_001', '91110000000000004A', '2024-01-01', DATE_ADD(CURDATE(), INTERVAL 3 DAY), 'ACTIVE', FALSE, '即将过期企业 - 3天后到期'),
('enterprise_expiring_002', '91110000000000005B', '2024-01-01', DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'ACTIVE', TRUE, '即将过期企业 - 7天后到期'),
('enterprise_expiring_003', '91110000000000006C', '2024-01-01', DATE_ADD(CURDATE(), INTERVAL 1 DAY), 'ACTIVE', FALSE, '即将过期企业 - 明天到期');

-- 3. 已过期但状态仍为ACTIVE的企业（需要定时任务处理）
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('enterprise_expired_001', '91110000000000007D', '2023-01-01', '2023-12-31', 'ACTIVE', FALSE, '已过期企业 - 需要更新状态'),
('enterprise_expired_002', '91110000000000008E', '2023-06-01', '2024-05-31', 'ACTIVE', FALSE, '已过期企业 - 需要更新状态');

-- 4. 正确标记为过期的企业
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('enterprise_expired_003', '91110000000000009F', '2023-01-01', '2023-12-31', 'EXPIRED', FALSE, '已过期企业 - 状态正确'),
('enterprise_expired_004', '91110000000000010G', '2023-06-01', '2024-05-31', 'EXPIRED', FALSE, '已过期企业 - 状态正确');

-- 5. 暂停和取消的订阅
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('enterprise_suspended_001', '91110000000000011H', '2024-01-01', '2024-12-31', 'SUSPENDED', FALSE, '暂停订阅企业'),
('enterprise_cancelled_001', '91110000000000012I', '2024-01-01', '2024-12-31', 'CANCELLED', FALSE, '取消订阅企业');

-- 6. 测试用企业（不同场景）
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('test_enterprise_001', '91110000000000013J', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'ACTIVE', FALSE, '测试企业 - 30天订阅'),
('test_enterprise_002', '91110000000000014K', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 90 DAY), 'ACTIVE', TRUE, '测试企业 - 90天订阅'),
('test_enterprise_003', '91110000000000015L', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 365 DAY), 'ACTIVE', FALSE, '测试企业 - 年度订阅');

-- 7. 演示用企业
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('demo_enterprise_001', '91110000000000016M', '2024-01-01', '2024-12-31', 'ACTIVE', FALSE, '演示企业 - 基础版'),
('demo_enterprise_002', '91110000000000017N', '2024-01-01', '2025-12-31', 'ACTIVE', TRUE, '演示企业 - 专业版'),
('demo_enterprise_003', '91110000000000018O', '2024-01-01', '2026-12-31', 'ACTIVE', FALSE, '演示企业 - 企业版');

-- ==================== 恢复外键检查 ====================
SET FOREIGN_KEY_CHECKS = 1;

-- ==================== 数据验证查询 ====================
-- 验证插入的数据
SELECT 
    '数据插入验证' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN status = 'EXPIRED' THEN 1 ELSE 0 END) as expired_count,
    SUM(CASE WHEN status = 'SUSPENDED' THEN 1 ELSE 0 END) as suspended_count,
    SUM(CASE WHEN status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelled_count
FROM enterprise_subscriptions 
WHERE enterprise_id LIKE 'enterprise_%' OR enterprise_id LIKE 'test_%' OR enterprise_id LIKE 'demo_%';

-- 验证即将过期的订阅
SELECT 
    '即将过期验证' as check_type,
    enterprise_id,
    end_date,
    DATEDIFF(end_date, CURDATE()) as days_until_expiration,
    status
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY end_date;

-- 验证已过期但状态错误的订阅
SELECT 
    '过期状态验证' as check_type,
    enterprise_id,
    end_date,
    DATEDIFF(CURDATE(), end_date) as days_overdue,
    status
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date < CURDATE()
ORDER BY end_date;

-- ==================== 完成信息 ====================
SELECT 
    '预制数据创建完成' as message,
    COUNT(*) as total_inserted_records,
    MIN(created_at) as first_record_time,
    MAX(created_at) as last_record_time
FROM enterprise_subscriptions 
WHERE enterprise_id LIKE 'enterprise_%' OR enterprise_id LIKE 'test_%' OR enterprise_id LIKE 'demo_%';

-- ==================================================================================
-- 预制数据说明：
-- 1. enterprise_active_*: 正常活跃的企业订阅
-- 2. enterprise_expiring_*: 即将过期的企业订阅（用于测试过期警告）
-- 3. enterprise_expired_*: 已过期的企业订阅（用于测试过期处理）
-- 4. enterprise_suspended_*: 暂停的企业订阅
-- 5. enterprise_cancelled_*: 取消的企业订阅
-- 6. test_enterprise_*: 测试用企业订阅
-- 7. demo_enterprise_*: 演示用企业订阅
-- ==================================================================================
