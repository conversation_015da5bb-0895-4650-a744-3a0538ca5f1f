-- 新浪舆情通令牌缓存表创建脚本
-- 用于持久化存储访问令牌，避免服务重启导致的令牌丢失

-- 创建新浪舆情通令牌缓存表
CREATE TABLE IF NOT EXISTS sina_token_cache (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    app_id VARCHAR(64) NOT NULL COMMENT '应用ID',
    encrypted_access_token TEXT NOT NULL COMMENT '加密后的访问令牌',
    encrypted_refresh_token TEXT COMMENT '加密后的刷新令牌',
    expire_time DATETIME NOT NULL COMMENT '令牌过期时间',
    created_time DATETIME NOT NULL COMMENT '令牌创建时间',
    created_date DATETIME NOT NULL COMMENT '令牌创建日期（用于统计当天获取次数）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为活跃令牌',
    daily_fetch_count INT NOT NULL DEFAULT 1 COMMENT '当天获取令牌次数',
    last_used_time DATETIME COMMENT '最后使用时间',
    remarks VARCHAR(500) COMMENT '备注信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间戳',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新浪舆情通令牌缓存表';

-- 创建索引
CREATE INDEX idx_sina_token_app_id ON sina_token_cache(app_id);
CREATE INDEX idx_sina_token_expire_time ON sina_token_cache(expire_time);
CREATE INDEX idx_sina_token_created_date ON sina_token_cache(created_date);
CREATE INDEX idx_sina_token_is_active ON sina_token_cache(is_active);
CREATE INDEX idx_sina_token_app_active ON sina_token_cache(app_id, is_active, expire_time);

-- 添加表注释
ALTER TABLE sina_token_cache COMMENT = '新浪舆情通令牌缓存表 - 持久化存储访问令牌，支持加密存储和获取次数限制，避免服务重启导致的令牌丢失';

-- 插入示例配置（可选）
-- INSERT INTO sina_token_cache (app_id, encrypted_access_token, expire_time, created_time, created_date, remarks) 
-- VALUES ('example_app_id', 'encrypted_token_example', DATE_ADD(NOW(), INTERVAL 22 HOUR), NOW(), DATE(NOW()), 'Example token cache entry');

-- 查询语句示例
-- 查看所有活跃令牌
-- SELECT app_id, expire_time, created_time, daily_fetch_count, last_used_time 
-- FROM sina_token_cache 
-- WHERE is_active = TRUE 
-- ORDER BY created_time DESC;

-- 查看今日获取次数统计
-- SELECT app_id, COUNT(*) as fetch_count, MAX(created_time) as last_fetch_time
-- FROM sina_token_cache 
-- WHERE DATE(created_date) = CURDATE()
-- GROUP BY app_id;

-- 清理过期令牌（7天前的非活跃令牌）
-- DELETE FROM sina_token_cache 
-- WHERE expire_time < DATE_SUB(NOW(), INTERVAL 7 DAY) 
-- AND is_active = FALSE;
