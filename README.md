# SecuRadar - 舆情监控系统

SecuRadar 是一个专注于舆情监控、分析和预警的系统，可以从多个数据源收集信息，进行清洗分析，并提供可视化展示和预警功能。

## 系统架构

系统采用分层设计：

- **数据采集层(ODS)**: 负责从各个数据源采集原始数据，当前支持新浪舆情通 API
- **数据清洗层(DWD)**: 负责对原始数据进行清洗、转换和标准化
- **数据聚合层(DWS)**: 负责对清洗后的数据进行聚合统计分析
- **数据应用层(ADS)**: 负责生成报表、预警和提供 API 服务

## 主要功能

- 支持从新浪舆情通 API 采集数据
- 本地文件测试模式，方便开发调试
- 自动数据清洗与结构化
- 情感分析和趋势识别
- 多维度数据聚合统计
- 舆情监控和预警
- 报表生成与数据可视化

## 技术栈

- **后端**: Spring Boot 3.x, Java 17
- **数据持久化**: Spring Data JPA, MySQL
- **序列化**: Jackson
- **定时任务**: Spring Task
- **API 通信**: RestTemplate

## 配置与部署

### 环境要求

- JDK 17+
- MySQL 8.0+
- Maven 3.8+

### 配置文件

主要配置参数位于`application.yml`中，包括：

```yaml
sina:
  api:
    appId: ${SINA_APP_ID:your_app_id} # 新浪舆情通应用ID
    appSecret: ${SINA_APP_SECRET:your_app_secret} # 新浪舆情通应用密钥
    default:
      ticket: ${SINA_DEFAULT_TICKET:your_default_ticket} # 默认监控方案标识
```

请确保配置了正确的数据库连接信息和新浪舆情通 API 凭证。

### 构建与运行

```bash
# 构建项目
mvn clean package

# 运行应用
java -jar target/SecuRadar-1.0-SNAPSHOT.jar
```

## 开发指南

### 数据流转过程

1. **采集层**: 通过`SinaNewsCollectorService`定时从 API 拉取数据，存入 ODS 库
2. **清洗层**: 通过`SinaNewsCleanerService`定时处理 ODS 数据，转存入 DWD 库
3. **聚合层**: 通过`SinaNewsAggregatorService`定时聚合 DWD 数据，生成 DWS 层统计结果
4. **应用层**: 基于 DWS 层数据生成报表、预警信息，存入 ADS 库
5. **预警层**: 通过`AlertConfigurationService`管理预警配置，实现智能预警

### 核心模块

#### 预警配置模块 (Alert Configuration Module)

- **功能**: 舆情监控预警配置管理，支持复杂的预警规则设置
- **特性**: JSON 配置存储、自动快照版本控制、性能优化的配置消费
- **文档**: [Alert Configuration Module](docs/alert-configuration-module.md)

#### 关键词处理模块 (Keyword Processing)

- **功能**: 支持 AND/OR 逻辑的关键词解析和验证
- **特性**: 转义字符处理、格式验证、逻辑运算符支持
- **文档**: [Keyword Processing Implementation](docs/keyword-processing-implementation.md)

### 本地开发测试

系统提供了本地文件测试模式，方便开发和调试：

#### 快速启动本地测试

```bash
# Linux/Mac
./scripts/start-local-test.sh

# Windows
scripts\start-local-test.bat
```

#### 测试功能

```bash
# 运行测试脚本
./scripts/test-local-collector.sh
```

#### 测试接口

- **数据获取**: `GET /api/test/collector/fetch?offset=1&limit=5`
- **保存测试**: `POST /api/test/collector/save-to-ods?offset=1&limit=3`
- **最新偏移**: `GET /api/test/collector/latest-offset`

详细说明请参考：[本地测试指南](docs/local-testing-guide.md)

### 添加新数据源

1. 创建对应的采集器接口和实现类
2. 定义 DTOs 用于 API 交互
3. 实现数据清洗和转换逻辑
4. 注册定时任务

### 添加新预警规则

1. 扩展`ContentSettingsDto`或`ThresholdSettingsDto`
2. 更新`AlertConfigurationValidator`验证逻辑
3. 修改`AlertConfigurationConsumerService`匹配逻辑
4. 添加相应的测试用例

## 注意事项

- 敏感配置应通过环境变量注入，避免硬编码
- 密码等敏感信息不应提交到代码仓库
- 在生产环境中应使用线程池管理并发任务
- 本地测试模式仅用于开发环境，生产环境请禁用
