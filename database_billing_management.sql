-- ==================== 计费系统管理操作SQL ====================
-- 包含订阅管理、批量操作、数据维护等管理功能
-- 请在执行前仔细检查，建议先在测试环境验证

-- ==================== 订阅创建模板 ====================

-- 1. 创建新企业订阅（模板）
-- 使用方法：替换参数后执行
/*
INSERT INTO enterprise_subscriptions (
    enterprise_id, 
    enterprise_credit_code, 
    start_date, 
    end_date, 
    status, 
    auto_renew, 
    notes
) VALUES (
    'NEW_ENTERPRISE_ID',           -- 替换为实际企业ID
    'NEW_CREDIT_CODE',             -- 替换为实际信用代码
    CURDATE(),                     -- 开始日期（今天）
    DATE_ADD(CURDATE(), INTERVAL 365 DAY), -- 结束日期（一年后）
    'ACTIVE',                      -- 状态
    FALSE,                         -- 是否自动续费
    '新企业订阅'                    -- 备注
);
*/

-- 2. 批量创建测试订阅（示例）
-- 注意：仅用于测试环境
/*
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, auto_renew, notes) VALUES
('batch_test_001', '91110000000000101A', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'ACTIVE', FALSE, '批量测试订阅1'),
('batch_test_002', '91110000000000102B', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 60 DAY), 'ACTIVE', TRUE, '批量测试订阅2'),
('batch_test_003', '91110000000000103C', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 90 DAY), 'ACTIVE', FALSE, '批量测试订阅3');
*/

-- ==================== 订阅更新操作 ====================

-- 3. 延长订阅期限（模板）
-- 使用方法：替换企业ID和新的结束日期
/*
UPDATE enterprise_subscriptions 
SET 
    end_date = 'NEW_END_DATE',     -- 替换为新的结束日期，格式：YYYY-MM-DD
    status = 'ACTIVE',             -- 确保状态为活跃
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 订阅延期至NEW_END_DATE')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- 4. 批量延长即将过期的订阅（30天）
-- 注意：请谨慎使用，建议先查询确认
/*
UPDATE enterprise_subscriptions 
SET 
    end_date = DATE_ADD(end_date, INTERVAL 30 DAY),
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 批量延期30天')
WHERE status = 'ACTIVE' 
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
AND auto_renew = FALSE;
*/

-- 5. 启用/禁用自动续费
-- 启用自动续费
/*
UPDATE enterprise_subscriptions 
SET 
    auto_renew = TRUE,
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 启用自动续费')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- 禁用自动续费
/*
UPDATE enterprise_subscriptions 
SET 
    auto_renew = FALSE,
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 禁用自动续费')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- ==================== 订阅状态管理 ====================

-- 6. 手动更新过期订阅状态
-- 将已过期但状态仍为ACTIVE的订阅更新为EXPIRED
/*
UPDATE enterprise_subscriptions 
SET 
    status = 'EXPIRED',
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 手动更新为过期状态')
WHERE status = 'ACTIVE' 
AND end_date < CURDATE();
*/

-- 7. 暂停订阅
/*
UPDATE enterprise_subscriptions 
SET 
    status = 'SUSPENDED',
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 订阅暂停')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- 8. 重新激活暂停的订阅
/*
UPDATE enterprise_subscriptions 
SET 
    status = 'ACTIVE',
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 订阅重新激活')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID'
AND status = 'SUSPENDED';
*/

-- 9. 取消订阅
/*
UPDATE enterprise_subscriptions 
SET 
    status = 'CANCELLED',
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 订阅取消')
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- ==================== 批量操作 ====================

-- 10. 批量处理即将过期的自动续费订阅
-- 自动延长自动续费订阅（延长一年）
/*
UPDATE enterprise_subscriptions 
SET 
    end_date = DATE_ADD(end_date, INTERVAL 365 DAY),
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 自动续费延期一年')
WHERE status = 'ACTIVE' 
AND auto_renew = TRUE
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY);
*/

-- 11. 批量清理测试数据
-- 删除测试和演示数据（请谨慎使用）
/*
DELETE FROM enterprise_subscriptions 
WHERE enterprise_id LIKE 'test_%' 
OR enterprise_id LIKE 'demo_%' 
OR enterprise_id LIKE 'batch_test_%';
*/

-- ==================== 数据修复操作 ====================

-- 12. 修复缺失的企业信用代码
-- 为没有信用代码的记录生成临时代码
/*
UPDATE enterprise_subscriptions 
SET 
    enterprise_credit_code = CONCAT('TEMP', LPAD(id, 14, '0'), 'X'),
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 自动生成临时信用代码')
WHERE enterprise_credit_code IS NULL 
OR enterprise_credit_code = '';
*/

-- 13. 修复无效的日期范围
-- 将开始日期晚于结束日期的记录进行修复
/*
UPDATE enterprise_subscriptions 
SET 
    start_date = DATE_SUB(end_date, INTERVAL 365 DAY),
    updated_at = CURRENT_TIMESTAMP,
    notes = CONCAT(IFNULL(notes, ''), '; 修复无效日期范围')
WHERE start_date > end_date;
*/

-- ==================== 数据备份和恢复 ====================

-- 14. 创建订阅数据备份表
/*
CREATE TABLE enterprise_subscriptions_backup AS 
SELECT * FROM enterprise_subscriptions;
*/

-- 15. 从备份恢复特定记录
/*
INSERT INTO enterprise_subscriptions 
SELECT * FROM enterprise_subscriptions_backup 
WHERE enterprise_id = 'TARGET_ENTERPRISE_ID';
*/

-- ==================== 监控和告警查询 ====================

-- 16. 生成过期告警报告
SELECT 
    '过期告警报告' as report_type,
    enterprise_id,
    enterprise_credit_code,
    end_date,
    DATEDIFF(CURDATE(), end_date) as days_overdue,
    auto_renew,
    CASE 
        WHEN DATEDIFF(CURDATE(), end_date) <= 7 THEN '轻微过期'
        WHEN DATEDIFF(CURDATE(), end_date) <= 30 THEN '中度过期'
        ELSE '严重过期'
    END as severity_level
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date < CURDATE()
ORDER BY end_date;

-- 17. 生成续费提醒报告
SELECT 
    '续费提醒报告' as report_type,
    enterprise_id,
    enterprise_credit_code,
    end_date,
    DATEDIFF(end_date, CURDATE()) as days_until_expiry,
    auto_renew,
    CASE 
        WHEN auto_renew = TRUE THEN '自动续费'
        ELSE '需要手动续费'
    END as action_required
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
ORDER BY end_date;

-- ==================== 性能优化操作 ====================

-- 18. 重建索引（如果需要）
/*
ALTER TABLE enterprise_subscriptions DROP INDEX idx_end_date;
ALTER TABLE enterprise_subscriptions ADD INDEX idx_end_date (end_date);

ALTER TABLE enterprise_subscriptions DROP INDEX idx_status;
ALTER TABLE enterprise_subscriptions ADD INDEX idx_status (status);
*/

-- 19. 分析表统计信息
/*
ANALYZE TABLE enterprise_subscriptions;
*/

-- 20. 优化表
/*
OPTIMIZE TABLE enterprise_subscriptions;
*/

-- ==================== 使用说明 ====================
/*
重要提醒：
1. 所有UPDATE和DELETE操作都已注释，使用前请：
   - 仔细检查WHERE条件
   - 先在测试环境验证
   - 备份相关数据
   - 逐步执行，避免批量操作

2. 参数替换说明：
   - TARGET_ENTERPRISE_ID: 目标企业ID
   - NEW_END_DATE: 新的结束日期（YYYY-MM-DD格式）
   - NEW_CREDIT_CODE: 新的企业信用代码

3. 建议的执行顺序：
   - 先执行查询确认影响范围
   - 创建备份
   - 执行操作
   - 验证结果

4. 监控建议：
   - 定期执行监控查询(16-17)
   - 设置自动化告警
   - 记录操作日志
*/
