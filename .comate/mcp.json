{"mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"}}, "f2c-mcp": {"command": "npx", "args": ["-y", "@f2c/mcp"], "env": {"personalToken": ""}}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"]}, "maven": {"command": "npx", "args": ["mcp-maven-deps"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "excel": {"command": "uvx", "args": ["excel-mcp-server", "stdio"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IdeaProjects"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/path/to/custom/memory.json"}}}}