-- ==================== 数据库架构验证脚本 ====================
-- 用于验证 database_schema_unified.sql 创建的架构完整性
-- 运行此脚本来检查所有表、索引、视图和存储过程是否正确创建

-- 检查所有表是否存在
SELECT 'Tables Check' as check_type, 
       table_name, 
       table_comment,
       CASE WHEN table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN (
    'plans',
    'alert_configurations', 
    'alert_configuration_snapshots',
    'alert_results',
    'alert_processing_logs',
    'sina_news_ods',
    'sina_news_dwd', 
    'sina_news_dws',
    'sina_news_ads',
    'search_performance_metrics',
    'index_usage_stats'
)
ORDER BY table_name;

-- 检查关键索引是否存在
SELECT 'Indexes Check' as check_type,
       table_name,
       index_name,
       CASE WHEN index_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND index_name IN (
    'ft_idx_title_content',
    'idx_enterprise_warning_time',
    'idx_plan_warning_time', 
    'idx_configuration_time',
    'idx_warning_level',
    'idx_information_sensitivity_type',
    'idx_content_category',
    'idx_enterprise_id',
    'idx_plan_id'
)
ORDER BY table_name, index_name;

-- 检查全文索引
SELECT 'Fulltext Indexes Check' as check_type,
       table_name,
       index_name,
       column_name,
       'EXISTS' as status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND index_type = 'FULLTEXT'
ORDER BY table_name, index_name;

-- 检查视图是否存在
SELECT 'Views Check' as check_type,
       table_name as view_name,
       CASE WHEN table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.views 
WHERE table_schema = DATABASE() 
AND table_name IN (
    'v_active_alert_configurations',
    'v_enabled_alert_configurations', 
    'v_latest_snapshots',
    'v_active_alert_results',
    'v_alert_statistics',
    'v_recent_alerts_optimized',
    'v_alert_stats_optimized'
)
ORDER BY table_name;

-- 检查存储过程是否存在
SELECT 'Procedures Check' as check_type,
       routine_name as procedure_name,
       routine_type,
       CASE WHEN routine_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.routines 
WHERE routine_schema = DATABASE() 
AND routine_name IN (
    'AnalyzeSlowQueries',
    'CleanupOldData',
    'GenerateAlertSummary'
)
ORDER BY routine_name;

-- 检查外键约束
SELECT 'Foreign Keys Check' as check_type,
       constraint_name,
       table_name,
       referenced_table_name,
       'EXISTS' as status
FROM information_schema.key_column_usage 
WHERE constraint_schema = DATABASE() 
AND referenced_table_name IS NOT NULL
ORDER BY table_name, constraint_name;

-- 检查枚举字段
SELECT 'Enum Fields Check' as check_type,
       table_name,
       column_name,
       column_type,
       'EXISTS' as status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND data_type = 'enum'
ORDER BY table_name, column_name;

-- 检查JSON字段
SELECT 'JSON Fields Check' as check_type,
       table_name,
       column_name,
       column_comment,
       'EXISTS' as status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND data_type = 'json'
ORDER BY table_name, column_name;

-- 统计信息汇总
SELECT 'Summary' as check_type,
       'Tables' as object_type,
       COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_type = 'BASE TABLE'

UNION ALL

SELECT 'Summary' as check_type,
       'Views' as object_type,
       COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = DATABASE()

UNION ALL

SELECT 'Summary' as check_type,
       'Procedures' as object_type,
       COUNT(*) as count
FROM information_schema.routines 
WHERE routine_schema = DATABASE() 
AND routine_type = 'PROCEDURE'

UNION ALL

SELECT 'Summary' as check_type,
       'Indexes' as object_type,
       COUNT(DISTINCT index_name) as count
FROM information_schema.statistics 
WHERE table_schema = DATABASE()

UNION ALL

SELECT 'Summary' as check_type,
       'Foreign Keys' as object_type,
       COUNT(*) as count
FROM information_schema.key_column_usage 
WHERE constraint_schema = DATABASE() 
AND referenced_table_name IS NOT NULL;

-- 检查特定的全文索引是否存在
SELECT 'Specific Fulltext Check' as check_type,
       CASE 
           WHEN EXISTS (
               SELECT 1 FROM information_schema.statistics 
               WHERE table_schema = DATABASE() 
               AND table_name = 'alert_results' 
               AND index_name = 'ft_idx_title_content'
               AND index_type = 'FULLTEXT'
           ) THEN 'ft_idx_title_content EXISTS on alert_results'
           ELSE 'ft_idx_title_content MISSING on alert_results'
       END as status;

-- 验证数据完整性约束
SELECT 'Data Integrity Check' as check_type,
       table_name,
       constraint_name,
       constraint_type,
       'EXISTS' as status
FROM information_schema.table_constraints 
WHERE constraint_schema = DATABASE() 
AND constraint_type IN ('PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE')
ORDER BY table_name, constraint_type, constraint_name;
