-- ==================== 核心计费功能数据库表 ====================
-- 创建企业订阅管理表，实现最基础的计费功能

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==================== 企业订阅表 ====================
CREATE TABLE enterprise_subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    enterprise_credit_code VARCHAR(18) COMMENT '企业统一社会信用代码',
    start_date DATE NOT NULL COMMENT '订阅开始日期',
    end_date DATE NOT NULL COMMENT '订阅结束日期',
    status ENUM('ACTIVE', 'EXPIRED', 'SUSPENDED', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE' COMMENT '订阅状态',
    auto_renew BOOLEAN DEFAULT FALSE COMMENT '是否自动续费',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_enterprise_id (enterprise_id) COMMENT '企业ID唯一索引',
    UNIQUE KEY uk_enterprise_credit_code (enterprise_credit_code) COMMENT '企业信用代码唯一索引',
    INDEX idx_end_date (end_date) COMMENT '到期日期索引',
    INDEX idx_status (status) COMMENT '状态索引',
    INDEX idx_status_end_date (status, end_date) COMMENT '状态和到期日期复合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='企业订阅表 - 管理企业的订阅状态和到期时间';

-- ==================== 初始化数据 ====================
-- 插入一些测试数据（可选）
INSERT INTO enterprise_subscriptions (enterprise_id, enterprise_credit_code, start_date, end_date, status, notes) VALUES
('enterprise_demo_001', '91110000000000001X', '2024-01-01', '2024-12-31', 'ACTIVE', '演示企业 - 正常订阅'),
('enterprise_demo_002', '91110000000000002Y', '2024-01-01', '2024-06-30', 'EXPIRED', '演示企业 - 已过期'),
('enterprise_demo_003', '91110000000000003Z', '2024-06-01', '2025-05-31', 'ACTIVE', '演示企业 - 长期订阅');

-- ==================== 视图定义 ====================
-- 创建活跃订阅视图（仅数据展示，不包含业务逻辑）
CREATE VIEW active_subscriptions AS
SELECT
    es.id,
    es.enterprise_id,
    es.enterprise_credit_code,
    es.start_date,
    es.end_date,
    es.status,
    es.auto_renew,
    es.notes,
    es.created_at,
    es.updated_at
FROM enterprise_subscriptions es
WHERE es.status = 'ACTIVE';

-- ==================== 存储过程 ====================
-- 简单的数据清理存储过程（不包含业务逻辑）
DELIMITER //
CREATE PROCEDURE CleanupOldSubscriptions()
BEGIN
    -- 清理超过1年的已取消订阅记录（数据维护用途）
    DELETE FROM enterprise_subscriptions
    WHERE status = 'CANCELLED'
    AND updated_at < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);

    -- 返回清理的记录数
    SELECT ROW_COUNT() AS cleaned_count;
END //
DELIMITER ;

-- ==================== 定时任务说明 ====================
-- 订阅过期检查和状态更新逻辑已移至Java应用层实现
-- 可通过Spring的@Scheduled注解或定时任务框架来处理
-- 避免在数据库层实现业务逻辑

-- ==================== 权限设置 ====================
-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ==================== 完成信息 ====================
-- 核心计费功能数据库表创建完成
-- 包含的功能:
-- 1. 企业订阅表 (enterprise_subscriptions) - 支持企业ID和信用代码双重标识
-- 2. 活跃订阅视图 (active_subscriptions) - 纯数据展示，无业务逻辑
-- 3. 数据清理存储过程 (CleanupOldSubscriptions) - 仅用于数据维护
-- 4. 测试数据 - 包含示例企业订阅记录
--
-- 业务逻辑说明:
-- - 订阅过期检查逻辑已移至Java应用层实现 (SubscriptionExpirationService)
-- - 支持配置化的定时任务执行 (通过application.yml配置)
-- - 数据库层仅负责数据存储，不包含业务逻辑
-- ==================================================================================
