-- ==================== SecuRadar 统一数据库架构文件 ====================
-- 项目: SecuRadar - 舆情监控预警系统
-- 版本: 1.0
-- 创建日期: 2025-06-25
-- 描述: 整合所有数据库表、索引、视图和存储过程的统一架构文件
-- ==================================================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==================== 核心业务表 ====================

-- 1. 方案表 (Plans)
CREATE TABLE plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '方案名称',
    description TEXT COMMENT '方案描述',
    monitor_keywords TEXT NOT NULL COMMENT '监控关键词',
    exclude_keywords TEXT COMMENT '排除关键词',
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_enterprise_id (enterprise_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='方案管理表';

-- 2. 预警配置表 (Alert Configurations)
CREATE TABLE alert_configurations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '配置名称',
    description TEXT COMMENT '配置描述',
    plan_id BIGINT COMMENT '关联方案ID',
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    
    -- JSON配置字段
    alert_keywords JSON COMMENT '预警关键词配置',
    content_settings JSON COMMENT '内容过滤设置',
    threshold_settings JSON COMMENT '预警阈值条件',
    level_settings JSON COMMENT '预警级别分类设置',
    reception_settings JSON COMMENT '预警接收和通知设置',
    
    -- 审计字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(255) COMMENT '创建者',
    updated_by VARCHAR(255) COMMENT '更新者',

    -- 版本控制字段
    current_version INT NOT NULL DEFAULT 1 COMMENT '当前版本号',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    last_snapshot_at TIMESTAMP(0) COMMENT '最后快照时间',
    
    -- 索引
    INDEX idx_plan_id (plan_id),
    INDEX idx_enterprise_id (enterprise_id),
    INDEX idx_enabled (enabled),
    INDEX idx_created_at (created_at),
    INDEX idx_name (name),
    INDEX idx_enterprise_enabled_active (enterprise_id, enabled, is_active),
    
    -- 外键约束
    CONSTRAINT fk_alert_config_plan 
        FOREIGN KEY (plan_id) REFERENCES plans(id) 
        ON DELETE SET NULL ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='预警配置主表 - 存储基于JSON的设置和版本控制';

-- 3. 预警配置快照表 (Alert Configuration Snapshots)
CREATE TABLE alert_configuration_snapshots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    configuration_id BIGINT NOT NULL COMMENT '配置ID引用',
    version_number INT NOT NULL COMMENT '版本号',
    
    -- 快照数据
    snapshot_data JSON NOT NULL COMMENT '完整配置快照JSON',
    
    -- 审计字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '快照创建时间',
    created_by VARCHAR(255) COMMENT '创建者',
    change_reason VARCHAR(500) COMMENT '创建快照的原因',
    
    -- 快照元数据
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为活跃快照',
    operation_type VARCHAR(50) COMMENT '操作类型: CREATE, UPDATE, ROLLBACK, DELETE, MANUAL',
    data_size BIGINT COMMENT '快照数据大小(字节)',
    checksum VARCHAR(64) COMMENT 'SHA-256校验和',
    
    -- 索引
    INDEX idx_config_id (configuration_id),
    INDEX idx_version (configuration_id, version_number),
    INDEX idx_active (configuration_id, is_active),
    INDEX idx_created_at (created_at),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_by (created_by),
    INDEX idx_data_size (data_size),
    INDEX idx_active_version (configuration_id, is_active, version_number DESC),
    INDEX idx_cleanup (configuration_id, version_number),
    
    -- 唯一约束
    UNIQUE KEY uk_config_version (configuration_id, version_number),
    
    -- 外键约束
    CONSTRAINT fk_snapshot_configuration 
        FOREIGN KEY (configuration_id) REFERENCES alert_configurations(id) 
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='预警配置快照表 - 版本历史和审计跟踪';

-- 4. 预警结果表 (Alert Results) - 核心表
CREATE TABLE alert_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 租户隔离字段
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    plan_id BIGINT COMMENT '方案ID',
    configuration_id BIGINT NOT NULL COMMENT '预警配置ID',
    
    -- 预警核心数据
    title TEXT NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '正文',
    involved_keywords JSON NOT NULL COMMENT '涉及关键词 [{"keyword":"关键词","count":3,"positions":[1,5,10]}]',
    
    -- 使用枚举字段替代布尔字段 (新架构)
    information_sensitivity_type ENUM('ALL', 'SENSITIVE', 'NEUTRAL', 'NON_SENSITIVE') NOT NULL DEFAULT 'NEUTRAL' COMMENT '信息敏感性类型',
    content_category ENUM('ALL', 'ORIGINAL', 'FORWARD') NOT NULL DEFAULT 'ORIGINAL' COMMENT '内容类别',
    
    warning_level ENUM('GENERAL', 'MODERATE', 'SEVERE') NOT NULL COMMENT '预警分级 (一般, 中等, 严重)',
    source VARCHAR(255) NOT NULL COMMENT '来源 (实际内容来源，如：微博、微信、新闻网站等)',
    warning_time TIMESTAMP(0) NOT NULL COMMENT '预警时间 (精确到秒)',
    similar_article_count INT DEFAULT 0 COMMENT '相似文章数',
    
    -- 数据源引用 (全部来自Elasticsearch)
    original_content_id VARCHAR(64) NOT NULL COMMENT '原始内容ID (来自Elasticsearch)',
    
    -- 灵活扩展
    extended_attributes JSON COMMENT '扩展属性 {"custom_field1":"value1","metrics":{"score":0.85}}',
    
    -- 处理元数据
    processing_version VARCHAR(20) DEFAULT '1.0' COMMENT '处理版本',
    rule_snapshot JSON COMMENT '触发规则快照',
    
    -- 审计字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(255) COMMENT '创建者',

    -- 性能索引
    INDEX idx_enterprise_warning_time (enterprise_id, warning_time DESC),
    INDEX idx_plan_warning_time (plan_id, warning_time DESC),
    INDEX idx_configuration_time (configuration_id, warning_time DESC),
    INDEX idx_warning_level (warning_level),
    INDEX idx_information_sensitivity_type (information_sensitivity_type),
    INDEX idx_content_category (content_category),
    INDEX idx_source (source),
    INDEX idx_original_content (original_content_id),

    -- 复合索引用于常见查询
    INDEX idx_enterprise_sensitive_level (enterprise_id, information_sensitivity_type, warning_level),
    INDEX idx_plan_category_time (plan_id, content_category, warning_time DESC),
    INDEX idx_enterprise_source_time (enterprise_id, source, warning_time DESC),

    -- 去重检查索引
    INDEX idx_duplicate_check (enterprise_id, original_content_id, configuration_id),
    INDEX idx_time_range (enterprise_id, warning_time, warning_level),
    INDEX idx_source_filter (enterprise_id, source, warning_time DESC),

    -- 覆盖索引用于企业搜索
    INDEX idx_enterprise_covering (enterprise_id, warning_time DESC, warning_level, information_sensitivity_type, content_category),

    -- 外键约束
    CONSTRAINT fk_alert_result_configuration
        FOREIGN KEY (configuration_id) REFERENCES alert_configurations(id)
        ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_alert_result_plan
        FOREIGN KEY (plan_id) REFERENCES plans(id)
        ON DELETE SET NULL ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='预警结果主表 - 存储基于Elasticsearch数据源的预警处理结果，支持多租户隔离';

-- 添加全文搜索索引 (中文文本支持)
ALTER TABLE alert_results
ADD FULLTEXT INDEX ft_idx_title_content (title, content) WITH PARSER ngram;

-- 5. 预警处理日志表 (Alert Processing Logs)
CREATE TABLE alert_processing_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    enterprise_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    plan_id BIGINT COMMENT '方案ID',
    configuration_id BIGINT COMMENT '配置ID',
    processing_start_time TIMESTAMP(0) NOT NULL COMMENT '处理开始时间',
    processing_end_time TIMESTAMP(0) COMMENT '处理结束时间',
    processed_records_count INT DEFAULT 0 COMMENT '处理记录数',
    generated_alerts_count INT DEFAULT 0 COMMENT '生成预警数',
    error_count INT DEFAULT 0 COMMENT '错误数量',
    status ENUM('RUNNING', 'COMPLETED', 'FAILED') DEFAULT 'RUNNING' COMMENT '处理状态',
    error_details TEXT COMMENT '错误详情',
    processing_duration_ms BIGINT COMMENT '处理耗时（毫秒）',
    batch_id VARCHAR(64) COMMENT '批次ID',

    -- 处理统计
    elasticsearch_query_time_ms BIGINT COMMENT 'ES查询耗时',
    rule_evaluation_time_ms BIGINT COMMENT '规则评估耗时',
    database_write_time_ms BIGINT COMMENT '数据库写入耗时',

    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引
    INDEX idx_enterprise_id (enterprise_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_configuration_id (configuration_id),
    INDEX idx_processing_start_time (processing_start_time),
    INDEX idx_status (status),
    INDEX idx_batch_id (batch_id),
    INDEX idx_enterprise_time (enterprise_id, processing_start_time DESC),
    INDEX idx_performance (processing_duration_ms, generated_alerts_count)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='预警处理日志表 - 记录预警处理过程的监控和审计信息';

-- ==================== 数据仓库表 (Data Warehouse) ====================

-- 6. 新浪舆情ODS表 (Operational Data Store)
CREATE TABLE sina_news_ods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_id VARCHAR(64) NOT NULL UNIQUE COMMENT '内容ID',
    text_id VARCHAR(64) COMMENT '文本ID',
    title TEXT COMMENT '标题',
    content TEXT COMMENT '内容',
    summary TEXT COMMENT '摘要',
    url VARCHAR(2048) COMMENT '链接',
    source VARCHAR(255) COMMENT '来源',
    source_website VARCHAR(255) COMMENT '来源网站',
    capture_website VARCHAR(255) COMMENT '采集网站',
    media_type VARCHAR(64) COMMENT '媒体类型',
    media_type_second VARCHAR(64) COMMENT '二级媒体类型',
    media_level VARCHAR(64) COMMENT '媒体级别',
    news_column VARCHAR(255) COMMENT '栏目',
    publish_time TIMESTAMP(0) COMMENT '发布时间',
    capture_time TIMESTAMP(0) COMMENT '采集时间',
    process_time TIMESTAMP(0) COMMENT '处理时间',

    -- 作者信息
    author VARCHAR(255) COMMENT '作者名称',
    author_id VARCHAR(64) COMMENT '作者ID',
    author_gender VARCHAR(16) COMMENT '作者性别',
    author_profile_url VARCHAR(2048) COMMENT '作者资料链接',
    author_home_page VARCHAR(2048) COMMENT '作者主页',
    author_province VARCHAR(64) COMMENT '作者省份',
    author_city VARCHAR(64) COMMENT '作者城市',
    author_verified BOOLEAN COMMENT '作者是否认证',
    author_verified_type VARCHAR(64) COMMENT '认证类型',

    -- 统计信息
    read_count BIGINT DEFAULT 0 COMMENT '阅读数',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',
    comment_count BIGINT DEFAULT 0 COMMENT '评论数',
    share_count BIGINT DEFAULT 0 COMMENT '分享数',

    -- 处理状态
    processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    origin_type VARCHAR(64) COMMENT '原始类型',

    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引
    INDEX idx_content_id (content_id),
    INDEX idx_text_id (text_id),
    INDEX idx_publish_time (publish_time),
    INDEX idx_capture_time (capture_time),
    INDEX idx_origin_type (origin_type),
    INDEX idx_processed (processed),
    INDEX idx_media_type (media_type),
    INDEX idx_author (author),
    INDEX idx_source (source)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='新浪舆情ODS表 - 存储从新浪舆情通接口获取的原始数据';

-- 7. 新浪舆情DWD表 (Data Warehouse Detail)
CREATE TABLE sina_news_dwd (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_id VARCHAR(64) NOT NULL UNIQUE COMMENT '内容ID',
    text_id VARCHAR(64) COMMENT '文本ID',
    title TEXT COMMENT '标题',
    content TEXT COMMENT '内容',
    summary TEXT COMMENT '摘要',
    url VARCHAR(2048) COMMENT '链接',
    source VARCHAR(255) COMMENT '来源',
    source_website VARCHAR(64) COMMENT '来源网站',
    capture_website VARCHAR(64) COMMENT '采集网站',
    media_type VARCHAR(64) COMMENT '媒体类型',
    media_type_second VARCHAR(64) COMMENT '二级媒体类型',
    media_level VARCHAR(64) COMMENT '媒体级别',
    news_column VARCHAR(255) COMMENT '栏目',
    publish_time TIMESTAMP(0) COMMENT '发布时间',
    capture_time TIMESTAMP(0) COMMENT '采集时间',
    process_time TIMESTAMP(0) COMMENT '处理时间',

    -- 作者信息
    author VARCHAR(255) COMMENT '作者名称',
    author_id VARCHAR(64) COMMENT '作者ID',
    author_gender VARCHAR(16) COMMENT '作者性别',
    author_profile_url VARCHAR(2048) COMMENT '作者资料链接',
    author_home_page VARCHAR(2048) COMMENT '作者主页',
    author_province VARCHAR(64) COMMENT '作者省份',
    author_city VARCHAR(64) COMMENT '作者城市',
    author_verified BOOLEAN COMMENT '作者是否认证',
    author_verified_type VARCHAR(64) COMMENT '认证类型',

    -- 统计信息
    read_count BIGINT DEFAULT 0 COMMENT '阅读数',
    like_count BIGINT DEFAULT 0 COMMENT '点赞数',
    comment_count BIGINT DEFAULT 0 COMMENT '评论数',
    share_count BIGINT DEFAULT 0 COMMENT '分享数',

    -- 清洗后的分析字段
    category VARCHAR(64) COMMENT '分类',
    emotion VARCHAR(32) COMMENT '情感倾向',
    keywords TEXT COMMENT '关键词',

    -- 处理状态
    processed_to_dws BOOLEAN DEFAULT FALSE COMMENT '是否已处理到DWS',

    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引
    INDEX idx_content_id (content_id),
    INDEX idx_text_id (text_id),
    INDEX idx_publish_time (publish_time),
    INDEX idx_media_type (media_type),
    INDEX idx_category (category),
    INDEX idx_emotion (emotion),
    INDEX idx_processed (processed_to_dws),
    INDEX idx_author (author),
    INDEX idx_source (source)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='新浪舆情DWD表 - 存储清洗后的数据，用于数据分析和聚合统计';

-- 8. 新浪舆情DWS表 (Data Warehouse Summary)
CREATE TABLE sina_news_dws (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    statistics_date DATE NOT NULL COMMENT '统计日期',
    category VARCHAR(64) COMMENT '分类',
    emotion VARCHAR(32) COMMENT '情感倾向',
    media_type VARCHAR(64) COMMENT '媒体类型',

    -- 统计指标
    total_count BIGINT DEFAULT 0 COMMENT '总数量',
    positive_count BIGINT DEFAULT 0 COMMENT '正面数量',
    negative_count BIGINT DEFAULT 0 COMMENT '负面数量',
    neutral_count BIGINT DEFAULT 0 COMMENT '中性数量',
    total_read_count BIGINT DEFAULT 0 COMMENT '总阅读数',
    total_like_count BIGINT DEFAULT 0 COMMENT '总点赞数',
    total_comment_count BIGINT DEFAULT 0 COMMENT '总评论数',
    total_share_count BIGINT DEFAULT 0 COMMENT '总分享数',
    avg_read_count DECIMAL(10,2) DEFAULT 0 COMMENT '平均阅读数',
    avg_like_count DECIMAL(10,2) DEFAULT 0 COMMENT '平均点赞数',
    avg_comment_count DECIMAL(10,2) DEFAULT 0 COMMENT '平均评论数',
    avg_share_count DECIMAL(10,2) DEFAULT 0 COMMENT '平均分享数',

    -- 处理状态
    processed_to_ads BOOLEAN DEFAULT FALSE COMMENT '是否已处理到ADS',

    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_dws_date (statistics_date),
    INDEX idx_dws_category (category),
    INDEX idx_dws_emotion (emotion),
    INDEX idx_dws_media_type (media_type),
    INDEX idx_dws_processed (processed_to_ads),
    INDEX idx_date_category (statistics_date, category),
    INDEX idx_date_emotion (statistics_date, emotion),

    -- 唯一约束
    UNIQUE KEY uk_date_category_emotion_media (statistics_date, category, emotion, media_type)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='新浪舆情DWS表 - 存储聚合统计的数据，用于分析和展示舆情趋势';

-- 9. 新浪舆情ADS表 (Application Data Store)
CREATE TABLE sina_news_ads (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_date DATE NOT NULL COMMENT '报告日期',
    report_type VARCHAR(50) NOT NULL COMMENT '报告类型 (Daily, Weekly, Monthly, Alert)',
    report_title VARCHAR(500) NOT NULL COMMENT '报告标题',
    report_summary TEXT COMMENT '报告摘要',
    key_insights TEXT COMMENT '关键洞察',
    trend_analysis TEXT COMMENT '趋势分析',
    risk_assessment TEXT COMMENT '风险评估',
    recommendations TEXT COMMENT '建议措施',

    -- 统计数据
    total_articles_count BIGINT DEFAULT 0 COMMENT '文章总数',
    positive_sentiment_ratio DECIMAL(5,2) DEFAULT 0 COMMENT '正面情感比例',
    negative_sentiment_ratio DECIMAL(5,2) DEFAULT 0 COMMENT '负面情感比例',
    neutral_sentiment_ratio DECIMAL(5,2) DEFAULT 0 COMMENT '中性情感比例',

    -- 预警相关
    alert_level INT COMMENT '预警级别 (1-5)',
    alert_keywords TEXT COMMENT '预警关键词',

    -- 报告状态
    published BOOLEAN DEFAULT FALSE COMMENT '是否已发布',

    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_ads_date (report_date),
    INDEX idx_ads_type (report_type),
    INDEX idx_ads_alert_level (alert_level),
    INDEX idx_ads_published (published),
    INDEX idx_date_type (report_date, report_type)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='新浪舆情ADS表 - 存储面向应用的数据';



-- ==================== 高级索引优化 ====================

-- JSON函数索引 (MySQL 8.0+)
-- 这些索引在MySQL 8.0以下版本会被忽略
ALTER TABLE alert_configurations
ADD INDEX idx_alert_keywords_json ((CAST(alert_keywords->'$.keywords' AS CHAR(255) ARRAY)));

ALTER TABLE alert_configurations
ADD INDEX idx_content_source_types ((CAST(content_settings->'$.sourceTypes' AS CHAR(100) ARRAY)));

ALTER TABLE alert_configurations
ADD INDEX idx_content_sensitivity ((CAST(content_settings->'$.sensitivityType' AS CHAR(50))));

-- 预警结果表的JSON索引
ALTER TABLE alert_results
ADD INDEX idx_involved_keywords_json ((CAST(involved_keywords AS CHAR(500))));

ALTER TABLE alert_results
ADD INDEX idx_extended_attrs_keys ((JSON_KEYS(extended_attributes)));

-- 时间分区友好索引
CREATE INDEX idx_alert_results_monthly ON alert_results
(YEAR(warning_time), MONTH(warning_time), enterprise_id, warning_time DESC);

CREATE INDEX idx_alert_results_daily_stats ON alert_results
(enterprise_id, DATE(warning_time), warning_level, source);

CREATE INDEX idx_alert_results_weekly ON alert_results
(enterprise_id, YEARWEEK(warning_time), warning_level);

-- 全文搜索优化索引
ALTER TABLE sina_news_ods
ADD FULLTEXT INDEX ft_ods_title_content (title, content) WITH PARSER ngram;

ALTER TABLE sina_news_dwd
ADD FULLTEXT INDEX ft_dwd_title_content (title, content) WITH PARSER ngram;

-- ==================== 视图定义 ====================

-- 活跃预警配置视图
CREATE VIEW v_active_alert_configurations AS
SELECT
    id, name, description, plan_id, enterprise_id, enabled,
    alert_keywords, content_settings, threshold_settings, level_settings, reception_settings,
    created_at, updated_at, created_by, updated_by, current_version, last_snapshot_at
FROM alert_configurations
WHERE is_active = TRUE;

-- 启用的预警配置视图
CREATE VIEW v_enabled_alert_configurations AS
SELECT
    id, name, description, plan_id, enterprise_id,
    alert_keywords, content_settings, threshold_settings, level_settings, reception_settings,
    created_at, updated_at, current_version
FROM alert_configurations
WHERE is_active = TRUE AND enabled = TRUE;

-- 最新快照视图
CREATE VIEW v_latest_snapshots AS
SELECT
    s.id, s.configuration_id, s.version_number, s.created_at, s.created_by,
    s.change_reason, s.operation_type, s.data_size,
    c.name as configuration_name, c.enterprise_id
FROM alert_configuration_snapshots s
JOIN alert_configurations c ON s.configuration_id = c.id
WHERE s.is_active = TRUE;

-- 活跃预警结果视图
CREATE VIEW v_active_alert_results AS
SELECT
    ar.id, ar.enterprise_id, ar.plan_id, ar.configuration_id,
    ar.title, ar.content, ar.involved_keywords,
    ar.information_sensitivity_type, ar.content_category,
    ar.warning_level, ar.source, ar.warning_time,
    ar.similar_article_count, ar.original_content_id,
    ar.extended_attributes, ar.created_at,
    ac.name as configuration_name, ac.enabled as configuration_enabled
FROM alert_results ar
JOIN alert_configurations ac ON ar.configuration_id = ac.id
WHERE ac.is_active = TRUE AND ac.enabled = TRUE;


-- ==================== 表注释更新 ====================

-- 更新表注释以提供更详细的说明
ALTER TABLE plans COMMENT = '方案管理表 - 存储舆情监控方案的基本信息和关键词配置';
ALTER TABLE alert_configurations COMMENT = '预警配置主表 - 存储基于JSON的设置和版本控制，支持快照管理';
ALTER TABLE alert_configuration_snapshots COMMENT = '预警配置快照表 - 版本历史和审计跟踪，支持配置回滚';
ALTER TABLE alert_results COMMENT = '预警结果主表 - 存储基于Elasticsearch数据源的预警处理结果，支持多租户隔离';
ALTER TABLE alert_processing_logs COMMENT = '预警处理日志表 - 记录预警处理过程的监控和审计信息';
ALTER TABLE sina_news_ods COMMENT = '新浪舆情ODS表 - 存储从新浪舆情通接口获取的原始数据';
ALTER TABLE sina_news_dwd COMMENT = '新浪舆情DWD表 - 存储清洗后的数据，用于数据分析和聚合统计';
ALTER TABLE sina_news_dws COMMENT = '新浪舆情DWS表 - 存储聚合统计的数据，用于分析和展示舆情趋势';
ALTER TABLE sina_news_ads COMMENT = '新浪舆情ADS表 - 存储面向应用的数据，包括报告和预警信息';
ALTER TABLE search_performance_metrics COMMENT = '搜索性能监控表 - 记录查询性能指标用于系统优化';
ALTER TABLE index_usage_stats COMMENT = '索引使用统计表 - 跟踪索引使用情况以优化数据库性能';

-- ==================== 性能优化设置 ====================

-- 分析所有表以优化查询计划
ANALYZE TABLE plans;
ANALYZE TABLE alert_configurations;
ANALYZE TABLE alert_configuration_snapshots;
ANALYZE TABLE alert_results;
ANALYZE TABLE alert_processing_logs;
ANALYZE TABLE sina_news_ods;
ANALYZE TABLE sina_news_dwd;
ANALYZE TABLE sina_news_dws;
ANALYZE TABLE sina_news_ads;
ANALYZE TABLE search_performance_metrics;
ANALYZE TABLE index_usage_stats;

-- ==================== 数据迁移脚本 (可选) ====================

-- 如果需要从旧的布尔字段迁移到新的枚举字段，取消注释以下代码：
/*
-- 迁移 is_sensitive 到 information_sensitivity_type
UPDATE alert_results
SET information_sensitivity_type = CASE
    WHEN is_sensitive = TRUE THEN 'SENSITIVE'
    WHEN is_sensitive = FALSE THEN 'NON_SENSITIVE'
    ELSE 'NEUTRAL'
END
WHERE information_sensitivity_type = 'NEUTRAL';

-- 迁移 is_original 到 content_category
UPDATE alert_results
SET content_category = CASE
    WHEN is_original = TRUE THEN 'ORIGINAL'
    WHEN is_original = FALSE THEN 'FORWARD'
    ELSE 'ALL'
END
WHERE content_category = 'ORIGINAL';

-- 验证迁移结果
SELECT
    information_sensitivity_type,
    COUNT(*) as count
FROM alert_results
GROUP BY information_sensitivity_type;

SELECT
    content_category,
    COUNT(*) as count
FROM alert_results
GROUP BY content_category;
*/

-- ==================== 安全设置 ====================

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ==================== 完成信息 ====================
-- 统一数据库架构创建完成
-- 包含的功能:
-- 1. 核心业务表 (plans, alert_configurations, alert_results 等)
-- 2. 数据仓库表 (ODS, DWD, DWS, ADS)
-- 3. 性能监控表
-- 4. 全文搜索索引 (包括 ft_idx_title_content)
-- 5. 高级索引优化
-- 6. 视图定义
-- 7. 存储过程
-- 8. 初始数据
-- 9. 性能优化设置
-- ==================================================================================
