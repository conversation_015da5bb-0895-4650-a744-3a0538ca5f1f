-- ==================== 计费系统常用查询SQL ====================
-- 包含日常运维、监控、统计等常用查询
-- 可用于数据分析、问题排查、系统监控

-- ==================== 基础查询 ====================

-- 1. 查看所有活跃订阅
SELECT 
    enterprise_id,
    enterprise_credit_code,
    start_date,
    end_date,
    DATEDIFF(end_date, CURDATE()) as days_remaining,
    auto_renew,
    notes
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE'
ORDER BY end_date;

-- 2. 查看即将过期的订阅（7天内）
SELECT 
    enterprise_id,
    enterprise_credit_code,
    end_date,
    DATEDIFF(end_date, CURDATE()) as days_until_expiration,
    auto_renew,
    CASE 
        WHEN auto_renew = TRUE THEN '自动续费'
        ELSE '需要手动续费'
    END as renewal_status
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY end_date;

-- 3. 查看已过期但状态仍为ACTIVE的订阅（需要处理）
SELECT 
    enterprise_id,
    enterprise_credit_code,
    end_date,
    DATEDIFF(CURDATE(), end_date) as days_overdue,
    status,
    updated_at
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date < CURDATE()
ORDER BY end_date;

-- ==================== 统计查询 ====================

-- 4. 订阅状态统计
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM enterprise_subscriptions), 2) as percentage
FROM enterprise_subscriptions 
GROUP BY status
ORDER BY count DESC;

-- 5. 按月份统计到期订阅数量
SELECT 
    DATE_FORMAT(end_date, '%Y-%m') as expiry_month,
    COUNT(*) as expiring_count,
    SUM(CASE WHEN auto_renew = TRUE THEN 1 ELSE 0 END) as auto_renew_count,
    SUM(CASE WHEN auto_renew = FALSE THEN 1 ELSE 0 END) as manual_renew_count
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE'
AND end_date >= CURDATE()
GROUP BY DATE_FORMAT(end_date, '%Y-%m')
ORDER BY expiry_month;

-- 6. 订阅时长分析
SELECT 
    CASE 
        WHEN DATEDIFF(end_date, start_date) <= 30 THEN '月度订阅(≤30天)'
        WHEN DATEDIFF(end_date, start_date) <= 90 THEN '季度订阅(31-90天)'
        WHEN DATEDIFF(end_date, start_date) <= 365 THEN '年度订阅(91-365天)'
        ELSE '长期订阅(>365天)'
    END as subscription_type,
    COUNT(*) as count,
    AVG(DATEDIFF(end_date, start_date)) as avg_duration_days
FROM enterprise_subscriptions 
GROUP BY 
    CASE 
        WHEN DATEDIFF(end_date, start_date) <= 30 THEN '月度订阅(≤30天)'
        WHEN DATEDIFF(end_date, start_date) <= 90 THEN '季度订阅(31-90天)'
        WHEN DATEDIFF(end_date, start_date) <= 365 THEN '年度订阅(91-365天)'
        ELSE '长期订阅(>365天)'
    END
ORDER BY avg_duration_days;

-- ==================== 监控查询 ====================

-- 7. 今日到期的订阅
SELECT 
    enterprise_id,
    enterprise_credit_code,
    end_date,
    auto_renew,
    notes
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date = CURDATE();

-- 8. 本周到期的订阅
SELECT 
    enterprise_id,
    enterprise_credit_code,
    end_date,
    DAYNAME(end_date) as day_of_week,
    auto_renew
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY end_date;

-- 9. 自动续费订阅监控
SELECT 
    COUNT(*) as total_auto_renew,
    SUM(CASE WHEN end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as expiring_this_week,
    SUM(CASE WHEN end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as expiring_this_month
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE' 
AND auto_renew = TRUE;

-- ==================== 运维查询 ====================

-- 10. 查找特定企业的订阅历史
-- 使用方法：将 'YOUR_ENTERPRISE_ID' 替换为实际的企业ID
SELECT 
    enterprise_id,
    enterprise_credit_code,
    start_date,
    end_date,
    status,
    auto_renew,
    notes,
    created_at,
    updated_at
FROM enterprise_subscriptions 
WHERE enterprise_id = 'YOUR_ENTERPRISE_ID' 
OR enterprise_credit_code = 'YOUR_CREDIT_CODE'
ORDER BY created_at DESC;

-- 11. 批量更新过期订阅状态（运维操作）
-- 注意：这是一个UPDATE语句，请谨慎使用
/*
UPDATE enterprise_subscriptions 
SET status = 'EXPIRED', updated_at = CURRENT_TIMESTAMP 
WHERE status = 'ACTIVE' 
AND end_date < CURDATE();
*/

-- 12. 查看最近创建的订阅
SELECT 
    enterprise_id,
    enterprise_credit_code,
    start_date,
    end_date,
    status,
    created_at
FROM enterprise_subscriptions 
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY created_at DESC;

-- ==================== 数据质量检查 ====================

-- 13. 检查数据完整性
SELECT 
    '数据完整性检查' as check_type,
    SUM(CASE WHEN enterprise_id IS NULL OR enterprise_id = '' THEN 1 ELSE 0 END) as missing_enterprise_id,
    SUM(CASE WHEN start_date IS NULL THEN 1 ELSE 0 END) as missing_start_date,
    SUM(CASE WHEN end_date IS NULL THEN 1 ELSE 0 END) as missing_end_date,
    SUM(CASE WHEN status IS NULL THEN 1 ELSE 0 END) as missing_status,
    SUM(CASE WHEN start_date > end_date THEN 1 ELSE 0 END) as invalid_date_range
FROM enterprise_subscriptions;

-- 14. 检查重复的企业ID
SELECT 
    enterprise_id,
    COUNT(*) as duplicate_count
FROM enterprise_subscriptions 
GROUP BY enterprise_id 
HAVING COUNT(*) > 1;

-- 15. 检查重复的企业信用代码
SELECT 
    enterprise_credit_code,
    COUNT(*) as duplicate_count
FROM enterprise_subscriptions 
WHERE enterprise_credit_code IS NOT NULL
GROUP BY enterprise_credit_code 
HAVING COUNT(*) > 1;

-- ==================== 性能监控查询 ====================

-- 16. 查看表的基本信息
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    (DATA_LENGTH + INDEX_LENGTH) as total_size
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'enterprise_subscriptions';

-- 17. 查看索引使用情况
SHOW INDEX FROM enterprise_subscriptions;

-- ==================== 导出查询 ====================

-- 18. 导出活跃订阅列表（CSV格式）
SELECT 
    enterprise_id as '企业ID',
    enterprise_credit_code as '企业信用代码',
    start_date as '开始日期',
    end_date as '结束日期',
    DATEDIFF(end_date, CURDATE()) as '剩余天数',
    CASE WHEN auto_renew = TRUE THEN '是' ELSE '否' END as '自动续费',
    notes as '备注'
FROM enterprise_subscriptions 
WHERE status = 'ACTIVE'
ORDER BY end_date;

-- ==================== 使用说明 ====================
/*
使用说明：
1. 基础查询(1-3)：日常查看订阅状态
2. 统计查询(4-6)：数据分析和报表
3. 监控查询(7-9)：实时监控和告警
4. 运维查询(10-12)：系统维护和问题排查
5. 数据质量检查(13-15)：数据完整性验证
6. 性能监控(16-17)：系统性能分析
7. 导出查询(18)：数据导出和报表生成

注意事项：
- 带有UPDATE的查询已注释，使用前请仔细检查
- 替换查询中的占位符（如YOUR_ENTERPRISE_ID）
- 生产环境使用前请先在测试环境验证
- 大数据量查询建议添加LIMIT限制
*/
