# SQL文件整理清理说明

## 已整合的文件

以下SQL文件的内容已经整合到 `database_schema_unified.sql` 中：

### 1. 迁移文件 (src/main/resources/db/migration/)
- `V001__Create_Alert_Configuration_Tables.sql` - 预警配置表
- `V002__Create_Alert_Results_Tables.sql` - 预警结果表  
- `V003__Search_Performance_Optimization.sql` - 搜索性能优化

### 2. 独立SQL文件
- `database_schema_corrected.sql` - 修正的预警结果架构
- `database_migration_enum_fields.sql` - 枚举字段迁移脚本

## 新的统一架构文件

### `database_schema_unified.sql`
包含完整的数据库架构，包括：

#### 核心业务表
- `plans` - 方案管理表
- `alert_configurations` - 预警配置表
- `alert_configuration_snapshots` - 配置快照表
- `alert_results` - 预警结果表 (包含新的枚举字段)
- `alert_processing_logs` - 处理日志表

#### 数据仓库表
- `sina_news_ods` - 原始数据存储
- `sina_news_dwd` - 清洗后数据
- `sina_news_dws` - 聚合统计数据
- `sina_news_ads` - 应用数据存储

#### 性能监控表
- `search_performance_metrics` - 搜索性能监控
- `index_usage_stats` - 索引使用统计

#### 索引优化
- 基础性能索引
- 复合索引
- 全文搜索索引 (包括要求的 `ft_idx_title_content`)
- JSON函数索引 (MySQL 8.0+)
- 时间分区友好索引

#### 视图定义
- `v_active_alert_configurations` - 活跃配置视图
- `v_enabled_alert_configurations` - 启用配置视图
- `v_latest_snapshots` - 最新快照视图
- `v_active_alert_results` - 活跃预警结果视图
- `v_alert_statistics` - 预警统计视图
- `v_recent_alerts_optimized` - 最近预警优化视图
- `v_alert_stats_optimized` - 预警统计优化视图

#### 存储过程
- `AnalyzeSlowQueries` - 分析慢查询
- `CleanupOldData` - 清理旧数据
- `GenerateAlertSummary` - 生成预警汇总

#### 特殊功能
- 全文搜索索引: `ALTER TABLE alert_results ADD FULLTEXT INDEX ft_idx_title_content (title, content) WITH PARSER ngram;`
- 枚举字段支持 (替代布尔字段)
- 多租户隔离支持
- 版本控制和快照管理
- 性能监控和优化

## 建议的清理步骤

### 1. 备份现有文件
```bash
mkdir -p backup/sql_files
cp src/main/resources/db/migration/*.sql backup/sql_files/
cp database_schema_corrected.sql backup/sql_files/ 2>/dev/null || true
cp database_migration_enum_fields.sql backup/sql_files/ 2>/dev/null || true
```

### 2. 使用新的统一架构
- 使用 `database_schema_unified.sql` 作为主要的数据库架构文件
- 在新环境中直接运行此文件创建完整架构
- 在现有环境中，可以选择性地运行需要的部分

### 3. 更新部署流程
- 修改部署脚本使用新的统一架构文件
- 更新文档引用新的文件结构
- 在CI/CD流程中使用统一的架构文件

### 4. 可选：移除旧文件
如果确认新架构工作正常，可以移除旧的分散文件：
```bash
# 移除迁移文件 (如果不再使用Flyway/Liquibase)
rm -f src/main/resources/db/migration/V001__Create_Alert_Configuration_Tables.sql
rm -f src/main/resources/db/migration/V002__Create_Alert_Results_Tables.sql  
rm -f src/main/resources/db/migration/V003__Search_Performance_Optimization.sql

# 移除独立SQL文件
rm -f database_schema_corrected.sql
rm -f database_migration_enum_fields.sql
```

## 注意事项

1. **保持迁移文件**: 如果项目使用Flyway或Liquibase等数据库迁移工具，建议保留迁移文件用于版本控制
2. **测试新架构**: 在生产环境使用前，请在测试环境充分验证新的统一架构
3. **数据迁移**: 如果需要从旧的布尔字段迁移到新的枚举字段，请使用文件中提供的迁移脚本
4. **性能监控**: 新架构包含性能监控功能，建议启用以优化查询性能

## 优势

1. **统一管理**: 所有数据库结构在一个文件中，便于维护
2. **完整性**: 包含所有表、索引、视图、存储过程
3. **性能优化**: 包含全面的索引策略和性能监控
4. **现代化**: 使用枚举字段、JSON字段、全文搜索等现代数据库特性
5. **可扩展性**: 支持多租户、版本控制、快照管理等企业级功能
