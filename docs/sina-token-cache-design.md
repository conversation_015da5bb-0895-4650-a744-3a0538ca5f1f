# 新浪舆情通令牌缓存机制设计文档

## 背景问题

新浪舆情通API对令牌获取有严格限制：
- 24小时内令牌只能获取5次
- 令牌有效期是24小时
- 如果服务不稳定，一天重启超过5次，就无法获得有效的令牌

## 解决方案

设计了一个完整的令牌持久化和恢复机制，包含以下核心组件：

### 1. 数据持久化层

#### SinaTokenCache 实体类
- 存储加密的访问令牌和刷新令牌
- 记录令牌过期时间和创建时间
- 跟踪每日获取次数
- 支持活跃状态管理

#### SinaTokenCacheRepository 数据访问层
- 提供令牌的CRUD操作
- 支持按应用ID查询活跃令牌
- 统计每日获取次数
- 清理过期令牌

### 2. 加密安全层

#### TokenEncryptionUtil 加密工具类
- 使用AES-GCM算法加密令牌
- 支持随机IV，确保每次加密结果不同
- 提供加密/解密验证功能
- 可配置加密密钥

### 3. 业务服务层

#### SinaTokenCacheService 令牌缓存服务
- 管理令牌的保存和获取
- 检查每日获取次数限制
- 提供令牌有效性验证
- 支持令牌清理和监控

### 4. 集成改造层

#### SinaAuthServiceImpl 认证服务增强
- 集成持久化缓存功能
- 服务启动时自动恢复令牌
- 优先使用缓存令牌，减少API调用
- 智能的令牌刷新策略

## 核心特性

### 1. 持久化存储
- 令牌存储在MySQL数据库中
- 服务重启后自动恢复有效令牌
- 支持多应用ID的令牌管理

### 2. 安全加密
- 令牌使用AES-GCM算法加密存储
- 支持自定义加密密钥
- 不以明文形式存储敏感信息

### 3. 获取次数限制
- 严格控制每日令牌获取次数（最多5次）
- 实时统计和检查获取次数
- 超过限制时提供明确的错误信息

### 4. 智能恢复机制
- 服务启动时自动检查并恢复有效令牌
- 优先使用持久化缓存，降级到内存缓存
- 支持令牌即将过期时的提前刷新

### 5. 自动清理
- 定时清理过期的令牌记录
- 可配置清理策略和时间间隔
- 保持数据库整洁

## 配置说明

### application.yml 配置项

```yaml
sina:
  token:
    cache:
      cleanup:
        days: 7 # 清理过期令牌的天数
        enabled: true # 是否启用自动清理
        cron: "0 0 2 * * ?" # 清理任务执行时间
    encryption:
      key: ${SINA_TOKEN_ENCRYPTION_KEY:SecuRadar2024TokenEncryptionKey32} # 令牌加密密钥
```

### 环境变量

- `SINA_TOKEN_ENCRYPTION_KEY`: 令牌加密密钥（建议32字符）

## 使用方式

### 1. 自动使用
服务启动后，令牌缓存机制会自动生效：
- 首次获取令牌时自动保存到数据库
- 后续请求优先使用缓存令牌
- 令牌过期时自动刷新（如果未超过每日限制）

### 2. 手动管理
```java
@Autowired
private SinaTokenCacheService tokenCacheService;

// 检查是否可以获取新令牌
boolean canFetch = tokenCacheService.canFetchNewToken(appId);

// 获取今日获取次数
long todayCount = tokenCacheService.getTodayFetchCount(appId);

// 强制使所有令牌失效
tokenCacheService.invalidateAllTokens(appId);
```

## 监控和运维

### 1. 日志监控
- 令牌获取、保存、恢复的详细日志
- 每日获取次数的统计日志
- 错误和异常的详细记录

### 2. 数据库查询
```sql
-- 查看所有活跃令牌
SELECT app_id, expire_time, created_time, daily_fetch_count 
FROM sina_token_cache 
WHERE is_active = TRUE;

-- 查看今日获取次数统计
SELECT app_id, COUNT(*) as fetch_count
FROM sina_token_cache 
WHERE DATE(created_date) = CURDATE()
GROUP BY app_id;
```

### 3. 健康检查
- 定时任务监控令牌状态
- 支持告警通知（可扩展）
- 自动清理过期数据

## 故障恢复

### 1. 服务重启恢复
- 自动从数据库恢复有效令牌
- 无需重新获取，节省API调用次数

### 2. 数据库故障降级
- 自动降级到内存缓存
- 保证服务基本可用性

### 3. 超过获取限制
- 明确的错误提示
- 建议等待到第二天重试
- 支持手动清理计数（紧急情况）

## 安全考虑

1. **加密存储**: 所有令牌都经过AES-GCM加密
2. **密钥管理**: 支持环境变量配置加密密钥
3. **访问控制**: 数据库访问权限控制
4. **日志脱敏**: 敏感信息不记录到日志中

## 性能优化

1. **索引优化**: 关键查询字段都有索引
2. **缓存策略**: 内存+数据库双重缓存
3. **批量操作**: 支持批量清理和更新
4. **异步处理**: 清理任务异步执行

## 扩展性

1. **多租户支持**: 支持多个应用ID的令牌管理
2. **配置灵活**: 大部分参数都可配置
3. **监控扩展**: 预留监控接口，可扩展告警功能
4. **存储扩展**: 可扩展到Redis等其他存储方案
