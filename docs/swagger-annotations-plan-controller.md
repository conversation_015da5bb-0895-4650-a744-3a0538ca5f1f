# PlanController Swagger 注解文档

## 概述

本文档描述了为 PlanController 和相关 DTO 类添加的详细 Swagger 注解，用于生成完整的 API 文档。

## 修改的文件

### 1. PlanController.java

**位置**: `src/main/java/com/czb/hn/web/controllers/PlanController.java`

**主要改进**:
- 添加了完整的 `@Operation` 注解，包含详细的 summary 和 description
- 为每个 API 端点添加了 `@ApiResponses` 注解，包含成功和失败的响应示例
- 为所有路径参数和请求体添加了 `@Parameter` 注解
- 提供了真实的 JSON 示例，符合项目业务场景

**API 端点**:

1. **POST /plans** - 创建方案
   - 支持关键词逻辑运算符（+表示AND，|表示OR，@@转义+字符）
   - 包含详细的请求和响应示例

2. **GET /plans/{id}** - 获取方案详情
   - 根据方案ID获取详细信息
   - 包含企业信息和关键词配置

3. **PUT /plans/{id}** - 更新方案
   - 支持部分字段更新
   - 所有字段均为可选

4. **DELETE /plans/{id}** - 删除方案
   - 删除后无法恢复的警告提示

5. **GET /plans/enterprise/{enterpriseId}** - 根据企业ID获取方案列表
   - 获取企业下所有舆情监控方案

### 2. PlanCreateDTO.java

**位置**: `src/main/java/com/czb/hn/dto/PlanCreateDTO.java`

**添加的注解**:
- `@Schema` 类级别注解，描述DTO用途
- 每个字段的详细 `@Schema` 注解，包含：
  - 字段描述
  - 示例值
  - 必填标识 (`requiredMode`)
  - 长度限制 (`minLength`, `maxLength`)

**字段说明**:
- `name`: 方案名称（必填，1-255字符）
- `description`: 方案描述（可选，最大1000字符）
- `monitorKeywords`: 监控关键词（必填，支持逻辑运算符，最大5000字符）
- `excludeKeywords`: 排除关键词（可选，使用|分隔，最大2000字符）
- `enterpriseId`: 企业ID（必填，1-255字符）

### 3. PlanUpdateDTO.java

**位置**: `src/main/java/com/czb/hn/dto/PlanUpdateDTO.java`

**特点**:
- 所有字段均为可选，支持部分更新
- 与 PlanCreateDTO 类似的注解结构
- 明确标注为更新DTO，避免与创建DTO混淆

### 4. PlanDTO.java

**位置**: `src/main/java/com/czb/hn/dto/PlanDTO.java`

**添加的注解**:
- 完整的响应DTO注解
- 时间字段使用 `@JsonFormat` 确保格式一致性
- 关联企业信息的描述

**字段说明**:
- `id`: 方案ID（必填）
- `name`: 方案名称（必填）
- `description`: 方案描述
- `monitorKeywords`: 监控关键词（必填）
- `excludeKeywords`: 排除关键词
- `enterprise`: 关联企业信息（必填）
- `createdAt`: 创建时间（必填，格式：yyyy-MM-dd HH:mm:ss）
- `updatedAt`: 更新时间（必填，格式：yyyy-MM-dd HH:mm:ss）

### 5. EnterpriseDTO.java

**位置**: `src/main/java/com/czb/hn/dto/EnterpriseDTO.java`

**字段说明**:
- `id`: 企业ID（必填）
- `name`: 企业名称（必填）

## 示例数据

### 创建方案请求示例

```json
{
    "name": "华能资本舆情监控方案",
    "description": "针对华能资本的全面舆情监控方案，包括投资动态、市场表现等",
    "monitorKeywords": "华能资本+投资|华能集团+金融|新能源+华能",
    "excludeKeywords": "广告|招聘|测试",
    "enterpriseId": "enterprise123"
}
```

### 方案信息响应示例

```json
{
    "result": "SUCCESS",
    "message": "操作成功",
    "data": {
        "id": 1,
        "name": "华能资本舆情监控方案",
        "description": "针对华能资本的全面舆情监控方案",
        "monitorKeywords": "华能资本+投资|华能集团+金融|新能源+华能",
        "excludeKeywords": "广告|招聘|测试",
        "enterprise": {
            "id": "enterprise123",
            "name": "华能资本服务有限公司"
        },
        "createdAt": "2024-01-15 10:30:00",
        "updatedAt": "2024-01-15 10:30:00"
    }
}
```

## 关键词处理说明

### 监控关键词格式
- 支持逻辑运算符：
  - `+` 表示 AND 逻辑
  - `|` 表示 OR 逻辑
  - `@@` 转义 + 字符
- 示例：`华能资本+投资|华能集团+金融` 表示 "(华能资本 AND 投资) OR (华能集团 AND 金融)"

### 排除关键词格式
- 使用 `|` 分隔，表示 OR 逻辑
- 示例：`广告|招聘|测试` 表示排除包含"广告"或"招聘"或"测试"的内容

## 技术细节

### 注解使用规范
1. 使用完整的类名避免命名冲突：`@io.swagger.v3.oas.annotations.responses.ApiResponse`
2. 为项目的 ApiResponse 类使用完整路径：`com.czb.hn.dto.response.ApiResponse.class`
3. 时间格式统一使用：`yyyy-MM-dd HH:mm:ss`
4. 所有示例数据都基于真实业务场景

### 验证规则
- 所有 DTO 都在构造函数中包含验证逻辑
- 关键词格式验证使用 `KeywordUtils` 工具类
- 字段长度限制在 `@Schema` 注解中明确定义

## 使用建议

1. **API 文档生成**: 这些注解将自动生成详细的 Swagger API 文档
2. **前端集成**: 前端开发者可以直接使用生成的文档了解 API 结构
3. **测试支持**: 示例数据可以直接用于 API 测试
4. **维护性**: 注解中的描述和示例需要与实际业务逻辑保持同步

## 编译验证

所有修改已通过 Maven 编译验证，确保没有语法错误和依赖问题。
