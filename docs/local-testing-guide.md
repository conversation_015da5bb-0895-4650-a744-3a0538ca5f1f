# 本地数据采集测试指南

## 概述

为了方便本地开发和测试数据采集功能，系统提供了从本地文件读取测试数据的功能，避免频繁调用远程API。

## 配置说明

### 1. 启用本地文件模式

在 `application-local.yml` 中配置：

```yaml
sina:
  api:
    use:
      local:
        file: true  # 启用本地文件模式
    local:
      file:
        path: docs/yuqing.txt  # 本地测试数据文件路径
```

### 2. 启动应用

使用本地配置启动应用：

```bash
# 方式1：使用Maven
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 方式2：使用Java命令
java -jar target/securadar.jar --spring.profiles.active=local

# 方式3：在IDE中设置
# Active profiles: local
```

## 测试数据文件

测试数据文件位于 `docs/yuqing.txt`，包含真实的舆情数据格式。每行是一个JSON对象，格式如下：

```json
{
  "data": {
    "contentId": "11751423729514673230574228",
    "author": "雅致",
    "title": "转发了",
    "text": "<div><p>转发了</p></div>",
    "publishTime": "2025-07-02 10:34:58",
    "captureTime": "2025-07-02 10:35:28",
    "matchInfo": {
      "info": "永诚财产保险股份有限公司",
      "name": "2",
      "ticket": "jnwkeyword2025070113kxoc9k268z3z",
      "type": "1"
    },
    "contentExt": {
      "sensitivityType": 2,
      "emotion": "中性",
      "isOriginal": "2",
      "isNormarlData": "1"
    }
  },
  "objType": "5",
  "offset": 1,
  "sourceType": null
}
```

## 测试接口

启用本地模式后，可以使用以下测试接口：

### 1. 测试数据获取

```http
GET /api/test/collector/fetch?ticket=test-ticket&offset=1&limit=5
```

参数说明：
- `ticket`: 票据ID（本地模式下可以是任意值）
- `offset`: 偏移量，从1开始
- `limit`: 获取数量限制

### 2. 测试保存到ODS

```http
POST /api/test/collector/save-to-ods?ticket=test-ticket&offset=1&limit=3
```

这个接口会：
1. 从本地文件获取数据
2. 将数据保存到ODS层
3. 返回保存结果

### 3. 获取最新偏移量

```http
GET /api/test/collector/latest-offset
```

返回当前ODS中的最新偏移量。

## 分页测试

本地文件模式支持分页测试：

```bash
# 获取第1-5条数据
curl "http://localhost:8080/api/test/collector/fetch?offset=1&limit=5"

# 获取第6-10条数据  
curl "http://localhost:8080/api/test/collector/fetch?offset=6&limit=5"

# 获取第11-15条数据
curl "http://localhost:8080/api/test/collector/fetch?offset=11&limit=5"
```

## 日志查看

本地模式下启用了详细日志，可以查看：

```
2025-07-03 10:30:00 [main] DEBUG com.czb.hn.service.collector - Reading test data from local file: docs/yuqing.txt, offset: 1, limit: 5
2025-07-03 10:30:00 [main] INFO  com.czb.hn.service.collector - Loaded 5 records from local file (requested offset: 1, limit: 5)
```

## 切换回远程模式

要切换回远程API模式，只需要：

1. 修改配置：
```yaml
sina:
  api:
    use:
      local:
        file: false  # 禁用本地文件模式
```

2. 或者使用不同的profile启动应用

## 注意事项

1. **数据格式**：确保 `docs/yuqing.txt` 中的数据格式正确，每行一个JSON对象
2. **偏移量**：本地模式下的偏移量是基于文件中的行数，从1开始
3. **性能**：本地文件模式每次都会读取整个文件，适合小量测试数据
4. **安全性**：测试控制器只在启用本地文件模式时才会注册，生产环境不会暴露

## 故障排除

### 1. 文件不存在
```
WARN - Local test file does not exist: docs/yuqing.txt
```
解决：确保文件路径正确，文件存在

### 2. JSON解析错误
```
ERROR - Error parsing response line 1: ...
```
解决：检查JSON格式是否正确

### 3. 接口404
确保启用了本地文件模式：
```yaml
sina.api.use.local.file: true
```
