# Alert Configuration Module

## Overview

The Alert Configuration Module provides comprehensive sentiment monitoring alert configuration management with JSON-based settings storage and automatic snapshot versioning for audit and rollback purposes.

## Features

- **JSON-based Configuration Storage**: Complex configuration settings stored as JSON for flexibility
- **Automatic Snapshot Management**: Every create/update operation generates versioned snapshots
- **Version Control**: Complete audit trail with rollback capabilities
- **Performance Optimized**: Cached configuration access for high-frequency operations
- **Comprehensive Validation**: Business rules enforcement and data validation
- **RESTful API**: Complete CRUD operations with Swagger documentation

## Architecture

### Core Components

1. **Entity Layer**

   - `AlertConfiguration`: Main configuration entity with JSON fields
   - `AlertConfigurationSnapshot`: Version control and audit trail

2. **Service Layer**

   - `AlertConfigurationService`: Main business logic and CRUD operations
   - `AlertConfigurationConsumerService`: Optimized read access for other modules

3. **Repository Layer**

   - `AlertConfigurationRepository`: Data access for configurations
   - `AlertConfigurationSnapshotRepository`: Snapshot management

4. **Controller Layer**

   - `AlertConfigurationController`: RESTful API endpoints

5. **Utility Classes**
   - `AlertConfigurationMapper`: Entity/DTO mapping and JSON handling
   - `AlertConfigurationValidator`: Comprehensive validation logic

## Configuration Structure

### Alert Keywords

```json
{
  "keywords": ["urgent", "critical", "breaking"],
  "description": "Keywords that trigger alerts"
}
```

### Content Settings

```json
{
  "sensitivityType": "ALL|SENSITIVE|NEUTRAL|NON_SENSITIVE",
  "preciseFiltering": true,
  "sourceTypes": ["WEIBO", "WECHAT", "WEBSITE"],
  "contentTypes": ["TEXT", "IMAGE", "VIDEO"],
  "resultDisplay": "NORMAL",
  "userTypes": ["MEDIA", "GOVERNMENT"]
  // ... additional filtering options
}
```

### Threshold Settings

```json
{
  "conditionRelation": "AND|OR",
  "interactionCount": { "enabled": true, "threshold": 100 },
  "fansCount": { "enabled": true, "threshold": 1000 },
  "readCount": { "enabled": false, "threshold": null },
  "keywordFrequency": [{ "keyword": "urgent", "frequency": 3 }]
}
```

### Level Settings

```json
{
  "sourceLevel": {
    "NATIONAL": "SEVERE",
    "PROVINCIAL": "MODERATE",
    "MUNICIPAL": "GENERAL"
  },
  "interactionThresholds": {
    "general": { "min": 0, "max": 99 },
    "medium": { "min": 100, "max": 999 },
    "severe": { "min": 1000, "max": null }
  }
}
```

### Reception Settings

```json
{
  "receptionTime": "DAILY|WORKDAYS|HOLIDAYS",
  "alertInterval": 30,
  "receptionPeriod": { "start": "09:00", "end": "18:00" },
  "receptionMethods": {
    "email": {
      "enabled": true,
      "recipients": [{ "name": "John Doe", "email": "<EMAIL>" }]
    },
    "sms": {
      "enabled": false,
      "recipients": []
    }
  }
}
```

## API Endpoints

### Configuration Management

| Method | Endpoint                            | Description                         |
| ------ | ----------------------------------- | ----------------------------------- |
| POST   | `/alert-configurations`             | Create new configuration            |
| GET    | `/alert-configurations/{id}`        | Get configuration by ID             |
| PUT    | `/alert-configurations/{id}`        | Update configuration                |
| DELETE | `/alert-configurations/{id}`        | Soft delete configuration           |
| GET    | `/alert-configurations`             | List all configurations (paginated) |
| PATCH  | `/alert-configurations/{id}/toggle` | Enable/disable configuration        |

### Query Endpoints

| Method | Endpoint                                             | Description             |
| ------ | ---------------------------------------------------- | ----------------------- |
| GET    | `/alert-configurations/by-enterprise/{enterpriseId}` | Get by enterprise       |
| GET    | `/alert-configurations/by-plan/{planId}`             | Get by plan             |
| GET    | `/alert-configurations/search`                       | Search by name pattern  |
| GET    | `/alert-configurations/statistics/{enterpriseId}`    | Get statistics          |
| GET    | `/alert-configurations/name-available`               | Check name availability |

### Snapshot Management

| Method | Endpoint                                         | Description            |
| ------ | ------------------------------------------------ | ---------------------- |
| GET    | `/alert-configurations/{id}/snapshots`           | Get all snapshots      |
| GET    | `/alert-configurations/{id}/snapshots/{version}` | Get specific snapshot  |
| POST   | `/alert-configurations/{id}/rollback/{version}`  | Rollback to version    |
| POST   | `/alert-configurations/{id}/snapshots`           | Create manual snapshot |

## Usage Examples

### Creating a Configuration

```bash
curl -X POST http://localhost:8080/alert-configurations \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Critical News Alert",
    "description": "Monitor critical news events",
    "enterpriseId": "enterprise123",
    "enabled": true,
    "alertKeywords": {
      "keywords": ["urgent", "critical", "breaking"],
      "description": "Critical event keywords"
    },
    "contentSettings": {
      "sensitivityType": "SENSITIVE",
      "sourceTypes": ["WEIBO", "WECHAT"],
      "contentTypes": ["TEXT", "IMAGE"]
    },
    "thresholdSettings": {
      "conditionRelation": "OR",
      "interactionCount": {"enabled": true, "threshold": 100}
    },
    "levelSettings": {
      "interactionThresholds": {
        "general": {"min": 0, "max": 99},
        "medium": {"min": 100, "max": 999},
        "severe": {"min": 1000, "max": null}
      }
    },
    "receptionSettings": {
      "receptionTime": "DAILY",
      "alertInterval": 60,
      "receptionMethods": {
        "email": {
          "enabled": true,
          "recipients": [
            {"name": "Admin", "email": "<EMAIL>"}
          ]
        }
      }
    }
  }'
```

### Consuming Configurations (For Other Modules)

```java
@Autowired
private AlertConfigurationConsumerService consumerService;

// Get all active configurations
List<AlertConfigurationResponseDto> activeConfigs =
    consumerService.getAllActiveConfigurations();

// Get configurations for specific enterprise
List<AlertConfigurationResponseDto> enterpriseConfigs =
    consumerService.getActiveConfigurationsByEnterprise("enterprise123");

// Find matching configurations for content
List<AlertConfigurationResponseDto> matchingConfigs =
    consumerService.getMatchingConfigurations(
        Arrays.asList("urgent", "breaking"),
        "WEIBO",
        "SENSITIVE"
    );
```

## Database Schema

### alert_configurations Table

- `id`: Primary key
- `name`: Configuration name
- `enterprise_id`: Enterprise identifier
- `enabled`: Whether alert is enabled
- `alert_keywords`: JSON field for keywords
- `content_settings`: JSON field for content filters
- `threshold_settings`: JSON field for thresholds
- `level_settings`: JSON field for level classification
- `reception_settings`: JSON field for notifications
- `current_version`: Current version number
- `is_active`: Soft delete flag

### alert_configuration_snapshots Table

- `id`: Primary key
- `configuration_id`: Foreign key to configuration
- `version_number`: Version number
- `snapshot_data`: Complete configuration as JSON
- `operation_type`: CREATE, UPDATE, ROLLBACK, etc.
- `is_active`: Currently active snapshot flag

## Performance Considerations

### Caching Strategy

- Active configurations cached for 5 minutes
- Cache automatically refreshed on updates
- Separate cache for enterprise and plan-specific queries

### Database Optimization

- Indexes on frequently queried fields
- JSON functional indexes for MySQL 8.0+
- Composite indexes for common query patterns

### Snapshot Management

- Automatic cleanup of old snapshots
- Configurable retention policy
- Compression for large snapshots

## Validation Rules

### Business Rules

- At least one threshold condition must be enabled
- At least one reception method must be enabled
- Configuration names must be unique within enterprise
- Alert interval must be at least 30 minutes

### Data Validation

- Email format validation
- Phone number format validation (Chinese mobile)
- Time format validation (HH:mm)
- JSON schema validation for complex objects

## Integration Points

### With Existing Modules

- **Plan Module**: Link configurations to monitoring plans
- **Enterprise Module**: Associate with enterprises
- **Search Module**: Use for alert triggering
- **Notification Module**: Use reception settings

### Event-Driven Updates

- Configuration changes trigger cache refresh
- Snapshot creation events for audit logging
- Integration with monitoring systems

## Testing

### Integration Tests

- Complete CRUD operation testing
- Snapshot management testing
- Consumer service integration
- Validation rule testing

### Performance Tests

- Cache performance validation
- Database query optimization
- Concurrent access testing

## Monitoring and Maintenance

### Health Checks

- Configuration service availability
- Cache hit ratio monitoring
- Database connection health

### Maintenance Tasks

- Scheduled snapshot cleanup
- Cache statistics monitoring
- Performance metrics collection

## Future Enhancements

- Configuration templates
- Bulk operations
- Advanced filtering options
- Real-time configuration updates
- Configuration import/export
- Advanced analytics and reporting
