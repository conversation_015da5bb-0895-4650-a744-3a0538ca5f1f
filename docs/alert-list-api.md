# 预警信息列表API文档

## 概述

预警信息列表功能提供工作台预警信息的展示，支持指定方案或企业下所有方案的查询，按预警时间降序排列。

## API接口

### 获取预警信息列表

**接口地址**: `GET /workbench/alert-list`

**功能描述**: 获取工作台预警信息列表，展示预警信息的内容、标题、敏感度、预警时间、来源、关键词等信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| planId | Long | 否 | 方案ID，不传则获取企业下所有方案的预警信息 | 1 |
| limit | Integer | 否 | 返回记录数限制，默认20，最大100 | 20 |

**请求示例**:

```bash
# 获取指定方案的预警信息列表
GET /workbench/alert-list?planId=1&limit=20

# 获取企业下所有方案的预警信息列表
GET /workbench/alert-list?limit=20

# 使用默认限制（20条）
GET /workbench/alert-list
```

**响应格式**:

```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "title": "重要安全预警",
      "content": "发现重要安全漏洞，请及时关注",
      "sensitivityType": "SENSITIVE",
      "sensitivityDescription": "敏感",
      "warningTime": "2024-01-15 14:30:00",
      "source": "微博",
      "involvedKeywords": "安全,漏洞,预警",
      "warningLevel": "SEVERE",
      "warningLevelDescription": "严重",
      "contentCategory": "ORIGINAL",
      "contentCategoryDescription": "原创",
      "similarArticleCount": 5
    },
    {
      "id": 2,
      "title": "一般信息提醒",
      "content": "一般性信息提醒内容",
      "sensitivityType": "NEUTRAL",
      "sensitivityDescription": "中性",
      "warningTime": "2024-01-15 13:20:00",
      "source": "微信",
      "involvedKeywords": "信息,提醒",
      "warningLevel": "GENERAL",
      "warningLevelDescription": "一般",
      "contentCategory": "ORIGINAL",
      "contentCategoryDescription": "原创",
      "similarArticleCount": 2
    }
  ]
}
```

**响应字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 预警ID |
| title | String | 预警标题 |
| content | String | 预警内容 |
| sensitivityType | String | 敏感度类型（SENSITIVE/NON_SENSITIVE/NEUTRAL） |
| sensitivityDescription | String | 敏感度描述（敏感/非敏感/中性） |
| warningTime | String | 预警时间（yyyy-MM-dd HH:mm:ss格式） |
| source | String | 来源（如：微博、微信等） |
| involvedKeywords | String | 涉及关键词 |
| warningLevel | String | 预警级别（GENERAL/MODERATE/SEVERE） |
| warningLevelDescription | String | 预警级别描述（一般/中等/严重） |
| contentCategory | String | 内容类别（ORIGINAL/NEUTRAL） |
| contentCategoryDescription | String | 内容类别描述（原创/中性） |
| similarArticleCount | Integer | 相似文章数量 |

## 使用场景

### 1. 指定方案预警列表

用于查看特定监控方案的预警信息：

```bash
GET /workbench/alert-list?planId=1&limit=20
```

### 2. 企业全量预警列表

用于查看企业下所有方案的预警信息：

```bash
GET /workbench/alert-list?limit=20
```

### 3. 自定义数量限制

支持自定义返回记录数：

```bash
# 获取最新10条预警
GET /workbench/alert-list?limit=10

# 获取最新50条预警
GET /workbench/alert-list?limit=50
```

## 功能特点

### 1. 排序规则
- 按预警时间降序排列
- 最新的预警信息排在前面

### 2. 数据展示
- **内容**: 预警的具体内容
- **标题**: 预警标题
- **敏感度**: 信息敏感程度（敏感/非敏感/中性）
- **预警时间**: 预警发生的时间
- **来源**: 信息来源平台
- **关键词**: 涉及的关键词

### 3. 限制控制
- 默认返回20条记录
- 最大支持100条记录
- 超过最大限制时自动调整为100

### 4. 权限控制
- 基于用户企业ID进行数据隔离
- 只能查询当前用户所属企业的预警信息

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查limit参数是否大于0 |
| 401 | 用户未登录 | 确保用户已登录并有有效会话 |
| 500 | 服务器内部错误 | 联系系统管理员 |

### 错误响应示例

```json
{
  "result": "ERROR",
  "message": "limit参数必须大于0",
  "data": null
}
```

## 技术实现

### 数据来源
- 从alert_results表中查询预警信息
- 支持方案级别和企业级别的查询

### 性能优化
- 使用数据库索引优化查询性能
- 分页查询避免大量数据加载
- 按预警时间降序排列，快速获取最新数据

### 服务架构
- 复用现有的AlertSearchService服务
- 遵循现有的服务架构模式
- 统一的异常处理和日志记录

## 测试用例

项目包含完整的单元测试：

- `AlertSearchServiceAlertListTest` - 预警列表功能测试

运行测试：

```bash
mvn test -Dtest=AlertSearchServiceAlertListTest
```

## 注意事项

1. 列表不分页，通过limit参数控制返回数量
2. 默认按预警时间降序排列，无法自定义排序
3. limit参数最大值为100，超过时自动调整
4. 企业级查询会返回该企业下所有方案的预警信息
5. 预警信息实时查询，无缓存机制
6. 支持空结果返回，不会抛出异常

## 与现有功能的关系

### 与统计功能的配合
- 预警信息列表展示具体的预警详情
- 统计功能提供预警数量的汇总信息
- 两者配合提供完整的预警信息视图

### 与AlertSearchService的集成
- 复用现有的AlertSearchService服务
- 保持服务架构的一致性
- 利用现有的数据访问层和异常处理机制
