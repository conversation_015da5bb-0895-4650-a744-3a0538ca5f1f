# 关键词逻辑运算符使用示例

## 概述

本文档提供了SecuRadar系统中关键词逻辑运算符的详细使用示例和最佳实践。

## 逻辑运算符说明

### 基本运算符

- `+` : AND逻辑，表示"并且"
- `|` : OR逻辑，表示"或者"  
- `@@` : 转义符，用于在关键词中包含"+"字符

### 优先级

1. `+` (AND) 优先级高于 `|` (OR)
2. 先处理AND组合，再处理OR关系

## 监控关键词示例

### 1. 简单OR逻辑

```
输入: "Java|Python|JavaScript"
解析结果:
- OR关键词: ["Java", "Python", "JavaScript"]
- AND组合: []
逻辑含义: Java OR Python OR JavaScript
```

### 2. 简单AND逻辑

```
输入: "Java+Spring+Boot"
解析结果:
- OR关键词: []
- AND组合: [["Java", "Spring", "Boot"]]
逻辑含义: Java AND Spring AND Boot
```

### 3. 混合逻辑

```
输入: "Java+Spring|Python+Django|React"
解析结果:
- OR关键词: ["React"]
- AND组合: [["Java", "Spring"], ["Python", "Django"]]
逻辑含义: (Java AND Spring) OR (Python AND Django) OR React
```

### 4. 转义字符使用

```
输入: "C@@语言|Java@@Script+Node.js"
解析结果:
- OR关键词: ["C+语言"]
- AND组合: [["Java+Script", "Node.js"]]
逻辑含义: C+语言 OR (Java+Script AND Node.js)
```

### 5. 复杂组合示例

```
输入: "前端+React+TypeScript|后端+Java+Spring|全栈+Node.js+Vue|Python@@机器学习"
解析结果:
- OR关键词: ["Python+机器学习"]
- AND组合: [
    ["前端", "React", "TypeScript"],
    ["后端", "Java", "Spring"],
    ["全栈", "Node.js", "Vue"]
  ]
逻辑含义: 
  (前端 AND React AND TypeScript) OR 
  (后端 AND Java AND Spring) OR 
  (全栈 AND Node.js AND Vue) OR 
  Python+机器学习
```

## 排除关键词示例

### 1. 简单排除

```
输入: "测试|Demo|调试"
解析结果:
- 关键词: ["测试", "Demo", "调试"]
逻辑含义: 排除包含"测试"或"Demo"或"调试"的内容
```

### 2. 转义字符排除

```
输入: "C@@语言入门|Java@@Script基础"
解析结果:
- 关键词: ["C+语言入门", "Java+Script基础"]
逻辑含义: 排除包含"C+语言入门"或"Java+Script基础"的内容
```

## 实际应用场景

### 场景1：技术招聘监控

```java
// 监控关键词：寻找Java后端或前端React开发职位
String monitorKeywords = "Java+后端+开发|React+前端+开发|全栈+开发";

// 排除关键词：排除实习和培训相关
String excludeKeywords = "实习|培训|学生|兼职";

PlanCreateDTO plan = new PlanCreateDTO(
    "技术招聘监控",
    "监控技术岗位招聘信息",
    monitorKeywords,
    excludeKeywords,
    "enterprise123"
);
```

### 场景2：产品竞品分析

```java
// 监控关键词：关注特定产品和竞品
String monitorKeywords = "iPhone+新功能|华为+手机+发布|小米+产品+更新";

// 排除关键词：排除二手和维修信息
String excludeKeywords = "二手|维修|翻新|山寨";

PlanCreateDTO plan = new PlanCreateDTO(
    "手机产品监控",
    "监控手机行业产品动态",
    monitorKeywords,
    excludeKeywords,
    "enterprise456"
);
```

### 场景3：品牌舆情监控

```java
// 监控关键词：品牌相关讨论
String monitorKeywords = "品牌名+评价|品牌名+体验|品牌名+服务|品牌名+质量";

// 排除关键词：排除广告和营销内容
String excludeKeywords = "广告|推广|营销|代购|假货";

PlanCreateDTO plan = new PlanCreateDTO(
    "品牌舆情监控",
    "监控品牌相关舆情信息",
    monitorKeywords,
    excludeKeywords,
    "enterprise789"
);
```

## API调用示例

### 1. 创建带逻辑关键词的方案

```bash
curl -X POST http://localhost:8080/plans \
  -H "Content-Type: application/json" \
  -d '{
    "name": "技术监控方案",
    "description": "监控技术相关信息",
    "monitorKeywords": "Java+Spring|Python+Django|React+TypeScript",
    "excludeKeywords": "测试|Demo|教程",
    "enterpriseId": "enterprise123"
  }'
```

### 2. 获取关键词逻辑解析结果

```bash
curl -X GET http://localhost:8080/plans/1/keywords/logic
```

响应示例：
```json
{
  "status": "SUCCESS",
  "data": {
    "monitorKeywords": {
      "andGroups": [
        ["Java", "Spring"],
        ["Python", "Django"],
        ["React", "TypeScript"]
      ],
      "orKeywords": [],
      "allKeywords": ["Java", "Spring", "Python", "Django", "React", "TypeScript"]
    },
    "excludeKeywords": {
      "keywords": ["测试", "Demo", "教程"]
    }
  }
}
```

### 3. 验证关键词格式

```bash
curl -X POST http://localhost:8080/plans/keywords/validate \
  -H "Content-Type: application/json" \
  -d '"Java+Spring|Python+Django"'
```

## 最佳实践

### 1. 关键词设计原则

- **精确性**: 使用具体的关键词组合，避免过于宽泛
- **完整性**: 考虑同义词和相关词汇
- **排除性**: 合理使用排除关键词过滤噪音

### 2. 逻辑组合建议

- **AND组合**: 用于精确匹配，如"Java+Spring+Boot"
- **OR组合**: 用于扩大覆盖范围，如"Java|Python|JavaScript"
- **混合使用**: 平衡精确性和覆盖面

### 3. 性能考虑

- 避免过多的AND组合（建议每个AND组合不超过5个关键词）
- 合理控制总关键词数量（建议不超过50个）
- 定期优化关键词组合，移除无效关键词

### 4. 测试验证

- 使用验证API测试关键词格式
- 通过逻辑解析API确认解析结果
- 小范围测试后再正式部署

## 注意事项

1. **转义字符**: 关键词中包含"+"时必须使用"@@"转义
2. **空格处理**: 关键词前后的空格会被自动去除
3. **重复去除**: 重复的关键词会被自动去重
4. **长度限制**: 单个关键词长度不超过50个字符
5. **数量限制**: 总关键词数量不超过100个

## 故障排除

### 常见错误

1. **"关键词包含无效字符"**: 检查是否使用了特殊字符，确保只使用中文、英文、数字、空格、连字符、下划线和@@
2. **"关键词数量超限"**: 减少关键词数量或优化关键词组合
3. **"关键词长度超限"**: 缩短过长的关键词

### 调试技巧

1. 使用验证API检查关键词格式
2. 使用逻辑解析API查看解析结果
3. 检查日志中的详细错误信息
