# 索引列名修复总结

## 问题描述

在JPA实体类中，`@Index`注解的`columnList`属性应该使用数据库列名（snake_case），而不是Java字段名（camelCase）。

## 修复的实体类

### 1. AlertResult.java
**修复前（错误）：**
```java
@Index(name = "idx_enterprise_warning_time", columnList = "enterpriseId,warningTime")
@Index(name = "idx_plan_warning_time", columnList = "planId,warningTime")
@Index(name = "idx_configuration_time", columnList = "configurationId,warningTime")
@Index(name = "idx_warning_level", columnList = "warningLevel")
@Index(name = "idx_information_sensitivity_type", columnList = "informationSensitivityType")
@Index(name = "idx_content_category", columnList = "contentCategory")
@Index(name = "idx_original_content", columnList = "originalContentId")
@Index(name = "idx_enterprise_sensitive_level", columnList = "enterpriseId,informationSensitivityType,warningLevel")
@Index(name = "idx_plan_category_time", columnList = "planId,contentCategory,warningTime")
@Index(name = "idx_enterprise_source_time", columnList = "enterpriseId,source,warningTime")
@Index(name = "idx_source_type", columnList = "sourceType")
@Index(name = "idx_content_type", columnList = "contentType")
@Index(name = "idx_content_match_type", columnList = "contentMatchType")
@Index(name = "idx_media_level", columnList = "mediaLevel")
@Index(name = "idx_enterprise_source_type_time", columnList = "enterpriseId,sourceType,warningTime")
@Index(name = "idx_plan_media_level_time", columnList = "planId,mediaLevel,warningTime")
@Index(name = "idx_content_type_sensitivity", columnList = "contentType,informationSensitivityType")
```

**修复后（正确）：**
```java
@Index(name = "idx_enterprise_warning_time", columnList = "enterprise_id,warning_time")
@Index(name = "idx_plan_warning_time", columnList = "plan_id,warning_time")
@Index(name = "idx_configuration_time", columnList = "configuration_id,warning_time")
@Index(name = "idx_warning_level", columnList = "warning_level")
@Index(name = "idx_information_sensitivity_type", columnList = "information_sensitivity_type")
@Index(name = "idx_content_category", columnList = "content_category")
@Index(name = "idx_original_content", columnList = "original_content_id")
@Index(name = "idx_enterprise_sensitive_level", columnList = "enterprise_id,information_sensitivity_type,warning_level")
@Index(name = "idx_plan_category_time", columnList = "plan_id,content_category,warning_time")
@Index(name = "idx_enterprise_source_time", columnList = "enterprise_id,source,warning_time")
@Index(name = "idx_source_type", columnList = "source_type")
@Index(name = "idx_content_type", columnList = "content_type")
@Index(name = "idx_content_match_type", columnList = "content_match_type")
@Index(name = "idx_media_level", columnList = "media_level")
@Index(name = "idx_enterprise_source_type_time", columnList = "enterprise_id,source_type,warning_time")
@Index(name = "idx_plan_media_level_time", columnList = "plan_id,media_level,warning_time")
@Index(name = "idx_content_type_sensitivity", columnList = "content_type,information_sensitivity_type")
```

### 2. AlertConfiguration.java
**修复前：**
```java
@Index(name = "idx_plan_id", columnList = "planId")
@Index(name = "idx_enterprise_id", columnList = "enterpriseId")
@Index(name = "idx_created_at", columnList = "createdAt")
```

**修复后：**
```java
@Index(name = "idx_plan_id", columnList = "plan_id")
@Index(name = "idx_enterprise_id", columnList = "enterprise_id")
@Index(name = "idx_created_at", columnList = "created_at")
```

### 3. BriefingConfiguration.java
**修复前：**
```java
@Index(name = "idx_plan_id", columnList = "planId")
```

**修复后：**
```java
@Index(name = "idx_plan_id", columnList = "plan_id")
```

### 4. AlertNotificationQueue.java
**修复前：**
```java
@Index(name = "idx_scheduled_status", columnList = "scheduledTime,status")
@Index(name = "idx_enterprise_scheduled", columnList = "enterpriseId,scheduledTime")
@Index(name = "idx_alert_id", columnList = "alertId")
@Index(name = "idx_configuration_id", columnList = "configurationId")
@Index(name = "idx_status_type", columnList = "status,notificationType")
@Index(name = "idx_enterprise_status", columnList = "enterpriseId,status")
```

**修复后：**
```java
@Index(name = "idx_scheduled_status", columnList = "scheduled_time,status")
@Index(name = "idx_enterprise_scheduled", columnList = "enterprise_id,scheduled_time")
@Index(name = "idx_alert_id", columnList = "alert_id")
@Index(name = "idx_configuration_id", columnList = "configuration_id")
@Index(name = "idx_status_type", columnList = "status,notification_type")
@Index(name = "idx_enterprise_status", columnList = "enterprise_id,status")
```

### 5. AlertPushDetail.java
**修复前：**
```java
@Index(name = "idx_alert_id", columnList = "alertId")
@Index(name = "idx_alert_push_type", columnList = "alertId,pushType")
@Index(name = "idx_push_time", columnList = "pushTime")
@Index(name = "idx_push_status", columnList = "pushStatus")
@Index(name = "idx_enterprise_push_time", columnList = "enterpriseId,pushTime")
@Index(name = "idx_account_push_type", columnList = "accountInfo,pushType")
```

**修复后：**
```java
@Index(name = "idx_alert_id", columnList = "alert_id")
@Index(name = "idx_alert_push_type", columnList = "alert_id,push_type")
@Index(name = "idx_push_time", columnList = "push_time")
@Index(name = "idx_push_status", columnList = "push_status")
@Index(name = "idx_enterprise_push_time", columnList = "enterprise_id,push_time")
@Index(name = "idx_account_push_type", columnList = "account_info,push_type")
```

### 6. UserLoginRecord.java
**修复前：**
```java
@Index(name = "idx_user_id", columnList = "userId")
@Index(name = "idx_enterprise_id", columnList = "enterpriseId")
@Index(name = "idx_enterprise_code", columnList = "enterpriseCode")
@Index(name = "idx_login_time", columnList = "loginTime")
@Index(name = "idx_user_login_time", columnList = "userId, loginTime")
@Index(name = "idx_enterprise_login_time", columnList = "enterpriseId, loginTime")
@Index(name = "idx_enterprise_code_time", columnList = "enterpriseCode, loginTime")
```

**修复后：**
```java
@Index(name = "idx_user_id", columnList = "user_id")
@Index(name = "idx_enterprise_id", columnList = "enterprise_id")
@Index(name = "idx_enterprise_code", columnList = "enterprise_code")
@Index(name = "idx_login_time", columnList = "login_time")
@Index(name = "idx_user_login_time", columnList = "user_id, login_time")
@Index(name = "idx_enterprise_login_time", columnList = "enterprise_id, login_time")
@Index(name = "idx_enterprise_code_time", columnList = "enterprise_code, login_time")
```

### 7. SinaTokenCache.java
**修复前：**
```java
@Index(name = "idx_app_id", columnList = "appId")
@Index(name = "idx_expire_time", columnList = "expireTime")
@Index(name = "idx_created_date", columnList = "createdDate")
@Index(name = "idx_is_active", columnList = "isActive")
```

**修复后：**
```java
@Index(name = "idx_app_id", columnList = "app_id")
@Index(name = "idx_expire_time", columnList = "expire_time")
@Index(name = "idx_created_date", columnList = "created_date")
@Index(name = "idx_is_active", columnList = "is_active")
```

## 已经正确的实体类

以下实体类的索引配置已经是正确的（使用snake_case）：

1. **SinaNewsOdsEntity.java** - 使用了正确的列名如 `content_id`, `publish_time`
2. **SinaNewsDwdEntity.java** - 使用了正确的列名如 `content_id`, `publish_time`
3. **HolidayEntity.java** - 没有索引配置

## 验证结果

- ✅ 所有修复已完成
- ✅ 编译测试通过
- ✅ 索引列名现在使用正确的数据库列名（snake_case）
- ✅ 与数据库schema保持一致

## 注意事项

1. 索引列名必须与`@Column(name = "...")`中定义的数据库列名完全一致
2. 使用snake_case命名约定，而不是Java的camelCase
3. 复合索引中的列名顺序很重要，应该根据查询模式优化
4. 修复后的索引配置与数据库迁移脚本中的索引定义保持一致
