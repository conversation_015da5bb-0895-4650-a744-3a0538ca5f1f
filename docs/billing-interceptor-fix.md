# 计费拦截器修复文档

## 问题描述

在实现计费功能时，`BillingInterceptor` 无法正确获取用户信息，导致企业标识符获取失败。主要问题包括：

1. **拦截器顺序问题**：`BillingInterceptor` 在 `UserContextInterceptor` 之前执行，导致用户上下文尚未设置
2. **用户获取方法错误**：使用了不存在的 `UserContext.getCurrentUser()` 方法
3. **空指针检查缺失**：`LoginUserContextHolder` 中缺少空指针检查

## 修复方案

### 1. 拦截器顺序修复

**修改文件**: `src/main/java/com/czb/hn/config/WebConfig.java`

```java
@Override
public void addInterceptors(InterceptorRegistry registry) {
    // 1. 首先注册用户上下文拦截器（优先级最高）
    registry.addInterceptor(userContextInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**")
            .order(1); // 设置最高优先级

    // 2. 然后注册计费拦截器（依赖用户上下文）
    registry.addInterceptor(billingInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/onepass/login",
                                "/onepass/logout",
                                "/onepass/login/**",
                                "/onepass/refresh-groups-cache",
                                "/groups/refresh",
                                "/",
                                "/login",
                                "/error",
                                "/share/**")
            .order(2); // 设置较低优先级，确保在用户上下文拦截器之后执行
}
```

**关键改进**：

- 使用 `.order()` 方法明确设置拦截器执行顺序
- `UserContextInterceptor` 优先级为 1（最高）
- `BillingInterceptor` 优先级为 2（较低）

### 2. 用户上下文工具类

**新增文件**: `src/main/java/com/czb/hn/util/UserContext.java`

提供统一的用户信息获取接口：

```java
public class UserContext {
    // 从ThreadLocal获取用户
    public static LoginUser getCurrentUser()

    // 从Session获取用户（回退方案）
    public static LoginUser getCurrentUserFromSession(HttpServletRequest request)

    // 获取用户（ThreadLocal优先，Session回退）
    public static LoginUser getCurrentUserWithFallback(HttpServletRequest request)

    // 获取企业ID（多种方式）
    public static String getCurrentEnterpriseIdWithFallback(HttpServletRequest request)
}
```

### 3. 空指针检查修复

**修改文件**: `src/main/java/com/czb/hn/dto/user/LoginUserContextHolder.java`

```java
public static LoginUser getUser() {
    LoginUserContext userContext = getContext();
    if (userContext != null) {
        return userContext.getUser();
    }
    return null;
}

public static String getUserId() {
    LoginUserContext userContext = getContext();
    if (userContext != null && userContext.getUser() != null) {
        return userContext.getUser().getUserId();
    }
    return null;
}
```

### 4. 计费拦截器增强

**修改文件**: `src/main/java/com/czb/hn/interceptor/BillingInterceptor.java`

```java
private String getCurrentEnterpriseIdentifier(HttpServletRequest request) {
    // 1. 优先从用户上下文获取（ThreadLocal + Session回退）
    try {
        String enterpriseId = UserContext.getCurrentEnterpriseIdWithFallback(request);
        if (enterpriseId != null && !enterpriseId.trim().isEmpty()) {
            return enterpriseId.trim();
        }
    } catch (Exception e) {
        log.debug("Could not get enterprise ID from user context: {}", e.getMessage());
    }

    // 2. 从请求参数获取
    String enterpriseId = request.getParameter("enterpriseId");
    if (enterpriseId != null && !enterpriseId.trim().isEmpty()) {
        return enterpriseId.trim();
    }

    // 3. 从请求头获取
    String headerEnterpriseId = request.getHeader("X-Enterprise-Id");
    if (headerEnterpriseId != null && !headerEnterpriseId.trim().isEmpty()) {
        return headerEnterpriseId.trim();
    }

    // 4. 从企业信用代码参数获取
    String creditCode = request.getParameter("enterpriseCreditCode");
    if (creditCode != null && !creditCode.trim().isEmpty()) {
        return creditCode.trim();
    }

    // 5. 从企业信用代码请求头获取
    String headerCreditCode = request.getHeader("X-Enterprise-Credit-Code");
    if (headerCreditCode != null && !headerCreditCode.trim().isEmpty()) {
        return headerCreditCode.trim();
    }

    return null;
}
```

## 执行流程

### 正常流程

1. **用户登录** → `UserContextInterceptor` 设置用户上下文到 ThreadLocal
2. **API 请求** → `BillingInterceptor` 从 ThreadLocal 获取企业 ID
3. **权限检查** → 调用 `BillingService.hasAccess(enterpriseId)`
4. **访问控制** → 根据检查结果允许或拒绝访问

### 回退流程

1. **ThreadLocal 无用户** → 尝试从 Session 获取用户信息
2. **Session 无用户** → 尝试从请求参数获取企业 ID
3. **参数无企业 ID** → 尝试从请求头获取企业 ID
4. **所有方式失败** → 返回错误响应

## 测试验证

### 单元测试

- `BillingInterceptorTest`: 测试拦截器的各种场景
- `UserContextTest`: 测试用户上下文工具类

### 测试场景

1. ✅ 用户在 ThreadLocal 中，订阅有效 → 允许访问
2. ✅ 用户在 ThreadLocal 中，订阅过期 → 拒绝访问
3. ✅ 用户不在 ThreadLocal 但在 Session 中 → 允许访问
4. ✅ 用户不在任何地方，但请求参数有企业 ID → 允许访问
5. ✅ 用户不在任何地方，请求头有企业 ID → 允许访问
6. ✅ 完全无法获取企业标识符 → 拒绝访问

## 配置说明

### 拦截器路径配置

```java
// 计费拦截器应用路径
.addPathPatterns("/api/**")

// 排除路径（不需要计费检查）
.excludePathPatterns(
    "/api/onepass/**",      // 认证相关
    "/api/billing/**",      // 计费管理
    "/api/share/**",        // 分享链接
    "/api/health/**",       // 健康检查
    "/api/actuator/**"      // 监控端点
)
```

### 企业标识符获取优先级

1. **用户上下文** (最高优先级)

   - ThreadLocal 中的 `LoginUser.primaryGroupId`
   - Session 中的用户信息回退

2. **请求参数**

   - `enterpriseId` 参数
   - `enterpriseCreditCode` 参数

3. **请求头**
   - `X-Enterprise-Id` 头
   - `X-Enterprise-Credit-Code` 头

## 注意事项

1. **拦截器顺序至关重要**：必须确保 `UserContextInterceptor` 在 `BillingInterceptor` 之前执行
2. **ThreadLocal 清理**：确保在请求结束后清理 ThreadLocal，防止内存泄漏
3. **异常处理**：所有用户信息获取都有异常处理，避免因获取失败导致系统崩溃
4. **日志记录**：详细的日志记录有助于问题排查和监控

## 性能考虑

1. **缓存机制**：`BillingService.hasAccess()` 使用缓存，减少数据库查询
2. **快速失败**：优先使用 ThreadLocal，避免不必要的 Session 访问
3. **最小化查询**：只在必要时进行权限检查，排除不需要检查的路径
