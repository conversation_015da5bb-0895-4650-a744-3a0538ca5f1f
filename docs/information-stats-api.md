# 信息统计总量 API 文档

## 概述

信息统计总量功能提供舆情信息、敏感信息和预警信息的统计数据，支持指定方案或企业下所有方案的查询。

## API 接口

### 获取信息统计总量

**接口地址**: `GET /workbench/information-stats`

**功能描述**: 统计舆情信息总量、舆情敏感信息总量、预警信息总量

**请求参数**:

| 参数名    | 类型   | 必填 | 说明                              | 示例                |
| --------- | ------ | ---- | --------------------------------- | ------------------- |
| planId    | Long   | 否   | 方案 ID，不传则统计企业下所有方案 | 1                   |
| startTime | String | 是   | 开始时间                          | 2024-01-15 10:00:00 |
| endTime   | String | 是   | 结束时间                          | 2024-01-15 18:00:00 |

**请求示例**:

```bash
# 统计指定方案
GET /workbench/information-stats?planId=1&startTime=2024-01-15 10:00:00&endTime=2024-01-15 18:00:00

# 统计企业下所有方案
GET /workbench/information-stats?startTime=2024-01-15 10:00:00&endTime=2024-01-15 18:00:00
```

**响应格式**:

```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "startTime": "2024-01-15 10:00:00",
    "endTime": "2024-01-15 18:00:00",
    "planId": 1,
    "planName": "华能资本舆情监控方案",
    "totalInformationCount": 1250,
    "sensitiveInformationCount": 85,
    "alertCount": 12
  }
}
```

**响应字段说明**:

| 字段名                    | 类型   | 说明                                   |
| ------------------------- | ------ | -------------------------------------- |
| startTime                 | String | 统计开始时间                           |
| endTime                   | String | 统计结束时间                           |
| planId                    | Long   | 方案 ID（为空时表示企业下所有方案）    |
| planName                  | String | 方案名称（为空时显示"企业下所有方案"） |
| totalInformationCount     | Long   | 舆情信息总量                           |
| sensitiveInformationCount | Long   | 舆情敏感信息总量                       |
| alertCount                | Long   | 预警信息总量                           |

## 使用场景

### 1. 指定方案统计

用于查看特定监控方案在指定时间段内的信息统计：

```bash
GET /workbench/information-stats?planId=1&startTime=2024-01-15 00:00:00&endTime=2024-01-15 23:59:59
```

### 2. 企业全量统计

用于查看企业下所有方案在指定时间段内的汇总统计：

```bash
GET /workbench/information-stats?startTime=2024-01-15 00:00:00&endTime=2024-01-15 23:59:59
```

### 3. 自定义时间范围

支持任意时间范围的统计查询：

```bash
# 查询最近7天
GET /workbench/information-stats?startTime=2024-01-08 00:00:00&endTime=2024-01-15 23:59:59

# 查询本月
GET /workbench/information-stats?startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59
```

## 错误处理

### 常见错误码

| 错误码 | 说明           | 解决方案                   |
| ------ | -------------- | -------------------------- |
| 400    | 参数错误       | 检查请求参数格式和必填项   |
| 401    | 用户未登录     | 确保用户已登录并有有效会话 |
| 500    | 服务器内部错误 | 联系系统管理员             |

### 错误响应示例

```json
{
  "result": "ERROR",
  "message": "开始时间不能晚于结束时间",
  "data": null
}
```

## 技术实现

### 数据来源

1. **舆情信息总量**: 从 Elasticsearch 中统计，基于方案关键词匹配
2. **敏感信息总量**: 从 Elasticsearch 中统计，过滤 sensitivityType=1 的数据
3. **预警信息总量**: 从 alert_results 表中统计

### 性能优化

#### 批量查询优化

- **Elasticsearch 批量统计**: 使用并行流处理多个方案的 ES 查询
- **数据库批量查询**: 使用 GROUP BY 一次性查询多个方案的预警统计
- **降级处理**: 批量查询失败时自动降级到单个查询，确保服务稳定性

#### 性能表现

| 方案数量    | 优化前查询次数 | 优化后查询次数 | 预期性能提升 |
| ----------- | -------------- | -------------- | ------------ |
| 10 个方案   | 30 次          | 3 次           | 10 倍提升    |
| 100 个方案  | 300 次         | 3 次           | 100 倍提升   |
| 1000 个方案 | 3000 次        | 3 次           | 1000 倍提升  |

#### 优化策略

- **并行处理**: ES 查询使用 parallelStream 并行执行
- **批量聚合**: 数据库查询使用 GROUP BY 减少查询次数
- **异常隔离**: 单个方案查询失败不影响整体统计
- **内存优化**: 使用流式处理避免大量数据加载到内存

### 权限控制

- 基于用户企业 ID 进行数据隔离
- 只能查询当前用户所属企业的数据
- 支持方案级别的权限控制

## 测试用例

项目包含完整的单元测试和集成测试：

- `InformationStatsServiceTest` - 服务层测试
- `WorkBenchControllerInformationStatsTest` - 控制器测试

运行测试：

```bash
mvn test -Dtest=InformationStatsServiceTest
mvn test -Dtest=WorkBenchControllerInformationStatsTest
```

## 注意事项

1. 时间格式必须为 `yyyy-MM-dd HH:mm:ss`
2. 开始时间不能晚于结束时间
3. 企业统计会汇总所有方案的数据
4. 统计结果实时计算，可能存在轻微延迟
5. 大时间范围查询可能影响性能，建议合理设置时间范围
