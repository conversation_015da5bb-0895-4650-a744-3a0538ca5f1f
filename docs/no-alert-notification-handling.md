# 无预警通知处理方案

## 问题解决

您提出的问题已经得到妥善解决：**无预警通知在数据库中确实关联不上alertId**，这是正确的设计。

## 核心设计

### 1. 数据库设计调整

#### AlertNotificationQueue表
```sql
CREATE TABLE alert_notification_queue (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    alert_id BIGINT NULL,  -- 关键：允许NULL值
    configuration_id BIGINT NOT NULL,
    notification_type ENUM('ALERT', 'INFO_PUSH', 'NO_ALERT') NOT NULL,
    -- 其他字段...
);
```

#### 约束条件
```sql
-- 无预警通知时alert_id必须为NULL
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_no_alert_consistency 
CHECK (
    (notification_type = 'NO_ALERT' AND alert_id IS NULL) OR 
    (notification_type != 'NO_ALERT')
);

-- 预警通知时alert_id不能为NULL
ALTER TABLE alert_notification_queue 
ADD CONSTRAINT chk_alert_consistency 
CHECK (
    (notification_type = 'ALERT' AND alert_id IS NOT NULL) OR 
    (notification_type != 'ALERT')
);
```

### 2. 实体类调整

#### AlertNotificationQueue实体
```java
public class AlertNotificationQueue {
    @Column(name = "alert_id")
    private Long alertId; // 允许NULL
    
    // 便利方法
    public boolean isNoAlertNotification() {
        return alertId == null && notificationType == NotificationType.NO_ALERT;
    }
    
    public boolean isAlertBasedNotification() {
        return alertId != null;
    }
}
```

### 3. 业务逻辑处理

#### 无预警通知的触发条件
1. **配置启用**：`receptionSettings.noAlertNotification() == true`
2. **时间窗口内无预警**：在指定间隔内没有产生预警
3. **未重复发送**：避免在同一时间段重复发送
4. **符合接收规则**：满足时间段、接收时间等条件

#### 调度逻辑
```java
public void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration) {
    // 1. 检查是否启用无预警通知
    if (!configuration.receptionSettings().noAlertNotification()) {
        return;
    }
    
    // 2. 检查时间窗口内是否有预警
    LocalDateTime intervalStart = LocalDateTime.now()
        .minusMinutes(configuration.receptionSettings().alertInterval());
    
    boolean hasRecentAlerts = alertResultRepository
        .existsByConfigurationIdAndWarningTimeBetween(
            configuration.id(), intervalStart, LocalDateTime.now());
    
    if (hasRecentAlerts) {
        return; // 有预警，不需要发送无预警通知
    }
    
    // 3. 检查是否已经调度过
    if (notificationQueueRepository.existsNoAlertNotificationInTimeRange(
        configuration.id(), intervalStart, LocalDateTime.now().plusHours(1))) {
        return; // 已调度，避免重复
    }
    
    // 4. 创建无预警通知
    AlertNotificationQueue notification = AlertNotificationQueue.builder()
        .alertId(null) // 关键：NULL值
        .configurationId(configuration.id())
        .notificationType(NotificationType.NO_ALERT)
        // 其他字段...
        .build();
}
```

### 4. 通知内容处理

#### 无预警通知的内容
```java
private void processNoAlertNotification(AlertNotificationQueue notification, 
                                      List<RecipientInfo> recipients) {
    
    // 创建虚拟预警对象用于通知
    AlertResult dummyAlert = AlertResult.builder()
        .id(null) // 无实际预警ID
        .title("无预警通知")
        .content("在指定时间段内未检测到预警信息")
        .warningTime(notification.getScheduledTime())
        .build();
    
    // 发送通知
    for (RecipientInfo recipient : recipients) {
        if (recipient.emailEnabled()) {
            alertPushService.sendEmailNotification(
                recipient.email(),
                "无预警通知",
                "在指定时间段内未检测到预警信息，系统运行正常。"
            );
        }
        // SMS和系统通知...
    }
}
```

### 5. 查询和统计

#### Repository方法
```java
// 查询无预警通知
@Query("SELECT anq FROM AlertNotificationQueue anq WHERE anq.alertId IS NULL")
List<AlertNotificationQueue> findNoAlertNotifications();

// 检查时间范围内是否存在无预警通知
@Query("SELECT COUNT(anq) > 0 FROM AlertNotificationQueue anq WHERE " +
       "anq.alertId IS NULL AND anq.configurationId = :configurationId AND " +
       "anq.notificationType = 'NO_ALERT' AND anq.scheduledTime BETWEEN :startTime AND :endTime")
boolean existsNoAlertNotificationInTimeRange(
    @Param("configurationId") Long configurationId,
    @Param("startTime") LocalDateTime startTime,
    @Param("endTime") LocalDateTime endTime);
```

#### 统计视图
```sql
CREATE VIEW v_alert_notification_statistics AS
SELECT 
    enterprise_id,
    notification_type,
    status,
    COUNT(*) as notification_count,
    COUNT(CASE WHEN alert_id IS NULL THEN 1 END) as no_alert_count,
    COUNT(CASE WHEN alert_id IS NOT NULL THEN 1 END) as alert_based_count
FROM alert_notification_queue
GROUP BY enterprise_id, notification_type, status;
```

## 优势

### 1. 数据完整性
- 通过数据库约束确保数据一致性
- NULL值明确表示无预警通知的特殊性

### 2. 业务逻辑清晰
- 无预警通知有独立的处理流程
- 避免与实际预警通知混淆

### 3. 查询效率
- 专门的索引和查询方法
- 清晰的数据区分便于统计分析

### 4. 扩展性
- 可以轻松添加其他类型的非预警通知
- 支持复杂的通知规则和条件

## 使用示例

### 配置无预警通知
```json
{
  "receptionSettings": {
    "noAlertNotification": true,
    "alertInterval": 60,
    "receptionTime": "DAILY",
    "receptionPeriod": {
      "start": "09:00",
      "end": "18:00"
    }
  }
}
```

### 查询无预警通知
```java
// 获取所有无预警通知
List<AlertNotificationQueue> noAlertNotifications = 
    notificationQueueRepository.findNoAlertNotifications();

// 检查特定配置的无预警通知
boolean hasNoAlertNotification = 
    notificationQueueRepository.existsNoAlertNotificationInTimeRange(
        configId, startTime, endTime);
```

## 总结

通过将`alert_id`设计为可NULL字段，我们优雅地解决了无预警通知的数据关联问题：

1. **数据模型**：NULL值明确表示无预警通知
2. **业务逻辑**：独立的处理流程和触发条件
3. **数据完整性**：数据库约束确保一致性
4. **查询效率**：专门的查询方法和索引

这个设计既保持了数据模型的简洁性，又满足了业务需求的复杂性。
