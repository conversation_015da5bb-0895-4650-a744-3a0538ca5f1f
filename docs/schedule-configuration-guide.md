# 定时任务配置指南

## 概述

本系统支持分环境配置定时任务，可以根据不同环境（开发、测试、生产）灵活控制数据拉取定时任务的启用状态和执行频率。

## 功能特性

- ✅ **分环境配置** - 支持开发、测试、生产环境的不同配置
- ✅ **动态开关控制** - 支持运行时动态启用/禁用定时任务
- ✅ **批处理大小配置** - 可根据环境调整批处理大小
- ✅ **重试机制配置** - 支持配置重试次数和间隔
- ✅ **环境标识** - 每个任务都有明确的环境标识
- ✅ **管理API** - 提供REST API进行任务状态查询和控制

## 配置结构

### 主配置文件 (application.yml)

```yaml
schedule:
  collector:                        # 数据收集器配置
    enabled: true                   # 是否启用
    cron: "0 */10 * * * *"         # 执行频率
    environment: "production"       # 环境标识
    batch-size: 500                # 批处理大小
    max-retries: 3                 # 最大重试次数
    retry-interval: 5000           # 重试间隔（毫秒）
  cleaner:                         # 数据清洗器配置
    enabled: true
    cron: "0 */15 * * * *"
    environment: "production"
    batch-size: 100
  aggregator:                      # 数据聚合器配置
    enabled: true
    cron: "0 0 1 * * *"
    environment: "production"
  elasticsearch-sync:              # ES同步配置
    enabled: true
    cron: "0 */5 * * * *"
    environment: "production"
    batch-size: 100
```

### 环境特定配置

#### 开发环境 (application-dev.yml)
- 数据收集：30分钟执行一次，批处理大小100
- 数据清洗：45分钟执行一次，批处理大小50
- 数据聚合：禁用
- ES同步：禁用

#### 测试环境 (application-test.yml)
- 所有定时任务：禁用
- 使用内存数据库
- 减少日志输出

#### 生产环境 (application-prod.yml)
- 数据收集：5分钟执行一次，批处理大小1000
- 数据清洗：10分钟执行一次，批处理大小200
- 数据聚合：每天凌晨1点执行
- ES同步：3分钟执行一次，批处理大小200

## 使用方法

### 1. 启动时指定环境

```bash
# 开发环境
java -jar securadar.jar --spring.profiles.active=dev

# 测试环境
java -jar securadar.jar --spring.profiles.active=test

# 生产环境
java -jar securadar.jar --spring.profiles.active=prod
```

### 2. 环境变量配置

```bash
export SPRING_PROFILES_ACTIVE=prod
java -jar securadar.jar
```

### 3. Docker环境配置

```dockerfile
ENV SPRING_PROFILES_ACTIVE=prod
```

## 管理API

### 查询定时任务状态

```bash
GET /api/admin/schedule/status
```

响应示例：
```json
{
  "result": "SUCCESS",
  "message": "获取定时任务状态成功",
  "data": {
    "collector": {
      "enabled": true,
      "cron": "0 */10 * * * *",
      "environment": "production",
      "batchSize": 500,
      "maxRetries": 3,
      "retryInterval": 5000
    },
    "cleaner": {
      "enabled": true,
      "cron": "0 */15 * * * *",
      "environment": "production",
      "batchSize": 100
    }
  }
}
```

### 动态控制定时任务

#### 启用/禁用数据收集器
```bash
POST /api/admin/schedule/collector/toggle?enabled=true
POST /api/admin/schedule/collector/toggle?enabled=false
```

#### 启用/禁用数据清洗器
```bash
POST /api/admin/schedule/cleaner/toggle?enabled=true
POST /api/admin/schedule/cleaner/toggle?enabled=false
```

#### 启用/禁用数据聚合器
```bash
POST /api/admin/schedule/aggregator/toggle?enabled=true
POST /api/admin/schedule/aggregator/toggle?enabled=false
```

#### 启用/禁用ES同步
```bash
POST /api/admin/schedule/elasticsearch-sync/toggle?enabled=true
POST /api/admin/schedule/elasticsearch-sync/toggle?enabled=false
```

## 实现原理

### 1. 配置类
- `ScheduleConfig` - 主配置类，使用`@ConfigurationProperties`自动绑定配置
- 支持嵌套配置结构，每个任务类型有独立的配置

### 2. 动态Cron表达式
```java
@Scheduled(cron = "#{@scheduleConfig.collector.enabled ? @scheduleConfig.collector.cron : '-'}")
```
- 使用SpEL表达式动态计算cron值
- 当enabled为false时，cron设为'-'，禁用定时任务

### 3. 运行时检查
```java
if (!scheduleConfig.getCollector().isEnabled()) {
    logger.debug("Data collector scheduled task is disabled, skipping execution");
    return CompletableFuture.completedFuture(null);
}
```
- 在任务执行前检查enabled状态
- 提供双重保护机制

## 监控和日志

### 日志输出示例
```
2024-06-30 10:00:00 [scheduling-1] INFO  SinaNewsCollectorServiceImpl - Starting scheduled data fetch from Sina API (Environment: production)
2024-06-30 10:00:05 [scheduling-1] INFO  SinaNewsCollectorServiceImpl - Scheduled fetch completed. Environment: production, Fetched: 150, Saved: 148
```

### 环境标识
- 每个任务执行时都会记录环境标识
- 便于在多环境部署时区分日志来源
- 支持基于环境的不同处理逻辑

## 最佳实践

1. **开发环境**：降低执行频率，减少资源消耗
2. **测试环境**：完全禁用定时任务，避免干扰测试
3. **生产环境**：根据业务需求优化执行频率和批处理大小
4. **监控**：定期检查定时任务状态和执行日志
5. **故障处理**：利用动态开关快速禁用有问题的任务

## 故障排除

### 常见问题

1. **定时任务不执行**
   - 检查`enabled`配置是否为true
   - 检查cron表达式是否正确
   - 查看应用日志中的调度信息

2. **配置不生效**
   - 确认使用了正确的profile
   - 检查配置文件语法是否正确
   - 重启应用使配置生效

3. **性能问题**
   - 调整批处理大小
   - 优化执行频率
   - 监控系统资源使用情况
