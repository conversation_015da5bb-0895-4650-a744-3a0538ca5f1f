# 配置同步时效性优化方案

## 问题分析

当前配置同步机制存在以下时效性问题：

1. 定时同步最长延迟 5 分钟
2. 配置 CRUD 操作未发布变更事件
3. 缓存刷新是被动的，不是主动推送

## 解决方案

### 方案 1：完善事件驱动机制 + 精确缓存失效（已实现）

**优点**：

- 实现成本低，基于现有架构
- 配置变更后立即生效（毫秒级）
- 精确缓存失效，避免不必要的性能损耗
- 保持系统架构简洁

**实现要点**：

1. 在 AlertConfigurationService 的 CRUD 操作中发布 ConfigurationChangeEvent
2. AlertConfigurationSyncService 监听事件并精确刷新相关缓存
3. 使用@CacheEvict 的精确 key 匹配，避免全量缓存清空
4. 使用@Async 确保事件处理不阻塞主流程

**缓存失效策略**：

- 单个配置变更：只刷新该配置、所属企业、所属方案、全量缓存
- 企业级变更：只刷新该企业和全量缓存
- 降级机制：出错时自动降级到全量刷新

**时效性**：配置变更后 1-2 秒内生效

### 方案 2：Redis 发布订阅（推荐用于分布式环境）

**适用场景**：多实例部署环境

**实现思路**：

```java
// 配置变更时发布Redis消息
@Autowired
private RedisTemplate<String, Object> redisTemplate;

public void publishConfigChange(Long configId, String enterpriseId, String eventType) {
    ConfigChangeMessage message = new ConfigChangeMessage(configId, enterpriseId, eventType);
    redisTemplate.convertAndSend("config-change-channel", message);
}

// 订阅Redis消息并刷新缓存
@RedisListener("config-change-channel")
public void handleConfigChange(ConfigChangeMessage message) {
    alertConfigConsumerService.refreshCache();
}
```

**时效性**：配置变更后 500ms-1s 内生效

### 方案 3：WebSocket 实时推送（用于前端实时更新）

**适用场景**：需要前端界面实时反映配置变更

**实现思路**：

```java
@Component
public class ConfigurationWebSocketHandler {

    @EventListener
    public void handleConfigChange(ConfigurationChangeEvent event) {
        // 推送给所有连接的客户端
        webSocketSessions.forEach(session -> {
            sendMessage(session, event);
        });
    }
}
```

### 方案 4：数据库触发器（不推荐）

**缺点**：

- 增加数据库复杂性
- 难以调试和维护
- 与应用逻辑耦合

## 精确缓存失效详解

### 缓存结构分析

```
activeConfigurations:
├── "all" -> 所有活跃配置列表
├── "enterprise:ENT001" -> 企业ENT001的配置列表
├── "enterprise:ENT002" -> 企业ENT002的配置列表
├── "plan:100" -> 方案100的配置列表
└── "plan:101" -> 方案101的配置列表

singleConfiguration:
├── "123" -> 配置ID为123的单个配置
├── "124" -> 配置ID为124的单个配置
└── ...
```

### 失效策略对比

| 场景             | 原方案（全量刷新）       | 新方案（精确失效）                          |
| ---------------- | ------------------------ | ------------------------------------------- |
| 更新配置 123     | 清空所有缓存             | 只清空: 配置 123、所属企业、所属方案、"all" |
| 企业 ENT001 变更 | 清空所有缓存             | 只清空: "enterprise:ENT001"、"all"          |
| 性能影响         | 所有后续请求都需重新加载 | 只有相关请求需要重新加载                    |

### 性能提升估算

- **缓存命中率提升**: 从变更后 0%提升到 80%+
- **响应时间**: 不相关配置查询仍然是缓存响应（毫秒级）
- **数据库压力**: 减少 90%+的不必要查询

## 性能对比

| 方案              | 时效性   | 实现复杂度 | 资源消耗 | 缓存效率 | 可扩展性 |
| ----------------- | -------- | ---------- | -------- | -------- | -------- |
| 事件驱动+精确失效 | 1-2 秒   | 低         | 低       | 高       | 中       |
| Redis 发布订阅    | 500ms-1s | 中         | 中       | 高       | 高       |
| WebSocket 推送    | 实时     | 高         | 高       | 中       | 中       |
| 数据库触发器      | 实时     | 高         | 低       | 低       | 低       |

## 监控和调试

### 1. 事件发布监控

```java
@EventListener
public void monitorConfigEvents(ConfigurationChangeEvent event) {
    log.info("Config change event: {} for config {} at {}",
        event.getEventType(), event.getConfigurationId(), event.getTimestamp());
}
```

### 2. 缓存刷新监控

```java
public void refreshCache() {
    long startTime = System.currentTimeMillis();
    // 刷新缓存逻辑
    long duration = System.currentTimeMillis() - startTime;
    log.info("Cache refresh completed in {}ms", duration);
}
```

### 3. 配置生效验证

```java
@RestController
public class ConfigSyncTestController {

    @PostMapping("/test/config-sync/{configId}")
    public ResponseEntity<String> testConfigSync(@PathVariable Long configId) {
        // 验证配置是否已生效
        var config = alertConfigConsumerService.getActiveConfigurationById(configId);
        return ResponseEntity.ok("Config sync status: " + (config.isPresent() ? "ACTIVE" : "INACTIVE"));
    }
}
```

## 最佳实践

1. **事件发布失败处理**：不要让事件发布失败影响主业务流程
2. **缓存预热**：应用启动时预加载常用配置
3. **监控告警**：监控缓存刷新频率和耗时
4. **降级策略**：缓存失效时直接查询数据库

## 配置参数

```yaml
# application.yml
alert:
  sync:
    # 是否启用实时同步
    realtime-enabled: true
    # 事件处理超时时间（毫秒）
    event-timeout: 5000
    # 缓存刷新批处理大小
    cache-refresh-batch-size: 100
```
