# Sina Public Opinion API - Enumeration Types Summary

This document catalogs all enumeration types and their possible values from the Sina Public Opinion API documentation, organized for Java enum implementation.

## Table of Contents
1. [Match Types](#match-types)
2. [User Verification Types](#user-verification-types)
3. [Content Sensitivity Types](#content-sensitivity-types)
4. [Content Emotion Types](#content-emotion-types)
5. [Content Originality Types](#content-originality-types)
6. [Content Result View Types](#content-result-view-types)
7. [Source Types (newOriginType)](#source-types-neworigintype)
8. [Media Levels (originLevel)](#media-levels-originlevel)
9. [Content Types (contentTypes)](#content-types-contenttypes)
10. [Detailed Source Classifications (originTypeSecond)](#detailed-source-classifications-origintypesecond)

---

## Match Types

**Field:** `matchInfo.type`  
**Data Type:** String  
**Usage Context:** MatchInfo object within content data  
**Description:** Indicates the type of content matching performed

| Value | Meaning |
|-------|---------|
| `1` | Keyword match |
| `2` | User match |

---

## User Verification Types

**Field:** `userExt.verifiedType`  
**Data Type:** String  
**Usage Context:** UserExt object within content data  
**Description:** Indicates the verification status and type of the user account

| Value | Meaning |
|-------|---------|
| `-1` | 普通 (Regular user) |
| `0` | 橙V (Orange V - Celebrity) |
| `1` | 蓝V (Blue V - Verified organization) |
| `2` | 蓝V (Blue V - Verified organization) |
| `3` | 蓝V (Blue V - Verified organization) |
| `4` | 蓝V (Blue V - Verified organization) |
| `5` | 蓝V (Blue V - Verified organization) |
| `6` | 蓝V (Blue V - Verified organization) |
| `7` | 蓝V (Blue V - Verified organization) |
| `200` | 达人 (Expert/Influencer) |
| `220` | 达人 (Expert/Influencer) |
| `600` | 金V (Gold V - Premium verification) |

---

## Content Sensitivity Types

**Field:** `contentExt.sensitivityType`  
**Data Type:** Integer  
**Usage Context:** ContentExt object within content data  
**Description:** Classification of content sensitivity level

| Value | Meaning |
|-------|---------|
| `1` | 敏感 (Sensitive) |
| `2` | 非敏感 (Non-sensitive) |
| `3` | 中性 (Neutral) |

---

## Content Emotion Types

**Field:** `contentExt.emotion`  
**Data Type:** String  
**Usage Context:** ContentExt object within content data  
**Description:** Six-element emotion classification of content

| Value | Meaning |
|-------|---------|
| `中性` | Neutral |
| `喜悦` | Joy |
| `悲伤` | Sadness |
| `愤怒` | Anger |
| `惊奇` | Surprise |
| `恐惧` | Fear |

---

## Content Originality Types

**Field:** `contentExt.isOriginal`  
**Data Type:** Integer  
**Usage Context:** ContentExt object within content data  
**Description:** Indicates whether the content is original or forwarded

| Value | Meaning |
|-------|---------|
| `1` | 原创 (Original) |
| `2` | 转发 (Forwarded/Reposted) |

---

## Content Result View Types

**Field:** `contentExt.isNormarlData`  
**Data Type:** Integer  
**Usage Context:** ContentExt object within content data  
**Description:** Classification of content quality/validity

| Value | Meaning |
|-------|---------|
| `1` | 正常 (Normal) |
| `2` | 噪音 (Noise/Invalid) |

---

## Source Types (newOriginType)

**Field:** `contentExt.newOriginType`  
**Data Type:** String  
**Usage Context:** ContentExt object within content data  
**Description:** Primary source type classification

| Value | Meaning |
|-------|---------|
| `hdlt` | 互动论坛 (Interactive Forums) |
| `wb` | 微博 (Weibo) |
| `wx` | 微信 (WeChat) |
| `zmtapp` | 客户端 (Mobile Apps) |
| `sp` | 视频 (Video) |
| `szb` | 数字报 (Digital Newspapers) |
| `wz` | 网站 (Websites) |

---

## Media Levels (originLevel)

**Field:** `contentExt.originLevel`  
**Data Type:** String  
**Usage Context:** ContentExt object within content data  
**Description:** Classification of media organization level

| Value | Meaning |
|-------|---------|
| `央级` | National level |
| `省级` | Provincial level |
| `地市` | City/Municipal level |
| `重点` | Key/Important media |
| `中小` | Small to medium media |
| `企业商业` | Corporate/Commercial |

---

## Content Types (contentTypes)

**Field:** `contentExt.contentTypes`  
**Data Type:** String  
**Usage Context:** ContentExt object within content data  
**Description:** Type of content format for Weibo posts

| Value | Meaning |
|-------|---------|
| `1` | 文本 (Text) |
| `2` | 图片 (Image) |
| `3` | 短链 (Short URL) |
| `4` | 视频 (Video) |

---

## Detailed Source Classifications (originTypeSecond)

**Field:** `originTypeSecond`  
**Data Type:** String  
**Usage Context:** Content/rootContent objects  
**Description:** Detailed secondary classification of content sources

### Website Sources (网站)
| Value | Meaning |
|-------|---------|
| `xw` | 新闻网站 (News websites) |
| `zw` | 政务网站 (Government websites) |
| `wzhangye` | 行业网站 (Industry websites) |
| `wzorg` | 机构组织 (Institutional organizations) |
| `wzqy` | 企业官网 (Corporate official websites) |
| `waimei` | 境外网媒 (Foreign web media) |
| `wzqita` | 其他网站 (Other websites) |

### Weibo Sources (微博)
| Value | Meaning |
|-------|---------|
| `wbxw` | 微博媒体号 (Weibo media accounts) |
| `wbzw` | 微博政务号 (Weibo government accounts) |
| `wbschool` | 微博校园号 (Weibo campus accounts) |
| `wbteam` | 微博团体号 (Weibo group accounts) |
| `wbcompany` | 微博企业号 (Weibo corporate accounts) |
| `wbweb` | 微博网站号 (Weibo website accounts) |
| `wbgoldv` | 金V号 (Gold V accounts) |
| `wborangev` | 橙V号 (Orange V accounts) |
| `wbpersonal` | 微博个人号 (Weibo personal accounts) |

### WeChat Sources (微信)
| Value | Meaning |
|-------|---------|
| `wxxw` | 微信媒体号 (WeChat media accounts) |
| `wxzw` | 微信政务号 (WeChat government accounts) |
| `wxorg` | 微信机构号 (WeChat institutional accounts) |
| `wxcompany` | 微信企业号 (WeChat corporate accounts) |
| `wxpersonal` | 微信个人号 (WeChat personal accounts) |

### Interactive Forum Sources (互动论坛)
| Value | Meaning |
|-------|---------|
| `liuyanban` | 留言版 (Message boards) |
| `tousu` | 投诉爆料 (Complaints and reports) |
| `lt` | 论坛 (Forums) |
| `wenda` | 问答 (Q&A platforms) |
| `tieba` | 贴吧 (Tieba forums) |
| `blog` | 博客 (Blogs) |

### Mobile App Sources (客户端)
| Value | Meaning |
|-------|---------|
| `app` | APP (Mobile applications) |
| `zmt` | 自媒体 (Self-media platforms) |

### Digital Newspaper Sources (数字报)
| Value | Meaning |
|-------|---------|
| `szbyj` | 央级数字报 (National digital newspapers) |
| `szbsj` | 省级数字报 (Provincial digital newspapers) |
| `szbdj` | 地市数字报 (Municipal digital newspapers) |
| `szbqita` | 其他数字报 (Other digital newspapers) |

### Video Sources (视频)
| Value | Meaning |
|-------|---------|
| `xsp` | 短视频 (Short videos) |
| `spwz` | 视频网站 (Video websites) |
| `diantai` | 电视视频 (TV videos) |

---

## Implementation Notes

1. **Data Types**: Most enumerations use String values, except for sensitivity types, originality, and result view which use Integer
2. **Null Handling**: Consider implementing proper null/empty value handling for optional fields
3. **Validation**: Implement validation to ensure only valid enumeration values are accepted
4. **Extensibility**: Design enums to be easily extensible as new values may be added by the API provider
5. **Localization**: Chinese values should be preserved as-is for display purposes, with English constants for code readability

## Usage in Java Enums

When implementing these as Java enums, consider:
- Using descriptive English constant names with Chinese descriptions
- Implementing value lookup methods for API response parsing
- Adding validation methods for input validation
- Providing display methods that return localized strings
