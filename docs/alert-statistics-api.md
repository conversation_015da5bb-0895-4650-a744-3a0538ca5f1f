# 预警统计 API 文档

## 概述

本文档描述了预警统计功能的 API 接口，支持获取指定时间段内指定方案的预警总条数和敏感信息类型预警条数。

## 功能特性

- ✅ 支持指定方案 ID 获取统计信息
- ✅ 支持时间范围过滤（可选）
- ✅ 返回明确的方案名称
- ✅ 返回预警总条数
- ✅ 返回敏感信息类型预警条数
- ✅ 空字符串检索条件自动转换为"ALL"

## API 接口

### 1. 获取预警统计信息

#### 接口地址

```
GET /alert-results/statistics
```

#### 请求参数

| 参数名    | 类型   | 必填 | 说明     | 示例                |
| --------- | ------ | ---- | -------- | ------------------- |
| planId    | Long   | 是   | 方案 ID  | 1                   |
| startTime | String | 否   | 开始时间 | 2024-01-15 10:30:00 |
| endTime   | String | 否   | 结束时间 | 2024-01-15 18:30:00 |

#### 请求示例

```bash
GET /alert-results/statistics?planId=1&startTime=2024-01-15 10:30:00&endTime=2024-01-15 18:30:00
```

#### 响应格式

```json
{
  "result": "SUCCESS",
  "message": "统计成功",
  "data": {
    "planId": 1,
    "planName": "华能资本舆情监控方案",
    "startTime": "2024-01-15 10:30:00",
    "endTime": "2024-01-15 18:30:00",
    "totalAlertCount": 156,
    "sensitiveAlertCount": 42
  }
}
```

#### 响应字段说明

| 字段名              | 类型   | 说明                 |
| ------------------- | ------ | -------------------- |
| planId              | Long   | 方案 ID              |
| planName            | String | 方案名称             |
| startTime           | String | 统计开始时间         |
| endTime             | String | 统计结束时间         |
| totalAlertCount     | Long   | 预警总条数           |
| sensitiveAlertCount | Long   | 敏感信息类型预警条数 |

### 2. 分享版本统计接口

#### 接口地址

```
GET /share/alert-results/statistics
```

#### 请求参数

| 参数名    | 类型   | 必填 | 说明     | 示例                |
| --------- | ------ | ---- | -------- | ------------------- |
| planId    | Long   | 是   | 方案 ID  | 1                   |
| startTime | String | 是   | 开始时间 | 2024-01-15 10:30:00 |
| endTime   | String | 是   | 结束时间 | 2024-01-15 18:30:00 |

## 搜索条件优化

### 空字符串处理

在搜索接口中，以下字段如果传入空字符串，会自动转换为"ALL"（表示不进行该维度的过滤）：

- `informationSensitivityType` - 信息敏感性类型
- `contentCategory` - 内容类别
- `warningLevel` - 预警级别
- `source` - 来源
- `searchText` - 搜索文本（空字符串转换为 null）

#### 示例

```json
{
  "planId": 1,
  "informationSensitivityType": "", // 自动转换为 "ALL"
  "contentCategory": "", // 自动转换为 "ALL"
  "warningLevel": "", // 自动转换为 "ALL"
  "source": "", // 自动转换为 "ALL"
  "searchText": "", // 自动转换为 null
  "startTime": "2024-01-15 10:30:00",
  "endTime": "2024-01-15 18:30:00",
  "page": 0,
  "size": 20
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明           | 示例响应                                                                                |
| ------ | -------------- | --------------------------------------------------------------------------------------- |
| 400    | 请求参数错误   | `{"result": "ERROR", "message": "参数无效: 开始时间不能晚于结束时间", "data": null}`    |
| 404    | 方案不存在     | `{"result": "ERROR", "message": "参数无效: Plan not found with ID: 999", "data": null}` |
| 500    | 服务器内部错误 | `{"result": "ERROR", "message": "统计失败: 数据库连接异常", "data": null}`              |

## 使用场景

### 1. 获取全时间段统计

```bash
GET /alert-results/statistics?planId=1
```

### 2. 获取指定时间段统计

```bash
GET /alert-results/statistics?planId=1&startTime=2024-01-15 00:00:00&endTime=2024-01-15 23:59:59
```

### 3. 获取某天的统计

```bash
GET /alert-results/statistics?planId=1&startTime=2024-01-15 00:00:00&endTime=2024-01-15 23:59:59
```

## 技术实现

### 数据库查询优化

- 使用索引优化的查询：`idx_plan_warning_time`
- 敏感信息过滤：`information_sensitivity_type = 'SENSITIVE'`
- 时间范围过滤：`warning_time BETWEEN ? AND ?`

### 性能考虑

- 统计查询使用 COUNT 聚合函数，性能较好
- 支持时间范围索引，大数据量下查询效率高
- 方案验证通过缓存优化（如果启用）

## 测试用例

项目包含完整的单元测试：

- `AlertSearchServiceStatisticsTest` - 统计功能测试
- `AlertSearchCriteriaDtoTest` - 搜索条件处理测试

运行测试：

```bash
mvn test -Dtest=AlertSearchServiceStatisticsTest
mvn test -Dtest=AlertSearchCriteriaDtoTest
```
