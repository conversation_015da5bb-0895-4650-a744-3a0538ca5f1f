# 本地测试使用示例

## 快速开始

### 1. 启动本地测试环境

```bash
# 进入项目目录
cd /path/to/SecuRadar

# 启动本地测试模式
./scripts/start-local-test.sh
```

启动后会看到类似输出：
```
===================================
启动本地数据采集测试环境
===================================
测试数据文件: docs/yuqing.txt
数据行数: 140

启动应用 (使用local profile)...
本地文件模式已启用

测试接口:
  - 数据获取: GET  http://localhost:8080/api/test/collector/fetch
  - 保存测试: POST http://localhost:8080/api/test/collector/save-to-ods
  - 最新偏移: GET  http://localhost:8080/api/test/collector/latest-offset
```

### 2. 测试数据获取

```bash
# 获取前5条数据
curl "http://localhost:8080/api/test/collector/fetch?offset=1&limit=5"
```

响应示例：
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "data": {
        "contentId": "11751423729514673230574228",
        "author": "雅致",
        "title": "转发了",
        "publishTime": "2025-07-02 10:34:58",
        "matchInfo": {
          "info": "永诚财产保险股份有限公司",
          "ticket": "jnwkeyword2025070113kxoc9k268z3z"
        }
      },
      "offset": 1
    }
  ]
}
```

### 3. 测试保存到数据库

```bash
# 保存前3条数据到ODS
curl -X POST "http://localhost:8080/api/test/collector/save-to-ods?offset=1&limit=3"
```

响应示例：
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": "成功获取 3 条数据，保存 3 条到ODS"
}
```

### 4. 检查最新偏移量

```bash
# 获取当前最新偏移量
curl "http://localhost:8080/api/test/collector/latest-offset"
```

响应示例：
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": 4
}
```

### 5. 分页测试

```bash
# 第一页 (1-5)
curl "http://localhost:8080/api/test/collector/fetch?offset=1&limit=5"

# 第二页 (6-10)
curl "http://localhost:8080/api/test/collector/fetch?offset=6&limit=5"

# 第三页 (11-15)
curl "http://localhost:8080/api/test/collector/fetch?offset=11&limit=5"
```

## 自动化测试

运行完整的测试套件：

```bash
./scripts/test-local-collector.sh
```

测试输出示例：
```
===================================
本地数据采集功能测试
===================================
1. 检查服务状态...
✓ 服务正常运行

2. 测试获取最新偏移量...
响应: {"result":"SUCCESS","message":"操作成功","data":1}

3. 测试数据获取 (第1-3条)...
✓ 数据获取成功
  获取到 3 条数据

4. 测试数据获取 (第4-6条)...
✓ 分页数据获取成功
  获取到 3 条数据

5. 测试保存到ODS...
✓ 保存到ODS成功

6. 检查保存后的偏移量...
响应: {"result":"SUCCESS","message":"操作成功","data":3}

7. 测试超出范围的偏移量...
✓ 超出范围处理正确

===================================
测试完成
===================================
```

## 开发调试

### 查看详细日志

本地模式启用了详细日志，可以看到：

```
2025-07-03 10:30:00 [main] DEBUG com.czb.hn.service.collector - Reading test data from local file: docs/yuqing.txt, offset: 1, limit: 5
2025-07-03 10:30:00 [main] INFO  com.czb.hn.service.collector - Loaded 5 records from local file (requested offset: 1, limit: 5)
2025-07-03 10:30:00 [main] INFO  com.czb.hn.service.collector - Completed saving to ODS. Total: 5, Saved: 5, Skipped: 0, Errors: 0
```

### 修改测试数据

可以编辑 `docs/yuqing.txt` 文件来添加或修改测试数据：

1. 每行一个JSON对象
2. 确保JSON格式正确
3. 重启应用后生效

### 切换回远程模式

修改配置或使用不同的profile：

```yaml
sina:
  api:
    use:
      local:
        file: false  # 禁用本地文件模式
```

或者使用默认profile启动：
```bash
mvn spring-boot:run
```

## 常见问题

### Q: 接口返回404
A: 确保启用了本地文件模式：`sina.api.use.local.file: true`

### Q: 数据为空
A: 检查 `docs/yuqing.txt` 文件是否存在且格式正确

### Q: JSON解析错误
A: 检查测试数据文件中的JSON格式，确保每行都是有效的JSON对象
