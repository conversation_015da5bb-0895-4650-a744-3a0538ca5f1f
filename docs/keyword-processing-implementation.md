# 舆情方案关键词处理实现文档

## 概述

本文档描述了为 SecuRadar 舆情监控系统实现的关键词处理功能。该功能解决了方案(Plan)实体中关键词存储与搜索服务之间的格式转换问题，并提供了完整的关键词验证、清理和处理能力。

**重要更新：现已支持逻辑运算符**

- `+` 表示"并且"(AND)逻辑
- `|` 表示"或"(OR)逻辑
- `@@` 用于转义关键词中的"+"字符
- 排除关键词使用`|`分割，逻辑关系为 OR

## 问题分析

### 原有问题

1. **格式不匹配**: Plan 实体将关键词存储为 String，但搜索服务需要 List<String>
2. **缺乏验证**: 创建/更新方案时没有关键词格式验证
3. **无标准化处理**: 关键词没有统一的清理和标准化流程
4. **转换缺失**: Plan 关键词与搜索服务参数之间缺乏转换层

### 解决方案

实现了完整的关键词处理体系，包括：

- 关键词工具类(KeywordUtils)
- DTO 层验证增强
- 服务层处理方法
- 搜索服务集成
- 控制器 API 扩展

## 实现详情

### 1. KeywordUtils 工具类

**位置**: `src/main/java/com/czb/hn/util/KeywordUtils.java`

**核心功能**:

**逻辑运算符功能**:

- `parseMonitorKeywords(String)`: 解析监控关键词（支持 AND 和 OR 逻辑）
- `parseExcludeKeywords(String)`: 解析排除关键词（OR 逻辑）
- `parseKeywordsWithLogic(String, String)`: 完整解析监控和排除关键词
- `unescapeKeyword(String)`: 处理转义字符，将@@替换为+
- `validateMonitorKeywords(String)`: 验证监控关键词格式（支持逻辑运算符）
- `validateExcludeKeywords(String)`: 验证排除关键词格式
- `getMonitorKeywordsValidationErrorMessage(String)`: 获取监控关键词验证错误信息
- `getExcludeKeywordsValidationErrorMessage(String)`: 获取排除关键词验证错误信息

**验证规则**:

- 关键词长度: 1-50 个字符
- 最大关键词数量: 100 个
- 允许字符: 中文、英文、数字、空格、连字符、下划线
- 自动去重和去除空白

### 2. DTO 层增强

**修改文件**:

- `src/main/java/com/czb/hn/dto/PlanCreateDTO.java`
- `src/main/java/com/czb/hn/dto/PlanUpdateDTO.java`

**增强内容**:

- 在构造函数中添加关键词格式验证
- 使用 KeywordUtils 进行验证
- 提供详细的错误信息

### 3. 服务层扩展

**修改文件**:

- `src/main/java/com/czb/hn/service/business/PlanService.java`
- `src/main/java/com/czb/hn/service/business/impl/PlanServiceImpl.java`

**新增方法**:

```java
List<String> getMonitorKeywordsList(Long planId)
List<String> getExcludeKeywordsList(Long planId)
KeywordUtils.MonitorKeywordGroup getMonitorKeywordsWithLogic(Long planId)
KeywordUtils.ExcludeKeywordGroup getExcludeKeywordsWithLogic(Long planId)
KeywordUtils.KeywordParseResult getPlanKeywordsWithLogic(Long planId)
```

**增强功能**:

- 创建/更新方案时自动清理关键词
- 提供关键词列表获取方法
- 空值处理优化

### 4. 搜索服务集成

**新增文件**: `src/main/java/com/czb/hn/service/business/PlanBasedSearchService.java`

**核心功能**:

- `searchByPlan()`: 根据方案 ID 执行搜索
- `searchByPlanWithAdditionalKeywords()`: 方案关键词与自定义关键词合并搜索
- `validatePlanKeywords()`: 验证方案关键词
- `getPlanKeywordInfo()`: 获取方案关键词完整信息

### 5. 控制器 API 扩展

**修改文件**: `src/main/java/com/czb/hn/web/controllers/PlanController.java`

**新增 API 端点**:

- `GET /plans/{id}/keywords/monitor`: 获取监控关键词列表
- `GET /plans/{id}/keywords/exclude`: 获取排除关键词列表
- `GET /plans/{id}/keywords/info`: 获取完整关键词信息
- `GET /plans/{id}/keywords/logic`: 获取逻辑关键词解析结果
- `GET /plans/{id}/keywords/monitor/logic`: 获取监控关键词逻辑结构
- `GET /plans/{id}/keywords/exclude/logic`: 获取排除关键词逻辑结构

## 测试覆盖

### 1. 单元测试

**文件**: `src/test/java/com/czb/hn/util/KeywordUtilsLogicSimpleTest.java`

- 9 个测试用例
- 覆盖逻辑运算符功能
- 包含 AND/OR 逻辑和转义字符测试

### 2. 集成测试

**文件**: `src/test/java/com/czb/hn/service/business/PlanServiceKeywordTest.java`

- 8 个测试用例
- 测试 PlanService 逻辑关键词处理功能
- 使用 Mockito 模拟依赖

## 使用示例

### 1. 创建方案时的关键词验证（支持逻辑运算符）

```java
PlanCreateDTO createDTO = new PlanCreateDTO(
    "测试方案",
    "描述",
    "Java+Spring|Python|C@@语言+框架", // 支持AND(+)和OR(|)逻辑，@@转义+
    "测试|Demo|调试", // 排除关键词使用|分割
    "enterprise123"
);
```

### 1.1 逻辑关键词解析示例

```java
// 监控关键词：(Java AND Spring) OR Python OR (C+语言 AND 框架)
String monitorKeywords = "Java+Spring|Python|C@@语言+框架";
KeywordUtils.MonitorKeywordGroup monitorGroup = KeywordUtils.parseMonitorKeywords(monitorKeywords);

// 获取AND组合
List<List<String>> andGroups = monitorGroup.getAndGroups();
// andGroups[0] = ["Java", "Spring"]
// andGroups[1] = ["C+语言", "框架"]

// 获取OR关键词
List<String> orKeywords = monitorGroup.getOrKeywords();
// orKeywords = ["Python"]

// 排除关键词：测试 OR Demo OR 调试
String excludeKeywords = "测试|Demo|调试";
KeywordUtils.ExcludeKeywordGroup excludeGroup = KeywordUtils.parseExcludeKeywords(excludeKeywords);
List<String> excludeList = excludeGroup.getKeywords();
// excludeList = ["测试", "Demo", "调试"]
```

### 2. 获取方案关键词列表

```java
List<String> monitorKeywords = planService.getMonitorKeywordsList(planId);
List<String> excludeKeywords = planService.getExcludeKeywordsList(planId);
```

### 3. 基于方案执行搜索

```java
List<SinaNewsSearchResponseDto> results = planBasedSearchService.searchByPlan(
    planId, displayRule, mediaTypes, sensitivityType,
    startTime, endTime, sortRule, pageSize, pageNum
);
```

## API 文档

### 新增 REST API

#### 获取监控关键词

```
GET /plans/{id}/keywords/monitor
Response: ["关键词1", "关键词2", "关键词3"]
```

#### 获取排除关键词

```
GET /plans/{id}/keywords/exclude
Response: ["排除词1", "排除词2"]
```

#### 获取关键词信息

```
GET /plans/{id}/keywords/info
Response: {
  "planId": 1,
  "planName": "测试方案",
  "monitorKeywordsList": ["关键词1", "关键词2"],
  "excludeKeywordsList": ["排除词1"],
  "monitorKeywordsString": "关键词1, 关键词2",
  "excludeKeywordsString": "排除词1"
}
```

#### 获取逻辑关键词解析结果

```
GET /plans/{id}/keywords/logic
Response: {
  "monitorKeywords": {
    "andGroups": [["Java", "Spring"]],
    "orKeywords": ["Python"],
    "allKeywords": ["Java", "Spring", "Python"]
  },
  "excludeKeywords": {
    "keywords": ["测试", "Demo"]
  }
}
```

#### 获取监控关键词逻辑结构

```
GET /plans/{id}/keywords/monitor/logic
Response: {
  "andGroups": [["Java", "Spring"], ["C+语言", "框架"]],
  "orKeywords": ["Python"],
  "allKeywords": ["Java", "Spring", "Python", "C+语言", "框架"]
}
```

#### 获取排除关键词逻辑结构

```
GET /plans/{id}/keywords/exclude/logic
Response: {
  "keywords": ["测试", "Demo", "调试"]
}
```

## 技术特性

### 1. 向后兼容

- 保持现有 API 接口不变
- 现有数据库数据无需迁移
- 渐进式增强，不影响现有功能

### 2. 性能优化

- 关键词解析使用 Stream API
- 正则表达式编译为静态常量
- 避免重复验证和处理

### 3. 错误处理

- 详细的验证错误信息
- 优雅的异常处理
- 用户友好的错误提示

### 4. 扩展性

- 工具类设计便于扩展
- 支持未来的复杂关键词逻辑
- 模块化设计，易于维护

## 部署说明

1. **编译**: `mvn clean compile`
2. **测试**: `mvn test`
3. **打包**: `mvn package`

所有测试通过，无需额外配置或数据库迁移。

## 后续优化建议

1. **缓存优化**: 对频繁访问的关键词列表添加缓存
2. **批量处理**: 支持批量关键词操作
3. **关键词分组**: 支持 AND/OR 逻辑的复杂关键词组合
4. **关键词推荐**: 基于历史数据的关键词推荐功能
5. **性能监控**: 添加关键词处理性能监控

## 总结

本次实现完全解决了舆情方案关键词处理的核心问题，提供了：

- 完整的关键词验证和清理机制
- Plan 实体与搜索服务的无缝集成
- 丰富的 API 接口支持
- 全面的测试覆盖
- 良好的扩展性和维护性

该实现为 SecuRadar 系统的关键词处理奠定了坚实的基础，支持未来的功能扩展和性能优化。
