# 预警通知系统架构设计

## 设计原则

### 1. 关注点分离
- **预警生成**：实时检测，立即生成预警记录
- **预警通知**：基于接收设置，定时批量处理

### 2. 解耦设计
- 预警生成不依赖通知设置
- 通知处理独立于预警生成
- 支持灵活的通知策略

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   实时预警生成   │    │   预警通知调度   │    │   推送执行引擎   │
│                │    │                │    │                │
│ • 规则引擎      │    │ • 接收设置解析   │    │ • 邮件发送      │
│ • 实时检测      │    │ • 时间窗口控制   │    │ • 短信发送      │
│ • 预警存储      │    │ • 批量处理      │    │ • 系统通知      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  alert_results  │    │notification_queue│    │alert_push_details│
│                │    │                │    │                │
│ • 预警记录      │    │ • 待发送队列    │    │ • 推送记录      │
│ • 实时生成      │    │ • 调度状态      │    │ • 发送状态      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件

### 1. 预警生成服务 (AlertProcessingService)
```java
// 保持现有逻辑，专注于预警生成
public class AlertProcessingServiceImpl {
    // 实时检测和生成预警
    // 不涉及通知逻辑
}
```

### 2. 通知调度服务 (AlertNotificationScheduler)
```java
@Component
public class AlertNotificationScheduler {
    
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void processNotifications() {
        // 1. 获取所有活跃的接收设置
        // 2. 根据时间窗口和间隔规则筛选需要发送的预警
        // 3. 生成通知队列记录
    }
}
```

### 3. 接收规则引擎 (ReceptionRulesEngine)
```java
public class ReceptionRulesEngine {
    
    public boolean shouldSendNotification(
        AlertResult alert, 
        ReceptionSettingsDto settings) {
        
        // 检查接收时间 (DAILY/WORKDAYS/HOLIDAYS)
        // 检查接收时段 (start/end time)
        // 检查预警间隔 (最小30分钟)
        // 检查信息补推设置
        // 检查无预警通知设置
    }
}
```

### 4. 通知队列表
```sql
CREATE TABLE alert_notification_queue (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    alert_id BIGINT NOT NULL,
    configuration_id BIGINT NOT NULL,
    enterprise_id VARCHAR(255) NOT NULL,
    scheduled_time TIMESTAMP NOT NULL,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'),
    recipients JSON NOT NULL, -- 接收人信息
    notification_type ENUM('ALERT', 'INFO_PUSH', 'NO_ALERT'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    INDEX idx_scheduled_status (scheduled_time, status),
    INDEX idx_enterprise_scheduled (enterprise_id, scheduled_time)
);
```

## 处理流程

### 1. 预警生成流程
```
数据源 → 规则引擎 → 预警记录 → alert_results表
```

### 2. 通知调度流程
```
定时任务 → 检查接收设置 → 筛选预警 → 生成通知队列 → notification_queue表
```

### 3. 通知发送流程
```
通知队列 → 推送引擎 → 实际发送 → 记录推送详情 → alert_push_details表
```

## 接收设置处理逻辑

### 1. 时间控制
- **接收时间**: DAILY(全天), WORKDAYS(工作日), HOLIDAYS(节假日)
- **接收时段**: 指定的开始和结束时间
- **预警间隔**: 最小30分钟，避免频繁推送

### 2. 内容控制
- **信息补推**: 非预警时段是否推送信息
- **无预警通知**: 无预警时是否发送通知

### 3. 接收方式
- **邮件**: 根据邮件接收人列表
- **短信**: 根据短信接收人列表
- **系统**: 系统内通知

## 优势

1. **解耦**: 预警生成和通知发送完全独立
2. **灵活**: 支持复杂的接收规则和时间控制
3. **可靠**: 队列机制确保通知不丢失
4. **可扩展**: 易于添加新的通知类型和规则
5. **性能**: 预警生成不受通知处理影响

## 实现步骤

1. 创建通知队列表和相关实体
2. 实现接收规则引擎
3. 创建通知调度服务
4. 修改推送服务以处理队列
5. 添加监控和管理接口
