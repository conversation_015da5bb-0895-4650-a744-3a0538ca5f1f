# 多租户告警处理系统文档

## 系统概述

多租户告警处理系统是基于MySQL的告警结果存储和处理系统，提供完整的告警生成、存储、搜索和管理功能。系统设计支持多租户隔离，具备高性能搜索能力和完善的API接口。

## 系统架构

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
├─────────────────────────────────────────────────────────────┤
│  AlertResultController  │  AlertIntegrationController      │
│  SecureAlertController  │  AlertTaskMonitoringController   │
├─────────────────────────────────────────────────────────────┤
│                  Service Layer                              │
├─────────────────────────────────────────────────────────────┤
│  AlertProcessingService │  AlertSearchService              │
│  AlertRuleEngine        │  AlertSystemIntegrationService   │
│  AlertTenantSecurity    │  AlertTaskMonitoringService      │
├─────────────────────────────────────────────────────────────┤
│                Repository Layer                             │
├─────────────────────────────────────────────────────────────┤
│  AlertResultRepository  │  AlertConfiguration (existing)   │
├─────────────────────────────────────────────────────────────┤
│                  Data Layer                                 │
├─────────────────────────────────────────────────────────────┤
│  MySQL Database         │  Elasticsearch (future)          │
└─────────────────────────────────────────────────────────────┘
```

### 数据流程

1. **告警生成流程**
   ```
   定时任务 → 获取配置 → 规则引擎评估 → 生成告警结果 → 存储到MySQL
   ```

2. **告警搜索流程**
   ```
   API请求 → 安全验证 → 搜索服务 → 数据库查询 → 返回结果
   ```

3. **租户隔离流程**
   ```
   用户请求 → 身份验证 → 企业ID验证 → 数据访问控制 → 结果过滤
   ```

## 核心功能

### 1. 告警规则引擎

**功能描述**: 基于JSON配置的灵活规则引擎，支持关键词匹配、排除规则和阈值设置。

**主要特性**:
- 支持多关键词AND/OR逻辑
- 排除关键词过滤
- 相似文档数量阈值
- 敏感度级别控制

**使用示例**:
```java
// 规则配置示例
{
  "keywords": ["紧急", "故障"],
  "excludeKeywords": ["测试", "演练"],
  "minSimilarCount": 3,
  "sensitivityLevel": "HIGH"
}
```

### 2. 告警处理服务

**功能描述**: 定时处理告警配置，生成告警结果并存储。

**主要特性**:
- 支持企业级、方案级、配置级处理
- 异步处理提高性能
- 任务监控和统计
- 错误处理和重试机制

**API接口**:
```http
POST /api/alert-monitoring/processing/enterprise/{enterpriseId}
POST /api/alert-monitoring/processing/plan/{planId}
POST /api/alert-monitoring/processing/configuration/{configurationId}
```

### 3. 告警搜索服务

**功能描述**: 提供多维度搜索和过滤功能。

**主要特性**:
- 快速搜索（企业+方案）
- 全文搜索（标题+内容）
- 复合条件搜索
- 分页和排序
- 统计分析

**搜索类型**:

1. **快速搜索**
   ```http
   GET /api/secure/alert-results/quick-search?planId=100&page=0&size=20
   ```

2. **全文搜索**
   ```http
   GET /api/secure/alert-results/fulltext-search?searchText=关键词&page=0&size=20
   ```

3. **复合搜索**
   ```http
   POST /api/secure/alert-results/search
   Content-Type: application/json
   
   {
     "enterpriseId": "enterprise-001",
     "planId": 100,
     "warningLevel": "SEVERE",
     "startTime": "2024-01-01 00:00:00",
     "endTime": "2024-01-31 23:59:59",
     "page": 0,
     "size": 20,
     "sortBy": "warningTime",
     "sortDirection": "desc"
   }
   ```

### 4. 租户安全控制

**功能描述**: 确保多租户数据隔离和访问控制。

**主要特性**:
- 基于用户企业ID的数据隔离
- 角色权限控制（管理员、操作员、只读）
- API级别的安全验证
- 审计日志记录

**安全控制器基类**:
```java
public abstract class SecureAlertControllerBase {
    protected <T> ResponseEntity<ApiResponse<T>> executeWithEnterpriseAccess(
        String enterpriseId, Supplier<T> operation, String operationName);
}
```

## 数据库设计

### 核心表结构

#### alert_results 表
```sql
CREATE TABLE alert_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    configuration_id BIGINT NOT NULL,
    plan_id BIGINT,
    enterprise_id VARCHAR(100) NOT NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    involved_keywords JSON,
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_forwarded BOOLEAN DEFAULT FALSE,
    warning_level ENUM('GENERAL', 'MODERATE', 'SEVERE') DEFAULT 'GENERAL',
    source VARCHAR(100),
    warning_time DATETIME NOT NULL,
    similar_count BIGINT DEFAULT 0,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    
    INDEX idx_enterprise_warning_time (enterprise_id, warning_time),
    INDEX idx_enterprise_plan (enterprise_id, plan_id),
    INDEX idx_configuration_warning_time (configuration_id, warning_time),
    INDEX idx_warning_level_time (warning_level, warning_time),
    INDEX idx_source_time (source, warning_time),
    FULLTEXT INDEX ft_title_content (title, content)
);
```

### 性能优化索引

1. **企业级查询优化**
   - `idx_enterprise_warning_time`: 支持企业级时间范围查询
   - `idx_enterprise_plan`: 支持企业+方案查询

2. **全文搜索优化**
   - `ft_title_content`: MySQL FULLTEXT索引支持中文搜索

3. **统计查询优化**
   - `idx_warning_level_time`: 支持告警级别统计
   - `idx_source_time`: 支持来源统计

## API文档

### 安全告警结果API

#### 1. 综合搜索
```http
POST /api/secure/alert-results/search
```

**请求体**:
```json
{
  "enterpriseId": "enterprise-001",
  "planId": 100,
  "configurationId": 1,
  "warningLevel": "SEVERE",
  "source": "微博",
  "isSensitive": false,
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-31 23:59:59",
  "titleKeyword": "关键词",
  "contentKeyword": "内容关键词",
  "page": 0,
  "size": 20,
  "sortBy": "warningTime",
  "sortDirection": "desc"
}
```

**响应**:
```json
{
  "code": "SUCCESS",
  "message": "搜索成功",
  "data": {
    "content": [
      {
        "id": 1,
        "configurationId": 1,
        "planId": 100,
        "enterpriseId": "enterprise-001",
        "title": "告警标题",
        "content": "告警内容",
        "involvedKeywords": ["关键词1", "关键词2"],
        "isSensitive": false,
        "isForwarded": false,
        "warningLevel": "SEVERE",
        "source": "微博",
        "warningTime": "2024-01-15 10:30:00",
        "similarCount": 5,
        "createdBy": "system"
      }
    ],
    "totalElements": 100,
    "totalPages": 5,
    "currentPage": 0,
    "pageSize": 20,
    "statistics": {
      "totalCount": 100,
      "levelDistribution": {
        "一般": 60,
        "中等": 30,
        "严重": 10
      },
      "sourceDistribution": {
        "微博": 70,
        "微信": 30
      },
      "sensitiveCount": 20,
      "forwardedCount": 15,
      "avgSimilarCount": 3.5
    }
  }
}
```

#### 2. 获取统计信息
```http
GET /api/secure/alert-results/statistics?startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59
```

#### 3. 获取最近告警
```http
GET /api/secure/alert-results/recent?limit=10
```

### 告警任务监控API

#### 1. 获取系统监控概览
```http
GET /api/alert-monitoring/overview
```

#### 2. 手动触发处理
```http
POST /api/alert-monitoring/processing/enterprise/{enterpriseId}
```

#### 3. 获取任务健康状况
```http
GET /api/alert-monitoring/tasks/health
```

## 配置说明

### 应用配置
```yaml
# 告警处理配置
alert:
  processing:
    cron: "0 */5 * * * *"  # 每5分钟执行
    enabled: true
    batch-size: 1000
    
  search:
    default-page-size: 20
    max-page-size: 100
    
  cache:
    search:
      default-ttl: 300      # 5分钟
      statistics-ttl: 600   # 10分钟
      configuration-ttl: 1800  # 30分钟

# 数据库配置
spring:
  datasource:
    url: ******************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
```

### 安全配置
```yaml
# 安全配置
security:
  tenant-isolation:
    enabled: true
    strict-mode: true
    
  audit:
    enabled: true
    log-level: INFO
```

## 性能指标

### 基准性能
- **快速搜索**: < 500ms
- **全文搜索**: < 1000ms
- **复合搜索**: < 2000ms
- **统计查询**: < 1000ms
- **并发用户**: 支持50+并发用户

### 数据容量
- **单表记录**: 支持千万级记录
- **企业数据**: 支持数万企业
- **搜索响应**: 99%请求在2秒内响应

### 资源使用
- **内存使用**: 正常运行< 2GB
- **CPU使用**: 平均< 30%
- **数据库连接**: 支持100+并发连接

## 监控和运维

### 关键监控指标
1. **业务指标**
   - 告警生成速率
   - 搜索请求量
   - 用户活跃度

2. **性能指标**
   - API响应时间
   - 数据库查询时间
   - 缓存命中率

3. **系统指标**
   - 内存使用率
   - CPU使用率
   - 磁盘空间

### 日志配置
```yaml
logging:
  level:
    com.czb.hn.service.business: INFO
    com.czb.hn.service.security: WARN
    com.czb.hn.repository: DEBUG
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{enterpriseId}] %logger{36} - %msg%n"
```

### 健康检查
```http
GET /api/alert-integration/health
GET /actuator/health
GET /actuator/metrics
```

## 故障排查

### 常见问题

1. **搜索性能慢**
   - 检查数据库索引
   - 优化查询条件
   - 增加缓存配置

2. **告警生成失败**
   - 检查配置有效性
   - 验证数据源连接
   - 查看错误日志

3. **租户数据泄露**
   - 验证安全配置
   - 检查API权限
   - 审查访问日志

### 调试工具
```bash
# 查看数据库性能
SHOW PROCESSLIST;
EXPLAIN SELECT * FROM alert_results WHERE enterprise_id = 'xxx';

# 查看应用日志
tail -f logs/application.log | grep "enterprise-001"

# 监控API性能
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:8080/api/secure/alert-results/quick-search
```

## 未来规划

### Elasticsearch迁移
系统设计支持未来迁移到Elasticsearch，提供更强大的搜索能力：

1. **迁移策略**
   - 双写模式（MySQL + Elasticsearch）
   - 数据同步验证
   - 逐步切换读取

2. **预期收益**
   - 更快的全文搜索
   - 更复杂的聚合查询
   - 更好的扩展性

3. **迁移准备**
   - 保持API接口不变
   - 抽象数据访问层
   - 完善测试覆盖

### 功能扩展
1. **实时告警推送**
2. **告警规则可视化编辑**
3. **智能告警去重**
4. **多语言支持**
5. **移动端适配**

## 总结

多租户告警处理系统提供了完整的告警生命周期管理，从规则配置到结果搜索，具备良好的性能和安全性。系统架构灵活，支持未来的功能扩展和技术升级。
