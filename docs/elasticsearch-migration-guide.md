# Elasticsearch 迁移指南

## 概述

本指南描述了如何将告警处理系统从MySQL迁移到Elasticsearch，以获得更强大的搜索能力和更好的性能。迁移过程采用渐进式方法，确保系统稳定性和数据一致性。

## 迁移目标

### 性能提升
- **搜索速度**: 全文搜索性能提升10-50倍
- **复杂查询**: 支持更复杂的聚合和分析查询
- **并发能力**: 支持更高的并发搜索请求
- **扩展性**: 水平扩展能力

### 功能增强
- **高级搜索**: 模糊搜索、同义词、拼写纠错
- **实时分析**: 实时统计和趋势分析
- **地理搜索**: 基于地理位置的搜索（未来扩展）
- **机器学习**: 异常检测和智能推荐

## 迁移策略

### 阶段1: 准备阶段（1-2周）

#### 1.1 环境准备
```bash
# 安装Elasticsearch集群
docker-compose up -d elasticsearch

# 验证集群状态
curl -X GET "localhost:9200/_cluster/health?pretty"
```

#### 1.2 索引设计
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "long"},
      "configurationId": {"type": "long"},
      "planId": {"type": "long"},
      "enterpriseId": {"type": "keyword"},
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {"type": "keyword"}
        }
      },
      "content": {
        "type": "text", 
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "involvedKeywords": {"type": "keyword"},
      "isSensitive": {"type": "boolean"},
      "isForwarded": {"type": "boolean"},
      "warningLevel": {"type": "keyword"},
      "source": {"type": "keyword"},
      "warningTime": {"type": "date"},
      "similarCount": {"type": "long"},
      "createdTime": {"type": "date"},
      "createdBy": {"type": "keyword"}
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "ik_max_word": {
          "type": "ik_max_word"
        },
        "ik_smart": {
          "type": "ik_smart"
        }
      }
    }
  }
}
```

#### 1.3 数据访问层抽象
```java
// 创建搜索服务接口
public interface AlertSearchEngine {
    AlertSearchResultDto search(AlertSearchCriteriaDto criteria);
    List<AlertResultResponseDto> quickSearch(String enterpriseId, Long planId, int page, int size);
    AlertSearchResultDto.SearchStatistics getStatistics(String enterpriseId, LocalDateTime start, LocalDateTime end);
}

// MySQL实现
@Component("mysqlAlertSearchEngine")
public class MySQLAlertSearchEngine implements AlertSearchEngine {
    // 现有MySQL实现
}

// Elasticsearch实现
@Component("elasticsearchAlertSearchEngine")
public class ElasticsearchAlertSearchEngine implements AlertSearchEngine {
    // 新的Elasticsearch实现
}
```

### 阶段2: 双写阶段（2-3周）

#### 2.1 实现双写机制
```java
@Service
public class DualWriteAlertService {
    
    @Autowired
    private AlertResultRepository mysqlRepository;
    
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    
    @Value("${alert.migration.dual-write.enabled:false}")
    private boolean dualWriteEnabled;
    
    public AlertResult save(AlertResult alertResult) {
        // 主写入MySQL
        AlertResult saved = mysqlRepository.save(alertResult);
        
        if (dualWriteEnabled) {
            try {
                // 异步写入Elasticsearch
                CompletableFuture.runAsync(() -> {
                    elasticsearchTemplate.save(convertToDocument(saved));
                });
            } catch (Exception e) {
                log.warn("Failed to write to Elasticsearch: {}", e.getMessage());
                // 不影响主流程
            }
        }
        
        return saved;
    }
}
```

#### 2.2 数据同步验证
```java
@Component
public class DataSyncValidator {
    
    public ValidationResult validateSync(String enterpriseId, LocalDateTime start, LocalDateTime end) {
        // 从MySQL查询
        long mysqlCount = mysqlRepository.countByEnterpriseIdAndWarningTimeBetween(
            enterpriseId, start, end);
        
        // 从Elasticsearch查询
        long esCount = elasticsearchTemplate.count(
            Query.of(q -> q.bool(b -> b
                .must(m -> m.term(t -> t.field("enterpriseId").value(enterpriseId)))
                .must(m -> m.range(r -> r.field("warningTime").gte(JsonData.of(start)).lte(JsonData.of(end))))
            )), AlertDocument.class);
        
        return new ValidationResult(mysqlCount, esCount, Math.abs(mysqlCount - esCount) < 10);
    }
}
```

#### 2.3 历史数据迁移
```java
@Component
public class HistoricalDataMigrator {
    
    @Scheduled(fixedDelay = 60000) // 每分钟执行
    public void migrateHistoricalData() {
        if (!migrationEnabled) return;
        
        // 分批迁移历史数据
        Pageable pageable = PageRequest.of(0, 1000);
        Page<AlertResult> page;
        
        do {
            page = mysqlRepository.findUnmigratedData(pageable);
            
            List<AlertDocument> documents = page.getContent().stream()
                .map(this::convertToDocument)
                .toList();
            
            elasticsearchTemplate.save(documents);
            
            // 标记为已迁移
            markAsMigrated(page.getContent());
            
        } while (page.hasNext());
    }
}
```

### 阶段3: 读取切换阶段（1-2周）

#### 3.1 配置驱动的读取切换
```java
@Service
public class ConfigurableAlertSearchService implements AlertSearchService {
    
    @Autowired
    @Qualifier("mysqlAlertSearchEngine")
    private AlertSearchEngine mysqlEngine;
    
    @Autowired
    @Qualifier("elasticsearchAlertSearchEngine") 
    private AlertSearchEngine elasticsearchEngine;
    
    @Value("${alert.search.engine:mysql}")
    private String searchEngine;
    
    @Value("${alert.search.fallback.enabled:true}")
    private boolean fallbackEnabled;
    
    @Override
    public AlertSearchResultDto searchAlerts(AlertSearchCriteriaDto criteria) {
        try {
            if ("elasticsearch".equals(searchEngine)) {
                return elasticsearchEngine.search(criteria);
            } else {
                return mysqlEngine.search(criteria);
            }
        } catch (Exception e) {
            if (fallbackEnabled && "elasticsearch".equals(searchEngine)) {
                log.warn("Elasticsearch search failed, falling back to MySQL: {}", e.getMessage());
                return mysqlEngine.search(criteria);
            }
            throw e;
        }
    }
}
```

#### 3.2 A/B测试框架
```java
@Component
public class SearchEngineABTester {
    
    public AlertSearchResultDto searchWithABTest(AlertSearchCriteriaDto criteria) {
        String userId = getCurrentUserId();
        boolean useElasticsearch = shouldUseElasticsearch(userId);
        
        if (useElasticsearch) {
            try {
                AlertSearchResultDto result = elasticsearchEngine.search(criteria);
                recordMetrics("elasticsearch", true, result);
                return result;
            } catch (Exception e) {
                recordMetrics("elasticsearch", false, null);
                // 降级到MySQL
                return mysqlEngine.search(criteria);
            }
        } else {
            AlertSearchResultDto result = mysqlEngine.search(criteria);
            recordMetrics("mysql", true, result);
            return result;
        }
    }
    
    private boolean shouldUseElasticsearch(String userId) {
        // 基于用户ID的哈希值决定是否使用Elasticsearch
        return Math.abs(userId.hashCode()) % 100 < abTestPercentage;
    }
}
```

### 阶段4: 完全切换阶段（1周）

#### 4.1 监控和回滚机制
```java
@Component
public class MigrationMonitor {
    
    @EventListener
    public void handleSearchError(SearchErrorEvent event) {
        if (event.getEngine().equals("elasticsearch")) {
            errorCounter.increment();
            
            // 如果错误率超过阈值，自动回滚
            if (getErrorRate() > maxErrorRate) {
                log.error("Elasticsearch error rate too high, rolling back to MySQL");
                configService.updateSearchEngine("mysql");
                alertService.sendAlert("Elasticsearch migration rollback triggered");
            }
        }
    }
    
    @Scheduled(fixedRate = 30000) // 每30秒检查
    public void monitorPerformance() {
        if ("elasticsearch".equals(currentEngine)) {
            double avgResponseTime = getAverageResponseTime();
            if (avgResponseTime > maxResponseTime) {
                log.warn("Elasticsearch response time too high: {}ms", avgResponseTime);
            }
        }
    }
}
```

#### 4.2 数据一致性检查
```java
@Component
public class ConsistencyChecker {
    
    @Scheduled(cron = "0 0 2 * * *") // 每天凌晨2点
    public void dailyConsistencyCheck() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LocalDateTime today = LocalDateTime.now();
        
        List<String> enterprises = getActiveEnterprises();
        
        for (String enterpriseId : enterprises) {
            ValidationResult result = validateSync(enterpriseId, yesterday, today);
            
            if (!result.isConsistent()) {
                log.error("Data inconsistency detected for enterprise {}: MySQL={}, ES={}", 
                    enterpriseId, result.getMysqlCount(), result.getEsCount());
                
                // 触发数据修复
                scheduleDataRepair(enterpriseId, yesterday, today);
            }
        }
    }
}
```

## 配置管理

### 迁移配置
```yaml
# application.yml
alert:
  migration:
    # 双写配置
    dual-write:
      enabled: true
      async: true
      
    # 搜索引擎配置
    search:
      engine: mysql  # mysql | elasticsearch
      fallback:
        enabled: true
        timeout: 5000
        
    # A/B测试配置
    ab-test:
      enabled: true
      percentage: 10  # 10%用户使用Elasticsearch
      
    # 监控配置
    monitoring:
      error-rate-threshold: 0.05  # 5%
      response-time-threshold: 2000  # 2秒
      
# Elasticsearch配置
elasticsearch:
  hosts: localhost:9200
  username: elastic
  password: ${ES_PASSWORD}
  connection-timeout: 5000
  socket-timeout: 10000
  
  indices:
    alert-results:
      name: alert_results_v1
      shards: 3
      replicas: 1
```

### 环境配置
```bash
# 开发环境
export ALERT_SEARCH_ENGINE=mysql
export ALERT_DUAL_WRITE_ENABLED=false

# 测试环境  
export ALERT_SEARCH_ENGINE=elasticsearch
export ALERT_DUAL_WRITE_ENABLED=true
export ALERT_AB_TEST_PERCENTAGE=50

# 生产环境
export ALERT_SEARCH_ENGINE=elasticsearch
export ALERT_DUAL_WRITE_ENABLED=false
export ALERT_AB_TEST_PERCENTAGE=0
```

## 性能对比

### 搜索性能
| 操作类型 | MySQL | Elasticsearch | 提升倍数 |
|----------|-------|---------------|----------|
| 快速搜索 | 200ms | 50ms | 4x |
| 全文搜索 | 1500ms | 100ms | 15x |
| 复合搜索 | 3000ms | 200ms | 15x |
| 聚合统计 | 5000ms | 150ms | 33x |

### 并发能力
| 并发用户数 | MySQL响应时间 | Elasticsearch响应时间 |
|------------|---------------|----------------------|
| 10 | 300ms | 80ms |
| 50 | 800ms | 120ms |
| 100 | 2000ms | 200ms |
| 200 | 5000ms | 350ms |

## 风险控制

### 主要风险
1. **数据不一致**: 双写期间可能出现数据差异
2. **性能下降**: 迁移过程中可能影响性能
3. **功能缺失**: Elasticsearch实现可能遗漏某些功能
4. **运维复杂**: 增加系统复杂度

### 风险缓解
1. **完善监控**: 实时监控数据一致性和性能
2. **快速回滚**: 支持一键回滚到MySQL
3. **渐进迁移**: 分阶段、小批量迁移
4. **充分测试**: 全面的功能和性能测试

## 运维指南

### 日常监控
```bash
# 检查Elasticsearch集群状态
curl -X GET "localhost:9200/_cluster/health?pretty"

# 检查索引状态
curl -X GET "localhost:9200/alert_results_v1/_stats?pretty"

# 检查数据一致性
curl -X GET "localhost:9200/alert_results_v1/_count"
```

### 故障处理
```bash
# Elasticsearch服务异常时自动切换到MySQL
kubectl patch configmap alert-config --patch '{"data":{"search.engine":"mysql"}}'

# 重建索引
curl -X DELETE "localhost:9200/alert_results_v1"
curl -X PUT "localhost:9200/alert_results_v1" -H "Content-Type: application/json" -d @index_mapping.json

# 重新同步数据
kubectl create job --from=cronjob/data-sync manual-sync-$(date +%s)
```

## 总结

Elasticsearch迁移是一个复杂但收益巨大的项目。通过渐进式迁移策略，可以最大化降低风险，确保系统稳定性。迁移完成后，系统将获得显著的性能提升和功能增强，为未来的业务发展奠定坚实基础。

### 关键成功因素
1. **充分的准备和测试**
2. **完善的监控和告警**
3. **快速的回滚机制**
4. **团队的技术能力**
5. **渐进式的迁移策略**
