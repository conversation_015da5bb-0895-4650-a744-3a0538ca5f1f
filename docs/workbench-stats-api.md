# 工作台统计信息API文档

## 接口概述

工作台统计信息接口提供用户登录统计、企业订阅信息和方案数量的综合查询功能。

## 接口详情

### 获取工作台统计信息

**接口地址：** `GET /workbench/stats`

**接口描述：** 获取当前用户的上次登录时间、近7天登录次数、企业账号到期时间和方案数量

**请求参数：** 无（通过用户上下文自动获取用户信息）

**响应格式：**

```json
{
    "code": "SUCCESS",
    "message": "操作成功",
    "data": {
        "lastLoginTime": "2024-01-15 14:30:00",
        "sevenDayLoginCount": 5,
        "enterpriseExpirationDate": "2024-12-31",
        "planCount": 3
    }
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| lastLoginTime | string | 用户上次登录时间，格式：yyyy-MM-dd HH:mm:ss | "2024-01-15 14:30:00" |
| sevenDayLoginCount | number | 近7天登录次数 | 5 |
| enterpriseExpirationDate | string | 企业账号到期时间，格式：yyyy-MM-dd | "2024-12-31" |
| planCount | number | 企业方案数量 | 3 |

**响应状态码：**

| 状态码 | 说明 | 响应示例 |
|--------|------|----------|
| 200 | 获取成功 | 见上方响应格式 |
| 401 | 用户未登录或企业信息未找到 | `{"code": "ERROR", "message": "用户未登录", "data": null}` |
| 500 | 服务器内部错误 | `{"code": "ERROR", "message": "获取工作台统计信息失败: ...", "data": null}` |

## 功能特性

### 1. 用户登录统计
- **上次登录时间：** 从用户登录记录表中获取最近一次登录时间
- **7天登录次数：** 统计近7天内的登录次数，用于分析用户活跃度

### 2. 企业订阅信息
- **账号到期时间：** 获取企业当前订阅的到期日期
- **支持多种企业标识：** 支持通过企业ID或企业信用代码查询订阅信息

### 3. 方案统计
- **方案数量：** 统计当前企业下的监控方案总数
- **企业隔离：** 只统计当前企业可见的方案

### 4. 安全性
- **用户认证：** 必须通过用户认证才能访问
- **企业隔离：** 只能查看当前用户所属企业的信息
- **异常处理：** 完善的异常处理机制，确保接口稳定性

## 数据来源

1. **用户登录记录：** `user_login_records` 表
2. **企业订阅信息：** `enterprise_subscriptions` 表
3. **方案信息：** `plans` 表

## 使用场景

1. **工作台首页：** 展示用户和企业的关键统计信息
2. **用户活跃度分析：** 通过登录统计了解用户使用频率
3. **订阅状态监控：** 及时了解企业账号到期情况
4. **方案管理概览：** 快速了解企业方案规模

## 注意事项

1. 接口依赖用户上下文，确保用户已正确登录
2. 企业订阅信息可能为空，前端需要处理null值情况
3. 登录统计基于30天数据保留策略，超过30天的记录会被清理
4. 方案数量实时统计，反映当前企业的实际方案数量

## 错误处理

接口实现了完善的错误处理机制：

- **用户未登录：** 返回401状态码，提示用户登录
- **企业信息缺失：** 返回401状态码，提示企业信息未找到
- **服务异常：** 返回500状态码，记录详细错误日志
- **数据异常：** 优雅处理空值和异常数据，确保接口稳定性
